const _ = require('lodash');
const client = require('../../../common/client');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const basketModel = require('../basket');
const paperUtils = require('../../../common/utils/paper_utils');
const questionModel = require('../question');
const { object } = require('underscore')

module.exports = {
    paperToPaper,
    knowledgePaper,
    recommendLevelPaper,
    paperByQuestions,
}

async function paperToPaper(params) {
    const {user_id, id, filtered_ques, province, preference} = params;
    const kb_exampaper = await client.kb.getPaperById(id);
    if (_.isEmpty(kb_exampaper)) throw new BussError('试卷不存在');
    const algo_params = {
        period: kb_exampaper.period,
        subject: kb_exampaper.subject,
        type: '细目表组卷',
        // school_id: user.schoolId,
        user_id: user_id.toString(),
        paper_id: kb_exampaper.id.toString(),
        grade: kb_exampaper.grade,
        blocks: [],
        filtered_ques: filtered_ques || [],
        province: province,
        preference: preference || 0, // 选题偏好：0:默认无偏好，1：优先使用组卷最多的试题，2:优先使用最近录入的试题，3:优先使用本地试题
    }
    for (const b of kb_exampaper.blocks) {
        const block = {
            type: b.type,
            questions: []
        };
        for (const q of b.questions || []) {
            algo_params.filtered_ques.push(q.id);
            if (!_.size(q.knowledges)) continue;
            const knowledges = q.knowledges.map(k => {
                return {
                    id: k.id,
                    name: k.name
                }
            });
            const question = {
                type: q.type,
                score: q.score,
                difficulty: enums.QuestionDifficultyName[q.difficulty],
                knowledges: knowledges.slice(0, 3)
            }
            block.questions.push(question);
        }
        if (!_.size(block.questions)) continue;
        algo_params.blocks.push(block);
    }
    const algo_paper = await client.algo.detailTablePaper(algo_params);
    // 添加试题
    const question_ids = [];
    for (const b of algo_paper.blocks) {
        question_ids.push(...b.questions.map(q => q.id));
    }
    // 获取试题
    const kb_questions = await client.kb.getQuestionByIds(question_ids);
    // 清空试题篮
    await basketModel.delete_basket({user_id, period: kb_exampaper.period, subject: kb_exampaper.subject})
    // 写入试题篮
    const questions = [];
    for (const id of question_ids) {
        const q = kb_questions.find(e=> e.id === id);
        if (!q) continue;
        questions.push({
            id: q.id,
            type: q.type,
            period: kb_exampaper.period,
            subject: kb_exampaper.subject
        });
    }
    return await basketModel.post_questions({ period: kb_exampaper.period, subject: kb_exampaper.subject, questions, user_id });
}

async function knowledgePaper(params) {
    const {user_id, knowledges_ids, difficulty, period, subject, blocks} = params;
    const algoDiffMap = {
        '容易': '容易',
        '一般': '中等',
        '普通': '中等',
        '困难': '困难',
        '不限': '不限',
    };
    const knowledges  = await client.kb.getKnowledgeByIds(knowledges_ids);
    let algo_params = {
        period: period,
        subject: subject,
        difficulty: algoDiffMap[difficulty] || '中等',
        knowledges: knowledges,
        ques_num: blocks,
        // province: req.body.province,
        // city: req.body.city
    };

    const algo_paper = await client.algo.knowledgesPaper(algo_params);
    if (_.isEmpty(algo_paper)) throw new BussError('组卷失败');
    // 添加试题
    const question_ids = [];
    for (const b of algo_paper.blocks) {
        question_ids.push(...b.questions.map(q => q.id));
    }
    // 获取试题
    const kb_questions = await client.kb.getQuestionByIds(question_ids);
    // 清空试题篮
    await basketModel.delete_basket({user_id, period, subject})
    // 写入试题篮
    const questions = [];
    for (const id of question_ids) {
        const q = kb_questions.find(e=> e.id === id);
        if (!q) continue;
        questions.push({
            id: q.id,
            type: q.type,
            period: period,
            subject: subject
        });
    }
    return await basketModel.post_questions({ period: period, subject: subject, questions, user_id });
}
async function recommendLevelPaper(params, type) {
    // const {user_id, knowledges_ids, difficulty, period, subject, blocks} = params;
    // 特殊处理 基于资源推题使用原卷
    const date = new Date();
    if (type === 'by_paper' && !params.is_level) {
        const paperModel = require('../../models/paper');
        const basketModel = require('../../models/basket');
        let paper
        const PaperFromMapping = {
            kb: enums.PaperFrom.SYS,
            tk: enums.PaperFrom.GROUP,
            xb: enums.PaperFrom.JYZY,
        }
        if (params.paper_from === 'zyl') {
            paper = await basketModel.get_basket({
                user_id: params.user_id,
                period:  params.period,
                subject:  params.subject,
                category: enums.BasketCategory.HOMEWORK,
             })
        } else {
            paper = await paperModel.getDetail({
               id: params.paper_id,
               from: PaperFromMapping[params.paper_from]
            })
        }
        paper.template = 'homework'
        paper.partsList = ['name', 'cand_info', 'blocks']
        paper.ctime = date;
        paper.utime = date;
        paper.period = params.period;
        paper.subject = params.subject;
        paper.grade = params.grade;
        const paperMap = {
            foundation: {},
            intermediate: paper,
            advanced: {},
        }
        return paperMap
    }

    let algo_paper 
    if (type === 'by_common') {
        algo_paper = await client.algo.recommendLevelPaperByCommon(params);
    } else if (type === 'by_knowledge') {
        algo_paper = await client.algo.recommendLevelPaperByKnowledge(params);
    } else if (type === 'by_paper') {
        algo_paper = await client.algo.recommendLevelPaperByPaper(params);
    } else if (type === 'by_ques') {
        algo_paper = await client.algo.recommendLevelPaperByQues(params);
    } else if (type === 'by_know') {
        algo_paper = await client.algo.recommendLevelPaperByKnow(params);
    } else {
        throw new BussError('参数错误');
    }
    if (_.isEmpty(algo_paper)) throw new BussError('推题失败');
    // 添加试题

    const paperMap = {}
    for (const key in algo_paper) {
        const paper = paperUtils.init();
        if (_.isEmpty(algo_paper[key])) {
            paperMap[key] = {};
            continue;
        }
        // 默认使用作业模板
        paper.template = 'homework'
        paper.partsList = ['name', 'cand_info', 'blocks']
        paper.ctime = date;
        paper.utime = date;
        paper.period = params.period;
        paper.subject = params.subject;
        paper.grade = params.grade;
        // paper.type = '考后巩固';
        // paper.display = 1; // 默认展示
    
        const ids = [];
        const algoQuestionMap = {};
        for (const b of algo_paper[key].blocks) {
            for (const q of b.questions) {
                ids.push(q.id);
                algoQuestionMap[q.id] = q;
            }
        }
        const kb_questions = await client.kb.getQuestionByIds(ids);
        const ques_map = {};
        for (const q of kb_questions) {
            ques_map[q.id] = q;
            ques_map[q.id].from_type = algoQuestionMap[q.id].from_type
            ques_map[q.id].from_str = algoQuestionMap[q.id].from_str
            ques_map[q.id].from_id = algoQuestionMap[q.id].from_id
        }
        for (const q of kb_questions) {
            if (_.isEmpty(q)) continue;
            const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject', '']);
            paperUtils.insert_questions(paper, simple_question);
        }
        paperUtils.traverse_questions(paper, ques_map, ['from_str', 'from_type', 'from_id']);
        paperUtils.render_basket(paper);
        if (algo_paper[key].name) {
            paper.name = algo_paper[key].name;
        }
        paperMap[key] = paper;
    }
  
    return paperMap;
}

async function paperByQuestions(params) {
    const {period, subject, grade, questions} = params;

    const paper = paperUtils.init();
    let kb_ids = [];
    let zx_ids = [];
    const ques_map = {};
    for (const q of questions) {
        if (!q) continue;
        if (_.isNumber(q)) {
            kb_ids.push(q);
        } else {
            zx_ids.push(q);
        }
    }
    if (_.size(kb_ids)) {
        const list = await client.kb.getQuestionByIds(kb_ids);
        list.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const list = await questionModel.getByIds(zx_ids);
        list.forEach(q => ques_map[q.id] = q);
    }
     for (const q of Object.values(ques_map)) {
        if (_.isEmpty(q)) continue;
        const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject']);
        paperUtils.insert_questions(paper, simple_question);
    }
    const date = new Date();
    paper.ctime = date;
    paper.utime = date;
    paper.period = period;
    paper.subject = subject;
    paper.grade = grade;
    paperUtils.traverse_questions(paper, ques_map);
    paperUtils.render_basket(paper);
    paperUtils.ques_process(paper);
    return paper;
}