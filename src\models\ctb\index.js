const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const moment = require('moment');

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../../common/enums/template');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const subject_utils = require('../../../common/utils/subject_utils');
const question_utils = require('../../../common/utils/question_utils');
const exam_utils = require('../exam/exam_utils');
const logger = require('../../../common/lib/logger');

module.exports = {
    getExamList,
    getExamPaperClasses,
    getExamPaperQuestionDetail,
    questionSearch,
    putExamPaperQuestionSame,
    addCtbConfig,
    getCtbConfigList,
    getCtbConfigDetail,
    getCtbConfigStudents,
    getCtbContent,
    download,
    // getExamPaperQuestionSame,
    // getExamPaperQuestionAnswerImage,
    // putExamPaperQuestionAnswerTag,
    // getExamPaperStudentQuestionAnswer,
}

async function getExamList(params) {
    const { user_id, offset = 0, limit = 10, type, event_time_start, event_time_end, exam_name } = params;
    let list = await client.hfsTeacherV3.getTeacherExamList(user_id);
    const result = {
        total: 0,
        list: []
    };
    if (_.size(list)) {
        if (type) list = list.filter(e => type === 1 ? e.type === 12 : type !== 12);
        if (event_time_start) list = list.filter(e => e.event_time >= moment(event_time_start).startOf('day').valueOf());
        if (event_time_end) list = list.filter(e => e.event_time <= moment(event_time_end).endOf('day').valueOf());
        if (exam_name) list = list.filter(e => e.name.includes(exam_name));
    }
    if (!_.size(list)) return result;
    result.total = _.size(list);
    const pages = await _.chunk(list, limit);
    result.list = pages[parseInt(offset / limit)];
    // 增加试卷状态
    const status = await Promise.all(result.list.map(e => getPaperStatus(e.id, e.paper_id)));
    for (const i in result.list) {
        const data = result.list[i];
        const {published, original} = status[i];
        data.published = published;
        data.original = original;
        if (data.zujuanId) data.original = enums.BOOL.YES;
    }
    return result;
}

async function getExamPaperClasses(params) {
    const { user_id, exam_id, paper_id } = params;
    return await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
}


async function getExamPaperQuestionDetail(params) {
    const { user_id, exam_id, paper_id, zujuanId } = params;
    const rankExam = await client.rank.getExamById(exam_id);
    if (_.isEmpty(rankExam)) throw new BussError('考试不存在或者未同步!');
    if (zujuanId && rankExam.type !== 12) throw new BussError('非考试作业');
    const rankPaper = rankExam['[papers]'].find(e => e.id === paper_id);
    const result = {
        paper: {
            exam_id: exam_id,
            exam_name: `${rankExam.name} （${rankPaper.name})`,
            original: enums.BOOL.NO, // 是否上传原卷0：否，1：是
            published: enums.BOOL.NO, // 是否发布成绩0：否，1：是
            image_url: []
        },
        questions: [],
        class_info: []
    }

    const { period, grade, subject } = getGradeInfoByRankExamPaper(rankPaper);
    result.paper.period = period;
    result.paper.grade = grade;
    result.paper.subject = subject;
    // 获取元培信息
    const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
    const paper_status = await getPaperStatus(exam_id, paper_id, yp_paper);
    result.paper.original = paper_status.original;
    result.paper.published = paper_status.published;
    if (zujuanId) {
        result.paper.original = enums.BOOL.YES;
    }

    if (!_.isEmpty(yp_paper)) {
        result.paper.image_url = _.get(yp_paper, 'paper.image_url', []);
    }

    let exam = await getUserExam(user_id, exam_id, paper_id);
    if (_.isEmpty(exam)) {
        exam = await initExam(user_id, exam_id, paper_id, zujuanId || '', period, grade, subject);
    } else {
        const ds = {};
        if (result.paper.original && !exam.period) {
            ds.period = period;
            ds.grade = grade;
            ds.subject = subject;
            exam = _.assign(exam, ds);
        }
        if (zujuanId && !exam.user_paper_id) {
            ds.user_paper_id = zujuanId;
            exam.user_paper_id = zujuanId;
        }
        if (!_.isEmpty(ds)) {
            ds.utime = new Date();
            await db.collection(schema.user_exam_ctb).updateOne({_id: exam._id}, {$set: ds});
        }
    }
    const hfs_question_info = await client.hfsTeacherV3.getExamPaperQuestionDetail(user_id, exam_id, paper_id);
    if (hfs_question_info) {
        if (exam.user_paper_id) { // 用户组卷挂载原题信息
            const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
            const kb_questions = getTikuPaperQuestions(tiku_paper);
            if (_.size(kb_questions)) {
                hfs_question_info.questions.forEach((ques, i) => {
                    const kb_question = kb_questions[i];
                    ques.type = kb_question.type;
                    ques.origin_question = kb_question;
                })
            }
        } else {
            for (const ques of hfs_question_info.questions || []) {
                const yp_question = findYpQuestion(ques, yp_paper);
                if (yp_question) ques.type = _get_hfs_question_type(yp_paper.paper.subject, ques, yp_question);
            }
        }
        // 原卷发布, 添加变式练习
        if (result.paper.original) {
            if (!_.size(exam.questions)) {
                await buildPaperRecommendQuestions(exam, yp_paper);
            }
            const ids = [];
            for (const ques of exam.questions || []) {
                ids.push(...ques.same);
            }
            const kb_questions = await client.kb.getQuestionByIds(ids);
            for (const ques of hfs_question_info.questions || []) {
                const exam_ques = (exam.questions || []).find(e => e.id === ques.id);
                if (_.isEmpty(exam_ques) || !_.size(exam_ques.same)) continue;
                ques.same_questions = (kb_questions || []).filter(e => exam_ques.same.includes(e.id));
            }
        }
        try {
            if (result.paper.published) {
                const comparison_params = [];
                for (const cls of hfs_question_info.class_info || []) {
                    comparison_params.push({
                        user_id,
                        exam_id,
                        paper_id,
                        class_id: cls.id
                    });
                }
                const comparison_res = await Promise.all(comparison_params.map(e => client.hfsTeacherV3.getExamPaperClassComparison(e.user_id, e.exam_id, e.paper_id, e.class_id)));
                for (const i in hfs_question_info.class_info || []) {
                    const cls = hfs_question_info.class_info[i];
                    const comparison = comparison_res[i];
                    sortByRank(comparison, cls);
                }
            }
        } catch (e) {
            logger.error(e);
        }
    }
    _.assign(result, hfs_question_info);
    return result;
}


async function questionSearch(params) {
    const { user_id, exam_id, paper_id, offset, limit } = params;
    const exam = await getUserExam(user_id, exam_id, paper_id);
    const algo_params = {
        period: exam.period,
        subject: exam.subject,
        grade: exam.grade,
    };
    if (params.type) algo_params.ques_type = params.type;
    if (params.difficulty) algo_params.difficulty = params.difficulty;
    if (params.exam_type) algo_params.exam_type = params.exam_type;
    if (params.province) algo_params.province = params.province;
    if (params.knowledges) algo_params.knowledges = params.knowledges;
    if (params.question_id) {
        algo_params.ques_id = String(params.question_id);
        algo_params.ques_from = 'kb';
    }
    const algo_result = await client.algo.recommendQuestions(algo_params);
    const result = {
        total: 0,
        list: []
    };
    if (_.isEmpty(algo_result)) return result;
    result.total = algo_result.total;
    const ids = algo_result.questions.slice(offset, offset + limit).map(e => e.id);
    result.list = await client.kb.getQuestionByIds(ids);
    question_utils.addShowTags(result.list);
    // for (const question of result.list) {
    //     const blocks = question['blocks'];
    //     delete blocks['solutions'];
    //     delete blocks['answers'];
    //     delete blocks['explanations'];
    // }
    return result;
}

async function putExamPaperQuestionSame(params) {
    const {user_id, exam_id, paper_id, question_id, questions } =  params;
    const exam = await getUserExam(user_id, exam_id, paper_id);
    let result = [];
    const exam_questions = exam.questions || [];
    if (_.size(exam_questions)) {
        const question = exam_questions.find(e => e.id === question_id);
        if (!_.isEmpty(question)) {
            question.same = questions.map(e => e.id);
            await db.collection(schema.user_exam_ctb).updateOne({_id: exam._id}, {$set: {questions: exam_questions}});
            result = await client.kb.getQuestionByIds(question.same);
        }
    }
    return result;
}

async function addCtbConfig(params) {
    const { user_id, exam_id, paper_id, questions } = params;
    const exam = await getUserExam(user_id, exam_id, paper_id);
    if (_.isEmpty(exam)) throw new BussError('考试信息不存在');
    const rank_exam = await client.rank.getExamById(exam_id);
    const doc = {
        user_id,
        exam_id,
        exam_name: rank_exam.name,
        paper_id,
        student_config: params.student_config,
        class_config: params.class_config,
        questions: questions,
        ctime: new Date(),
        utime: new Date()
    }
    const result = await db.collection(schema.user_exam_ctb_config).insertOne(doc);
    return result.insertedId.toString();
}

async function getCtbConfigList(params) {
    const {user_id, offset, limit, exam_name, ctime_start, ctime_end} = params;

    const query = {
        user_id
    }
    if (exam_name) query.exam_name = {$regex: exam_name};
    if (ctime_start && ctime_end) {
        query.ctime = {
            $gte: moment(ctime_start).startOf('day').toDate(),
            $lte: moment(ctime_end).endOf('day').toDate()
        }
    }
    const result = {
        total: 0,
        list: []
    }
    result.total = await db.collection(schema.user_exam_ctb_config).find(query).count();
    if (!result.total) return result;
    const list = await db.collection(schema.user_exam_ctb_config).find(query).sort({ctime: -1}).skip(offset).limit(limit).toArray();
    for (const data of list) {
        data.id = data._id.toString();
        data.ctime = data.ctime.getTime();
        data.utime = data.utime.getTime();
        delete data._id;
        delete data.questions;
    }
    const set = new Set(list.map(e => e.exam_id));
    const exams = await client.rank.getExams([...set]);
    for (const data of list) {
        data.status = _.isEmpty(exams[+data.exam_id]) ? 0 : 1;
    }
    result.list = list;
    return result;
}

async function getCtbConfigDetail(params) {
    const {user_id, id} = params;
    const ctbConfig = await db.collection(schema.user_exam_ctb_config).findOne({_id: ObjectId(id)});
    if (_.isEmpty(ctbConfig) || ctbConfig.user_id !== user_id) throw new BussError('数据不存在');
    ctbConfig.id = ctbConfig._id.toString();
    ctbConfig.ctime = ctbConfig.ctime.getTime();
    ctbConfig.utime = ctbConfig.utime.getTime();
    delete ctbConfig._id;
    return ctbConfig;
}

async function getCtbConfigStudents(params) {
    const {config_id} = params;
    const ctbConfig = await db.collection(schema.user_exam_ctb_config).findOne({_id: ObjectId(config_id)});
    if (_.isEmpty(ctbConfig)) throw new BussError('导出配置不存在');
     // 获取班级列表
    const {user_id, exam_id, paper_id} = ctbConfig;
    const classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
    const result = [];
    if (_.size(classes)) {
        for (const cls of classes) {
            result.push({
                class_id: cls.id,
                class_name: cls.name,
                wrong_count: 0,
                same_count: 0,
                students: []
            });
        }
    }
    // 获取原题
    const hfs_question_info = await client.hfsTeacherV3.getExamPaperQuestionDetail(user_id, exam_id, paper_id);
    if (_.isEmpty(hfs_question_info)) {
        return result;
    }
    const quesMap = new Map();
    if (_.size(hfs_question_info.questions)) {
        for (const ques of hfs_question_info.questions) {
            quesMap.set(ques.id, ques);
        }
    }
    const selected_ids = ctbConfig.questions.map(e => e.id) || [];
    // 班级学生信息
    if (_.size(hfs_question_info.class_info)) {
        for (const cls of hfs_question_info.class_info) {
            const res_cls = result.find(e => e.class_id === cls.id);
            if (_.isEmpty(res_cls)) continue;
            // 班级学生
            const studentMap = new Map();
            const handlerStudent = (stu, qu) => {
                if (!studentMap.has(stu.id)) {
                    studentMap.set(stu.id, {
                        id: stu.id,
                        name: stu.name,
                        xuehao: stu.xuehao,
                        kaohao: stu.kaohao,
                        wrong_count: 0, // 错题数量
                        same_count: 0, // 相似题题数量
                        ids:  []
                    })
                }
                const student = studentMap.get(stu.id);
                student.wrong_count += 1;
                student.same_count = student.wrong_count * 2;
                if (!student.xuehao) student.xuehao = stu.xuehao || '';
                if (!student.kaohao) student.kaohao = stu.kaohao || '';
                student.ids.push(qu.id);
            }
            for (const cls_ques of cls.question_score) {
                if (!selected_ids.includes(cls_ques.id)) continue;
                const hfs_ques = quesMap.get(cls_ques.id);
                if (_.isEmpty(hfs_ques)) continue;
                if (cls_ques.score_rate < 1) { // 班级错题
                    res_cls.wrong_count += 1;
                    res_cls.same_count = res_cls.wrong_count * 2;
                }
                if (cls_ques.type === 1) { // 主观题
                    for (const score of cls_ques.scores || []) {
                        for (const stu of score.students || []) {
                            if (stu.score >= hfs_ques.manfen) continue;
                            handlerStudent(stu, cls_ques);
                        }
                    }
                } else if (cls_ques.type === 2) { // 客观题
                    for (const option of cls_ques.options) {
                        if (option.option === hfs_ques.answer) continue; // 正确的跳过
                        for (const stu of option.students || []) {
                            handlerStudent(stu, cls_ques);
                        }
                    }
                }
            }
            res_cls.students = [...studentMap.values()];
        }
    }
    return result;
}

async function getCtbContent(params) {
    const { id, class_id, student_id } = params;
    const ctbConfig = await db.collection(schema.user_exam_ctb_config).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(ctbConfig)) throw new BussError('导出配置不存在');
    const {user_id, exam_id, paper_id} = ctbConfig;
    const exam = await getUserExam(user_id, exam_id, paper_id);
    const rankExam = await client.rank.getExamById(exam_id);
    const result = {
        exam_id: exam_id,
        exam_name: rankExam.name,
        event_time: moment(rankExam.event_time).toDate().getTime(),
        period: exam.period,
        grade: exam.grade,
        subject: exam.subject,
        class_config: ctbConfig.class_config,
        student_config: ctbConfig.student_config,
        // 'class':
    };
    const hfs_question_info = await client.hfsTeacherV3.getExamPaperQuestionDetail(user_id, exam_id, paper_id);
    const quesMap = new Map();
    if (_.size(hfs_question_info.questions)) {
        if (exam.user_paper_id) { // 设置电子化原题
            const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
            const kb_questions = getTikuPaperQuestions(tiku_paper);
            if (_.size(kb_questions)) {
                hfs_question_info.questions.forEach((ques, i) => {
                    const kb_question = kb_questions[i];
                    // ques.type = kb_question.type;
                    ques.origin_question = kb_question;
                })
            }
        }
        // 相似题
        const same_ids = [];
        for (const exam_ques of exam.questions) {
            if (!_.size(exam_ques.same)) continue; // 可能会存在没有相似题的情况
            same_ids.push(...exam_ques.same);
        }
        if (_.size(same_ids)) {
            const same_questions = await client.kb.getQuestionByIds(same_ids);
            hfs_question_info.questions.forEach((ques) => {
                const exam_ques = exam.questions.find(e => e.id === ques.id);
                const ids = exam_ques && exam_ques.same || [];
                ques.same_questions = same_questions.filter(e => ids.includes(e.id));
            })
        }

        for (const ques of hfs_question_info.questions) {
            quesMap.set(ques.id, ques);
        }
    }
    const selected_ids = ctbConfig.questions.map(e => e.id) || [];
    // const map = new Map();
    const questions = [];
    for (const cls of hfs_question_info.class_info) {
        if (cls.id !== class_id) continue;
        // 班级信息
        result['class'] = {
            id: cls.id,
            name: cls.name
        };
        let student = null;
        // 试题
        for (const cls_ques of cls.question_score || []) {
            if (!selected_ids.includes(cls_ques.id)) continue;
            const hfs_ques = quesMap.get(cls_ques.id);
            let stu_ques = null;
            if (student_id) { // 校验对错
                if (cls_ques.type === 1) { // 主观题
                    for (const score of cls_ques.scores || []) {
                        stu_ques = (score.students || []).find(e => e.id === student_id && e.score < hfs_ques.manfen);
                        if (!_.isEmpty(stu_ques)) break;
                    }
                } else if (cls_ques.type === 2) { // 客观题
                    for (const option of cls_ques.options) {
                        if (option.option === hfs_ques.answer) continue; // 正确的跳过
                        stu_ques = (option.students || []).find(e => e.id === student_id);
                        if (!_.isEmpty(stu_ques)) break;
                    }
                }
                if (_.isEmpty(stu_ques)) continue;
                if (_.isEmpty(student)) {
                    student = {
                        id: stu_ques.id,
                        name: stu_ques.name,
                        xuehao: stu_ques.xuehao,
                        kaohao: stu_ques.kaohao
                    }
                }
            } else {
                if (cls_ques.score_rate >= 1) continue;
            }
            const ques = _.assign({}, hfs_ques);
            // if (!_.size(ques.same_questions)) continue;
            ques.class_score_rate = cls_ques.score_rate;
            ques.class_score = cls_ques.score;
            if (stu_ques) ques.student_score = stu_ques.score || 0;
            questions.push(ques);
        }
        if (!_.isEmpty(student)) {
            result.student = student;
        }
    }
    result.questions = questions;
    return result;
}

async function download(req, res, params) {
    const {user_id, filename, content} = params;
    const download_params = {
        html: content,
        filename: filename,
    }
    const response = await client.utilbox.getDownloadInfo(download_params);
    if (_.isEmpty(response)) throw new BussError('下载文件错误');
    // 响应数据
    res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${filename}.docx`)}`);
    // res.setHeader('Content-type', fileMime);
    res.setHeader('Content-type', 'application/octet-stream');
    return response.data;
}


function getGradeInfoByRankExamPaper(paper) {
    const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === paper.grade || e.grade === paper.grade);
    return {
        period: gradeInfo.period,
        grade: gradeInfo.grade,
        subject: paper.subject
    };
}

async function getUserExam(user_id, exam_id, paper_id) {
    return await db.collection(schema.user_exam_ctb).findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
}


async function getPaperStatus(examId, paperId, ypPaper) {
    const result =  {
        original: enums.BOOL.NO, // 是否上传原卷0：否，1：是
        published: enums.BOOL.NO, // 是否发布成绩0：否，1：是
    };
    if (_.isEmpty(ypPaper)) {
        ypPaper = await client.yuanpei.getPaperQuestions(paperId);
        if (!_.isEmpty(ypPaper)) {
            result.original = enums.BOOL.YES;
        }
    } else {
        result.original = enums.BOOL.YES;
    }
    const subject = _.isEmpty(ypPaper) ? '' : _.get(ypPaper, 'paper.subject');
    const published = await client.hfs.getExamPublishStatus(examId, paperId, subject);
    if (published) {
        result.published = enums.BOOL.YES;
    }
    return result;
}


async function initExam(user_id, exam_id, paper_id, user_paper_id = '', period, grade, subject) {
    const exam = {
        user_id: user_id,
        exam_id: exam_id,
        paper_id: paper_id,
        user_paper_id: user_paper_id, // 用户组卷ID
        grade: grade, // 年级
        period: period, // 学段
        subject: subject, // 科目
        questions: [],
        ctime: new Date(),
        utime: new Date(),
    };
    const result = await db.collection(schema.user_exam_ctb).insertOne(exam);
    exam._id = result.insertedId;
    return exam;
}

function getTikuPaperQuestions(tiku_paper) {
    const questions = [];
    if (_.isEmpty(tiku_paper)) return questions;
    for (const v of tiku_paper.volumes || []) {
        for (const b of v.blocks || []) {
            questions.push(...b.questions);
        }
    }
    return questions;
}

function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}

function findYpQuestion(hfs_question, yp_paper) {
    let result = null;
    if (_.isEmpty(yp_paper)) return result;
    for (const yp_question of yp_paper.questions || []) {
        const point = (yp_question.points || []).find(e => e.id.split('-')[1] === hfs_question.questionId.toString());
        if (!_.isEmpty(point)) {
            result = yp_question;
            break;
        }
    }
    return result;
}

function sortByRank(score_report, class_info) {
    if (!_.size(score_report) || _.isEmpty(class_info)) return;
    const stu_map = {};
    for (const stu of score_report) {
        stu_map[stu.studentId] = { rank: _.get(stu, 'current.classRank') };
    }
    for (const ques of class_info.question_score) {
        if (ques.hasOwnProperty('options')) { // 客观题
            for (const option of ques.options || []) {
                option.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                option.students = _.orderBy(option.students, ['rank'], ['asc']);
            }
        } else if (ques.hasOwnProperty('scores')) { // 主观题
            for (const score of ques.scores || []) {
                score.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                score.students = _.orderBy(score.students, ['rank'], ['asc']);
            }
        }
    }
}


async function buildPaperRecommendQuestions(exam, yp_paper) {
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    let kb_questions = [];
    if (exam.user_paper_id) {
        const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
        kb_questions = getTikuPaperQuestions(tiku_paper);
    }
    const params = [];
    const quest_map = new Map();
    const exam_questions = exam.questions || [];
    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.id === hfs_question.id);
        if (!_.isEmpty(question)) continue;

        const param = {
            period: exam.period,
            subject: exam.subject,
            grade: exam.grade,
            recom_num: 2
        };
        if (exam.user_paper_id) {
            const index = hfs_questions.findIndex(e => e.id === hfs_question.id);
            const kb_question = kb_questions[index];
            if (_.isEmpty(kb_question) || quest_map.has(kb_question.id)) continue;
            quest_map.set(kb_question.id, []);
            param.ques_id = kb_question.id.toString();
            param.ques_from = 'kb';
            param.ques_type = kb_question.type;
        } else {
            const yp_question = findYpQuestion(hfs_question, yp_paper);
            if (_.isEmpty(yp_question) || quest_map.has(yp_question.id)) continue;
            quest_map.set(yp_question.id, []);
            param.ques_id = yp_question.id;
            param.ques_from = 'yuanpei';
            param.ques_type = _get_hfs_question_type(exam.subject, hfs_question, yp_question);
        }
        params.push(param);
    }
    if (!_.size(params)) return exam_questions;
    let algo_result = [];
    try {
        for (const arr of _.chunk(params, 10)) {
            const res = await Promise.all(arr.map(e => client.algo.recommendQuestions(e)));
            algo_result.push(...res);
        }
    } catch (e) {
        algo_result = [];
        logger.error(e);
    }
    if (!_.size(algo_result)) return exam_questions;
    const result = [];
    //
    for (let i = 0; i < params.length; i++) {
        const param = params[i];
        quest_map.set(param.ques_id, (algo_result[i].questions || []).map(e => e.id));
    }

    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.id === hfs_question.id);
        if (!_.isEmpty(question)) {
            result.push(question);
        } else {
            let same = [];
            if (exam.user_paper_id) {
                const index = hfs_questions.findIndex(e => e.id === hfs_question.id);
                const kb_question = kb_questions[index];
                same = quest_map.get(kb_question.id.toString()) ||[];
            } else {
                const yp_question = findYpQuestion(hfs_question, yp_paper);
                same = quest_map.get(yp_question.id) ||[];
            }
            if (!_.size(same)) continue;
            result.push({
                id: hfs_question.id,
                same: same
            });
        }
    }
    await db.collection(schema.user_exam_ctb).updateOne({_id: exam._id}, {$set: {questions: result}});
    exam.questions = result;
    return result;
}
