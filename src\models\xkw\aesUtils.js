const crypto = require('crypto');

module.exports = {
    aesEncrypt,
    md5,
}
/**
 * AES 加密
 * @param {string} str 需要加密的字符串
 * @param {string} key 加密密钥
 * @returns {string} 加密后的Base64编码字符串
 */
function aesEncrypt (str, key) {
    if (!str || !key) {
        return "";
    }

    // 确保密钥长度为 128位（16字节）、192位（24字节）或 256位（32字节）
    const keyBuffer = Buffer.alloc(32, 0); // 默认使用 256位密钥
    const keyBytes = Buffer.from(key, 'utf8');
    keyBytes.copy(keyBuffer); // 将密钥复制到固定长度的Buffer中

    const cipher = crypto.createCipheriv('aes-256-ecb', keyBuffer, null); // ECB模式不需要IV
    let encrypted = cipher.update(str, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
}
function md5(encryptStr) {
    const hash = crypto.createHash('md5').update(encryptStr, 'utf8').digest('hex');
    return hash.length % 2 === 1 ? '0' + hash : hash;
}

function getSign(params, secret) {

}
// // 示例用法
// const key = 'your-secret-key'; // 密钥可以是任意长度，但会被填充或截断为32字节
// const str = 'Hello, World!';
//
// const encrypted = aesEncrypt(str, key);
// console.log('AES Encrypted:', encrypted);
//
// const md5Hash = md5(str);
// console.log('MD5 Hash:', md5Hash);
