---
title: 考试管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 考试管理接口

考试管理接口提供考试组织、数据分析、试卷管理和讲评功能，支持完整的考试流程管理。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取考试列表

GET /v1/exam

获取当前用户的考试列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "exam_001",
      "name": "2024年春季期中考试",
      "start_time": "2024-04-15T08:00:00Z",
      "end_time": "2024-04-15T10:00:00Z",
      "status": "已结束",
      "subject": "数学",
      "grade": "七年级",
      "classes": ["1班", "2班", "3班"],
      "participant_count": 135
    }
  ]
}
```

---

## GET 获取考试详情

GET /v1/exam/detail

获取指定考试的详细信息，包括试卷和班级信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|query|string|是|考试ID|
|paper_id|query|string|是|试卷ID|
|user_paper_id|query|string|否|用户试卷ID，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/detail?exam_id=exam_001&paper_id=paper_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "exam_001",
    "name": "2024年春季期中考试",
    "paper_id": "paper_001",
    "user_paper_id": "",
    "original_question_count": 25,
    "papers": [
      {
        "id": "paper_001",
        "name": "数学试卷A",
        "question_count": 25,
        "score": 100
      }
    ],
    "table": {
      "id": "table_001",
      "name": "细目表",
      "questions": []
    },
    "table_id": "table_001",
    "classes": [
      {
        "id": "class_001",
        "name": "七年级1班",
        "student_count": 45,
        "submitted_count": 44
      }
    ],
    "original": 1,
    "published": 0
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|考试详情|考试完整信息|
|»» id|string|true|none|考试ID|唯一标识|
|»» name|string|true|none|考试名称|考试标题|
|»» original|integer|true|none|原卷状态|0-未上传，1-已上传|
|»» published|integer|true|none|发布状态|0-未发布，1-已发布成绩|
|»» papers|array|true|none|试卷列表|关联的试卷信息|
|»» classes|array|true|none|班级列表|参加考试的班级|

---

## GET 获取班级详情

GET /v1/exam/class

获取班级在指定考试中的详细数据分析。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|query|string|是|考试ID|
|paper_id|query|string|是|试卷ID|
|class_id|query|string|是|班级ID|
|user_paper_id|query|string|否|用户试卷ID，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/class?exam_id=exam_001&paper_id=paper_001&class_id=class_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "grade_score": {
      "avg": 85.6,
      "excellent_rate": 68.5,
      "class_count": 8
    },
    "class_score": {
      "avg": 87.2,
      "excellent_rate": 72.3,
      "avg_rank": 3
    },
    "knowledges": [
      {
        "id": "k_001",
        "name": "有理数",
        "accuracy": 0.85,
        "difficulty": "中等"
      }
    ],
    "paper": {
      "id": "paper_001",
      "name": "数学试卷A",
      "question_count": 25
    },
    "table": {
      "id": "table_001",
      "name": "细目表"
    },
    "table_id": "table_001"
  }
}
```

### 错误示例

#### 班级信息不存在

```json
{
  "code": 4,
  "msg": "班级信息不存在",
  "data": ""
}
```

#### 班级考试信息不存在

```json
{
  "code": 4,
  "msg": "班级考试信息不存在",
  "data": ""
}
```

---

## POST 平行组卷

POST /v1/exam/paper

为考试创建平行试卷，用于减少作弊风险。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|body|string|是|考试ID|
|exam_name|body|string|是|考试名称|
|paper_id|body|string|是|试卷ID|
|user_paper_id|body|string|否|用户试卷ID|
|class_id|body|string|否|班级ID，允许空字符串|
|class_name|body|string|否|班级名称，允许空字符串|
|category|body|array|是|组卷类别数组，至少1个|
|table|body|object|是|细目表对象|
|» name|body|string|是|细目表名称|
|» period|body|string|是|学段|
|» grade|body|string|否|年级，允许空字符串|
|» subject|body|string|是|科目|
|» blocks|body|array|是|题块数组，至少1个|
|»» name|body|string|是|题块名称|
|»» type|body|string|是|题块类型|
|»» questions|body|array|是|题目数组，至少1个|
|»»» type|body|string|是|题目类型|
|»»» difficulty|body|string|是|难度|
|»»» score|body|number|是|分值|
|»»» period|body|string|是|学段|
|»»» subject|body|string|是|科目|
|»»» knowledges|body|array|是|知识点数组，至少1个|
|»»»» id|body|number|是|知识点ID|
|»»»» name|body|string|是|知识点名称|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/exam/paper" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "exam_id": "exam_001",
    "exam_name": "期中考试",
    "paper_id": "paper_001",
    "category": [1, 2],
    "table": {
      "name": "数学细目表",
      "period": "初中",
      "subject": "数学",
      "blocks": [
        {
          "name": "选择题",
          "type": "选择题",
          "questions": [
            {
              "type": "选择题",
              "difficulty": "容易",
              "score": 4,
              "period": "初中",
              "subject": "数学",
              "knowledges": [
                {
                  "id": 1001,
                  "name": "有理数"
                }
              ]
            }
          ]
        }
      ]
    }
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "papers": [
      {
        "id": "parallel_paper_001",
        "name": "数学试卷A",
        "category": 1
      },
      {
        "id": "parallel_paper_002", 
        "name": "数学试卷B",
        "category": 2
      }
    ],
    "table_id": "table_001"
  }
}
```

### 错误示例

#### 组卷失败

```json
{
  "code": 4,
  "msg": "组卷失败",
  "data": ""
}
```

---

## GET 获取考试列表（分页）

GET /v1/exam/list

分页获取考试列表，支持类型筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|string|是|偏移量|
|limit|query|string|是|每页数量|
|type|query|number|否|考试类型|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/list?offset=0&limit=20&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 45,
    "list": [
      {
        "id": "exam_001",
        "name": "2024年春季期中考试",
        "type": 1,
        "start_time": "2024-04-15T08:00:00Z",
        "status": "已结束"
      }
    ]
  }
}
```

---

## GET 获取考试班级列表

GET /v1/exam/{exam_id}/paper/{paper_id}/classes

获取参加指定考试试卷的班级列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/exam_001/paper/paper_001/classes" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "class_001",
      "name": "七年级1班",
      "student_count": 45,
      "submitted_count": 44,
      "average_score": 87.5
    }
  ]
}
```

---

## GET 获取考试试题详情

GET /v1/exam/{exam_id}/paper/{paper_id}/questions

获取考试试卷中所有题目的详细信息和统计数据。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|zujuanId|query|string|否|组卷ID，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/exam_001/paper/paper_001/questions?zujuanId=zj_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "paper": {
      "exam_id": "exam_001",
      "exam_name": "2024年春季期中考试",
      "original": 1,
      "published": 0,
      "image_url": ["https://example.com/paper.jpg"],
      "period": "初中",
      "grade": "七年级",
      "subject": "数学"
    },
    "questions": [
      {
        "id": "q_001",
        "content": "下列数中，是有理数的是（）",
        "type": "选择题",
        "difficulty": "容易",
        "score": 4,
        "accuracy": 0.85,
        "knowledges": ["有理数"]
      }
    ],
    "class_info": [
      {
        "id": "class_001",
        "name": "七年级1班",
        "student_count": 45
      }
    ]
  }
}
```

### 错误示例

#### 考试不存在

```json
{
  "code": 4,
  "msg": "考试不存在或者未同步!",
  "data": ""
}
```

#### 非考试作业

```json
{
  "code": 4,
  "msg": "非考试作业",
  "data": ""
}
```

---

## GET 获取相似题

GET /v1/exam/{exam_id}/paper/{paper_id}/question/{question_id}/same

获取与考试中某道题相似的其他题目。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|question_id|path|string|是|题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/exam_001/paper/paper_001/question/q_001/same" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "q_002",
      "content": "关于有理数的分类，下列说法正确的是（）",
      "type": "选择题",
      "difficulty": "容易",
      "similarity": 0.92
    }
  ]
}
```

---

## GET 获取答题卡图片

GET /v1/exam/{exam_id}/paper/{paper_id}/question/{question_id}/answer/pictures

获取学生在某道题上的答题卡图片。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|question_id|path|string|是|题目ID|
|offset|query|number|是|偏移量|
|limit|query|number|是|每页数量|
|class_id|query|string|否|班级ID|
|min|query|number|否|最小分数，默认-1|
|max|query|number|否|最大分数，默认-1|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/exam_001/paper/paper_001/question/q_001/answer/pictures?offset=0&limit=20&class_id=class_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 44,
    "list": [
      {
        "student_id": "stu_001",
        "student_name": "张三",
        "score": 4,
        "image_url": "https://example.com/answer/stu_001_q_001.jpg",
        "class_name": "七年级1班"
      }
    ],
    "excellent_count": 38,
    "mediocre_count": 6
  }
}
```

---

## GET 获取学生答题详情

GET /v1/exam/{exam_id}/paper/{paper_id}/class/{class_id}/student/{student_id}/question/{question_id}/brief

获取特定学生在某道题上的详细答题信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|class_id|path|string|是|班级ID|
|student_id|path|string|是|学生ID|
|question_id|path|string|是|题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exam/exam_001/paper/paper_001/class/class_001/student/stu_001/question/q_001/brief" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "student_answer": "C",
    "correct_answer": "B",
    "score": 0,
    "full_score": 4,
    "is_correct": false,
    "answer_time": "2024-04-15T09:15:30Z",
    "image_url": "https://example.com/answer/stu_001_q_001.jpg"
  }
}
```

---

## PUT 答题卡标记

PUT /v1/exam/{exam_id}/paper/{paper_id}/question/{question_id}/answer/tag

为学生的答题卡添加标记（如优秀、一般等）。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|question_id|path|string|是|题目ID|
|type|body|string|是|标记类型|
|student_id|body|string|是|学生ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/exam/exam_001/paper/paper_001/question/q_001/answer/tag" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "excellent",
    "student_id": "stu_001"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

---

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 考试流程

1. **考试创建**: 从HFS系统同步考试信息
2. **试卷关联**: 将试卷与考试关联
3. **平行组卷**: 基于细目表生成多份平行试卷
4. **数据分析**: 自动统计成绩和知识点掌握情况
5. **讲评准备**: 标记典型答题，生成讲评课件

### 数据来源

- **考试信息**: 来自HFS教务系统
- **成绩数据**: 来自元培阅卷系统
- **试题内容**: 来自知识库服务
- **学生信息**: 来自班级管理系统

### 统计算法

- **平均分计算**: 基于实际参考学生
- **优秀率统计**: 按学校标准计算
- **知识点分析**: 基于试题知识点标签
- **排名计算**: 支持年级和班级排名

### 权限控制

- 教师只能查看自己任教班级的数据
- 考试数据需要同步后才能查看
- 成绩发布前后有不同的数据权限