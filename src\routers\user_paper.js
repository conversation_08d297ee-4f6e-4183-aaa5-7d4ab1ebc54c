const express = require('express');
const router = express.Router();
const service = require('../service/user_paper');
const { handler } = require('../../common/middlewares/request');

// 获取试卷分类
router.get('/list', handler(1, service.get_list));
// 试卷详细
router.get('/:id', handler(1, service.get_detail));
// 保存试卷
router.post('', handler(1, service.post_paper));
// 批量保存、修改试卷
router.post('/batch', handler(1, service.batch_post_paper));
// 修改试卷状态
router.put('/:id/status/:status', handler(1, service.update_paper_status));
// 删除试卷
router.delete('/:id', handler(1, service.delete_paper));
// 答题卡
router.get('/dtk/gateway', handler(1, service.getDtkGateway));
// 上传试卷
router.post('/upload', handler(1, service.upload_paper));
// 上传试卷解析回调
router.post('/upload/parse/callback', handler(1, service.parse_callback));
// 获取上传列表
router.get('/upload/list', handler(1, service.get_upload_list));
// 删除上传任务
router.delete('/upload/:id', handler(1, service.delete_upload_by_id));

module.exports = router;
