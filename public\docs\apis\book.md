---
title: 教材管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 教材管理接口

教材管理接口提供教材信息查询、章节浏览和相关试卷推荐功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取教材列表

GET /v1/book

获取教材列表，支持按学段和科目筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段，如：小学、初中、高中|
|subject|query|string|否|科目，如：语文、数学、英语|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/book?period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "book_id": 1001,
      "name": "人教版初中数学七年级上册",
      "period": "初中",
      "subject": "数学",
      "grade": "七年级",
      "term": "上学期",
      "press_version": "人教版",
      "cover_url": "https://example.com/cover.jpg",
      "isbn": "978-7-107-12345-6",
      "publisher": "人民教育出版社",
      "publish_date": "2023-06"
    }
  ]
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|array|true|none|教材列表|教材信息数组|
|»» book_id|integer|true|none|教材ID|唯一标识|
|»» name|string|true|none|教材名称|完整的教材名称|
|»» period|string|true|none|学段|小学/初中/高中|
|»» subject|string|true|none|科目|具体科目名称|
|»» grade|string|true|none|年级|适用年级|
|»» term|string|true|none|学期|上学期/下学期/全年|
|»» press_version|string|true|none|版本|出版社版本|
|»» cover_url|string|false|none|封面图片|教材封面URL|

---

## GET 获取教材详情

GET /v1/book/{book_id}

获取指定教材的详细信息，包括章节结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|book_id|path|number|是|教材ID|
|type|query|string|否|类型参数|
|fields_type|query|string|否|字段类型|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/book/1001?type=detail&fields_type=knowledge" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "book_id": 1001,
    "name": "人教版初中数学七年级上册",
    "period": "初中", 
    "subject": "数学",
    "grade": "七年级",
    "term": "上学期",
    "press_version": "人教版",
    "description": "教材简介...",
    "book": {
      "children": [
        {
          "id": 10001,
          "name": "第一章 有理数",
          "key": "chapter_1",
          "order": 1,
          "children": [
            {
              "id": 10011,
              "name": "1.1 正数和负数",
              "key": "section_1_1",
              "order": 1,
              "knowledge_points": ["正数", "负数", "有理数分类"]
            },
            {
              "id": 10012,
              "name": "1.2 有理数",
              "key": "section_1_2", 
              "order": 2,
              "knowledge_points": ["有理数定义", "数轴"]
            }
          ]
        },
        {
          "id": 10002,
          "name": "第二章 整式的加减",
          "key": "chapter_2",
          "order": 2,
          "children": []
        }
      ]
    }
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|教材详情|教材完整信息|
|»» book_id|integer|true|none|教材ID|唯一标识|
|»» name|string|true|none|教材名称|完整名称|
|»» book|object|true|none|教材结构|章节层级结构|
|»»» children|array|true|none|章节列表|顶级章节数组|
|»»»» id|integer|true|none|章节ID|唯一标识|
|»»»» name|string|true|none|章节名称|章节标题|
|»»»» key|string|true|none|章节键|章节标识符|
|»»»» order|integer|true|none|排序|章节顺序|
|»»»» children|array|true|none|子章节|小节数组|

---

## POST 章节推荐试卷

POST /v1/book/chapter/papers

根据教材章节推荐相关试卷，支持智能算法推荐。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|book_id|body|number|否|教材ID，与period+subject二选一|
|chapter_id|body|number|否|章节ID|
|period|body|string|条件必选|学段，当book_id不存在时必选|
|subject|body|string|条件必选|科目，当book_id不存在时必选|
|grade|body|string|是|年级，必填|
|term|body|string|否|学期|
|type|body|string|否|试卷类型，默认"同步练习"|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
# 通过教材ID推荐
curl -X POST "http://localhost:8055/v1/book/chapter/papers" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "book_id": 1001,
    "chapter_id": 10001,
    "grade": "七年级",
    "term": "上学期",
    "type": "同步练习"
  }'

# 通过学段科目推荐
curl -X POST "http://localhost:8055/v1/book/chapter/papers" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学", 
    "grade": "七年级",
    "term": "上学期",
    "type": "单元测试"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 15,
    "list": [
      {
        "id": "paper_001",
        "name": "有理数单元测试卷",
        "type": "同步练习",
        "question_count": 20,
        "difficulty": "中等",
        "score": 100,
        "duration": 90,
        "create_time": "2024-01-15",
        "source": "系统推荐",
        "match_score": 0.95,
        "knowledge_coverage": [
          {
            "name": "正数和负数",
            "coverage": 0.8
          },
          {
            "name": "有理数概念", 
            "coverage": 0.9
          }
        ]
      },
      {
        "id": "paper_002",
        "name": "第一章综合练习",
        "type": "章节测试",
        "question_count": 25,
        "difficulty": "较难",
        "score": 120,
        "duration": 120,
        "create_time": "2024-01-10",
        "source": "智能推荐",
        "match_score": 0.88
      }
    ]
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|推荐结果|推荐试卷数据|
|»» total|integer|true|none|总数量|推荐试卷总数|
|»» list|array|true|none|试卷列表|推荐试卷数组|
|»»» id|string|true|none|试卷ID|唯一标识|
|»»» name|string|true|none|试卷名称|试卷标题|
|»»» type|string|true|none|试卷类型|同步练习/单元测试等|
|»»» question_count|integer|true|none|题目数量|试卷包含题目数|
|»»» difficulty|string|true|none|难度等级|容易/中等/较难/困难|
|»»» score|integer|true|none|总分|试卷满分|
|»»» duration|integer|true|none|时长|建议答题时间(分钟)|
|»»» match_score|number|true|none|匹配度|推荐算法匹配分数(0-1)|

### 错误示例

#### 年级错误

```json
{
  "code": 4,
  "msg": "年级错误",
  "data": ""
}
```

#### 学段科目错误

```json
{
  "code": 4,
  "msg": "学段科目错误", 
  "data": ""
}
```

#### 参数验证失败

```json
{
  "code": 3,
  "msg": "参数错误：grade是必需的",
  "data": ""
}
```

### 业务逻辑说明

1. **参数验证**: 验证grade必填，book_id与period+subject至少提供一组
2. **知识树构建**: 基于教材章节构建知识点映射
3. **算法推荐**: 使用机器学习算法分析匹配度
4. **结果排序**: 按匹配度和质量评分排序
5. **数据增强**: 返回知识点覆盖度等详细信息

### 使用场景

- **备课辅助**: 教师根据教学进度寻找配套练习
- **个性化学习**: 学生针对性地练习特定知识点
- **教学资源**: 快速获取高质量的同步试卷

### 注意事项

- book_id和period+subject参数至少提供一组
- grade参数在任何情况下都是必需的
- 推荐结果按匹配度降序排列
- 算法会考虑用户学校和地区特点进行个性化推荐