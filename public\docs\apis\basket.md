---
title: 试题篮管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 试题篮管理接口

试题篮管理接口提供试题收藏、批量操作和组卷功能，类似购物车机制。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取试题篮试题

GET /v1/basket/questions

获取用户试题篮中的试题列表，支持按学段科目分类管理。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|query|string|否|试题篮类型，默认"basket"|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|page|query|number|否|页码，默认1|
|limit|query|number|否|每页数量，默认20|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/basket/questions?period=初中&subject=数学&category=basket&page=1&limit=20" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_123",
    "category": "basket",
    "period": "初中",
    "subject": "数学",
    "total": 25,
    "page": 1,
    "limit": 20,
    "questions": [
      {
        "id": 12345,
        "type": "选择题",
        "difficulty": "中等",
        "content": "下列说法正确的是（）",
        "period": "初中",
        "subject": "数学",
        "grade": "七年级",
        "source": "sys",
        "source_id": 12345,
        "score": 3,
        "knowledges": ["有理数", "数的分类"],
        "add_time": "2024-01-15 10:30:00",
        "order": 1
      },
      {
        "id": 12346,
        "type": "填空题",
        "difficulty": "容易",
        "content": "计算：(-3) + 5 = ______",
        "period": "初中", 
        "subject": "数学",
        "grade": "七年级",
        "source": "sys",
        "source_id": 12346,
        "score": 2,
        "knowledges": ["有理数的加法"],
        "add_time": "2024-01-15 11:15:00",
        "order": 2
      }
    ],
    "summary": {
      "by_type": {
        "选择题": 12,
        "填空题": 8,
        "解答题": 5
      },
      "by_difficulty": {
        "容易": 8,
        "中等": 12,
        "较难": 4,
        "困难": 1
      },
      "total_score": 75,
      "estimated_time": 90
    },
    "limit_info": {
      "current": 25,
      "max": 60,
      "remaining": 35
    }
  }
}
```

---

## POST 添加试题到试题篮

POST /v1/basket/questions

向试题篮中添加一个或多个试题。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|body|string|否|试题篮类型，默认"basket"|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|questions|body|array|是|试题数组，至少1题，最多10题|
|» id|body|number/string|是|题目ID|
|» type|body|string|是|题目类型|
|» period|body|string|是|学段|
|» subject|body|string|是|科目|
|» source|body|string|否|来源，默认"sys"|
|» source_id|body|number/string|否|来源ID|
|» score|body|number|否|分值|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/basket/questions" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "category": "basket",
    "questions": [
      {
        "id": 12347,
        "type": "解答题",
        "period": "初中",
        "subject": "数学",
        "source": "sys",
        "source_id": 12347,
        "score": 8
      },
      {
        "id": 12348,
        "type": "选择题",
        "period": "初中",
        "subject": "数学",
        "source": "sys",
        "source_id": 12348,
        "score": 3
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_123",
    "period": "初中",
    "subject": "数学",
    "total": 27,
    "added_count": 2,
    "duplicate_count": 0,
    "questions": [
      {
        "id": 12345,
        "type": "选择题",
        "add_time": "2024-01-15 10:30:00",
        "order": 1
      },
      {
        "id": 12347,
        "type": "解答题",
        "add_time": "2024-01-15 15:20:00",
        "order": 26
      },
      {
        "id": 12348,
        "type": "选择题",
        "add_time": "2024-01-15 15:20:00",
        "order": 27
      }
    ],
    "summary": {
      "by_type": {
        "选择题": 13,
        "填空题": 8,
        "解答题": 6
      },
      "total_score": 86,
      "estimated_time": 105
    },
    "limit_info": {
      "current": 27,
      "max": 60,
      "remaining": 33
    }
  }
}
```

### 错误示例

#### 试题数量超限

```json
{
  "code": 4,
  "msg": "您的账号已达数量上限",
  "data": ""
}
```

#### 参数验证失败

```json
{
  "code": 3,
  "msg": "参数错误：questions数组至少需要1个元素",
  "data": ""
}
```

---

## DELETE 从试题篮删除试题

DELETE /v1/basket/questions

从试题篮中删除指定的试题。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|body|string|否|试题篮类型，默认"basket"|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|questions|body|array|是|要删除的题目ID数组，至少1个|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/basket/questions" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "category": "basket",
    "questions": [12345, 12346]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_123",
    "period": "初中",
    "subject": "数学",
    "total": 23,
    "deleted_count": 2,
    "questions": [
      {
        "id": 12347,
        "type": "解答题",
        "add_time": "2024-01-14 16:45:00",
        "order": 1
      },
      {
        "id": 12348,
        "type": "选择题",
        "add_time": "2024-01-15 15:20:00",
        "order": 2
      }
    ],
    "summary": {
      "by_type": {
        "选择题": 11,
        "填空题": 6,
        "解答题": 6
      },
      "total_score": 70,
      "estimated_time": 85
    },
    "limit_info": {
      "current": 23,
      "max": 60,
      "remaining": 37
    }
  }
}
```

---

## GET 获取试题篮信息

GET /v1/basket

获取试题篮基本信息和试题，支持试卷合并操作。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|query|string|否|试题篮类型，默认"basket"|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|paper_id|query|string/number|否|试卷ID，用于合并操作|
|op|query|string|否|操作类型：delete/merge，默认delete|
|source_type|query|string|否|来源类型，默认"sys"|
|template|query|string|否|模板类型：standard/exam/homework|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
# 基础获取
curl -X GET "http://localhost:8055/v1/basket?period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"

# 合并试卷
curl -X GET "http://localhost:8055/v1/basket?period=初中&subject=数学&paper_id=paper_001&op=merge&template=exam" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应（基础获取）

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_123",
    "period": "初中",
    "subject": "数学",
    "category": "basket",
    "template": "standard",
    "total": 23,
    "questions": [
      {
        "id": 12347,
        "type": "解答题",
        "difficulty": "中等",
        "content": "题目内容...",
        "score": 8,
        "add_time": "2024-01-14 16:45:00",
        "order": 1
      }
    ],
    "summary": {
      "by_type": {
        "选择题": 11,
        "填空题": 6,
        "解答题": 6
      },
      "by_difficulty": {
        "容易": 6,
        "中等": 12,
        "较难": 4,
        "困难": 1
      },
      "total_score": 70,
      "estimated_time": 85
    },
    "limit_info": {
      "current": 23,
      "max": 60,
      "remaining": 37
    }
  }
}
```

#### 成功响应（合并试卷）

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_123",
    "period": "初中",
    "subject": "数学",
    "category": "basket",
    "template": "exam",
    "total": 28,
    "questions": [
      {
        "id": 12347,
        "type": "解答题",
        "source": "basket",
        "add_time": "2024-01-14 16:45:00",
        "order": 1
      },
      {
        "id": 13001,
        "type": "选择题",
        "source": "paper",
        "paper_id": "paper_001",
        "add_time": "2024-01-15 16:00:00",
        "order": 24
      }
    ],
    "paper_info": {
      "id": "paper_001",
      "name": "期中测试卷",
      "question_count": 25,
      "merged_count": 5,
      "merge_time": "2024-01-15 16:00:00"
    },
    "summary": {
      "basket_questions": 23,
      "paper_questions": 5,
      "total_score": 95,
      "estimated_time": 120
    },
    "limit_info": {
      "current": 28,
      "max": 60,
      "remaining": 32
    }
  }
}
```

### 错误示例

#### 试题数量超限

```json
{
  "code": 4,
  "msg": "试题篮试题数量超过上限",
  "data": ""
}
```

#### 试卷不存在

```json
{
  "code": 5,
  "msg": "指定的试卷不存在",
  "data": ""
}
```

---

## DELETE 清空试题篮

DELETE /v1/basket

清空指定学段科目的试题篮。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category|body|string|否|试题篮类型，默认"basket"|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/basket" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "category": "basket"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "success": true,
    "user_id": "user_123",
    "category": "basket",
    "period": "初中",
    "subject": "数学",
    "deleted_count": 23,
    "clear_time": "2024-01-15 16:30:00",
    "message": "试题篮已清空"
  }
}
```

---

## 业务逻辑说明

### 试题篮分类

系统支持多种试题篮类型：
- **basket**: 默认试题篮，用于日常收藏
- **exam**: 考试试题篮，用于考试组卷
- **homework**: 作业试题篮，用于作业布置
- **practice**: 练习试题篮，用于练习组织

### 数量限制

- 每个学段科目的试题篮最多60题
- 单次添加最多10题
- 单次删除无数量限制
- 超出限制时会返回相应错误信息

### 合并策略

试卷合并到试题篮时采用以下策略：
1. **去重处理**: 自动过滤已存在的试题
2. **顺序保持**: 保持试卷中试题的相对顺序
3. **元数据保留**: 保留试题来源信息
4. **限制检查**: 合并前检查是否超出数量限制

### 统计信息

系统提供丰富的统计信息：
- **题型分布**: 各种题型的数量统计
- **难度分布**: 各难度等级的题目数量
- **分值统计**: 总分值和平均分值
- **时间估算**: 基于题型和难度的答题时间估算

### 排序规则

试题篮中的试题按以下规则排序：
1. 添加时间升序（先加入的在前）
2. 相同时间按题目ID升序
3. 合并试卷时保持试卷内部顺序

### 缓存机制

- 试题篮数据采用缓存机制提高性能
- 增删改操作会自动更新缓存
- 缓存有效期为30分钟
- 支持强制刷新缓存