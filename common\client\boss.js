const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const SERVER = config.get('boss_server');
const qs = require('querystring');
const options = {
    headers: {
        'apikey': SERVER.apikey
    },
    timeout: 2000
};

module.exports = {
    getSchoolInfo,
    getSchoolAllAppUsageInfo,
}

async function getSchoolInfo(schoolId, productCategory) {
    let bossUrl = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/external/api/customer/app_usage/get_by_school_id',
        port: SERVER.port,
        search: qs.stringify({
            schoolId: schoolId,
            productCategory: productCategory,
        })
    });
    try {
        const result = await axios.get(bossUrl, options);
        return result.data && result.data.data;
    } catch (e) {
        logger.error(e);
        return null;
    }
}


async function getSchoolAllAppUsageInfo(schoolId) {
    let bossUrl = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/external/api/customer/app_usage/get_all_by_school_id',
        port: SERVER.port,
        search: qs.stringify({
            schoolId: schoolId,
        })
    });
    try {
        const result = await axios.get(bossUrl, options);
        return result.data && result.data.data;
    } catch (e) {
        logger.error(e);
        return null;
    }
}
