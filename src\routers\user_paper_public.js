const express = require('express');
const router = express.Router();
const service = require('../service/user_paper');
const { handler } = require('../../common/middlewares/request');


// 试卷详细
router.get('/public/by_ai_task_id', handler(1, service.get_detail_by_ai_task_id));
// 保存试卷
router.post('/public', handler(1, service.post_paper_public));

router.get('/public/content', handler(1, service.get_paper_public_by_content));

module.exports = router;
