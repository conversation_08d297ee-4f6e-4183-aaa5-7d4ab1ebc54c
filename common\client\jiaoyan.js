const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('jiaoyan_server');
const qs = require('querystring');

module.exports = {
    unifyTokenLogin
}

async function unifyTokenLogin(unifyToken) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/auth/actions/loginByUnifyToken',
    });
    try {
        const response = await axios.post(url, {unifyToken});
        if (response && response.data.code === 0) {
            return response.data.data;
        }
    } catch (e) {
        logger.error('教研平台登录错误', e);
    }
    return null;
}
