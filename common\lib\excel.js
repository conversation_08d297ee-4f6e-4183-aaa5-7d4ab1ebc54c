const parseString = require('xml2js').parseString;
const fs = require('fs');
const _ = require('underscore');
const zipdir = require('zip-dir');
const decompress = require('decompress');
const decompressUnzip = require('decompress-unzip');
const path = require('path');
const rimraf = require('rimraf');
const enfscopy = require('enfscopy');
const nodeExcel = require('node-xlsx');

function _cBaseInfo(table){
	var template = '<row r="1" spans="1:5" x14ac:dyDescent="0.15"><c r="A1" t="s"><v>0</v></c><c r="B1" t="s"><v>1</v></c><c r="C1" t="s"><v>2</v></c></row>';

	var templateHeader = '<row r="<%=ix%>" spans="1:5" x14ac:dyDescent="0.15"><c r="A<%=ix%>" t="s"><v><%=name%></v></c><c r="B<%=ix%>" t="s"><v><%=type%></v></c><c r="C<%=ix%>" t="s"><v><%=knowledges%></v></c><c r="D<%=ix%>" t="s"><v><%=difficulty%></v></c><c r="E<%=ix%>" t="s"><v><%=score%></v></c></row>';

	return template + _.template(templateHeader)({
		ix: 2,
		name: 3,
		type: 4,
		knowledges: 5,
		difficulty: 6,
		score: 7,
	});
}

function _cSheetRow(index){
	var ix = Number(index) * 5 + 7;
	var template = '<row r="<%=ix%>" spans="1:5" x14ac:dyDescent="0.15"><c r="A<%=ix%>" t="s"><v><%=no%></v></c><c r="B<%=ix%>" t="s"><v><%=type%></v></c><c r="C<%=ix%>" t="s"><v><%=knowledges%></v></c><c r="D<%=ix%>" t="s"><v><%=difficulty%></v></c><c r="E<%=ix%>" t="s"><v><%=score%></v></c></row>';
	return _.template(template)({
		ix: Number(index) + 3,
		no: ix+1,
		type: ix+2,
		knowledges: ix+3,
		difficulty: ix+4,
		score: ix+5,
	});
}

function _cSi(str){
	var si = '<si><t><%=str%></t><phoneticPr fontId="1" type="noConversion"/></si>';
	return _.template(si)({
		str: str
	});
}

function _table2sheet(table){

	var template = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><dimension ref="A1:E4"/><sheetViews><sheetView tabSelected="1" workbookViewId="0"><selection activeCell="E7" sqref="E7"/></sheetView></sheetViews><sheetFormatPr baseColWidth="10" defaultRowHeight="15" x14ac:dyDescent="0.15"/><sheetData><%=cuihovah%></sheetData><phoneticPr fontId="1" type="noConversion"/><pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3"/></worksheet>';
	var str = '';
	str += _cBaseInfo(table);

	var questions = _.flatten(_.pluck(table.blocks, 'questions'));

	for(var ix in questions){
		str += _cSheetRow(ix);
	}

	return _.template(template)({
		cuihovah: str
	});
}

function _table2shareString(table){
	var template = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="<%=count%>" uniqueCount="<%=count%>"><%=cuihovah%></sst>';
	var str = [];
	str.push(_cSi(table.name));
	str.push(_cSi(table.period));
	str.push(_cSi(table.subject));
	str.push(_cSi('题号'));
	str.push(_cSi('题型'));
	str.push(_cSi('知识点'));
	str.push(_cSi('难度'));
	str.push(_cSi('分数'));

	var qno = 1;
	_.each(table.blocks, function(block){
		_.each(block.questions, function(question){
			str.push(_cSi(qno));
			str.push(_cSi(question.type));
			str.push(_cSi(_.pluck(question.knowledges, 'name').join(';')));
			str.push(_cSi(question.difficulty));
			str.push(_cSi(question.score));
			qno++;
		});
	});

	return _.template(template)({
		cuihovah: str.join(''),
		count: str.length
	});

}

function _xls2table(xmlObj, orderArray){

	var objlist = _.flatten(_.pluck(xmlObj.sst.si, 't'));
	if(!orderArray)
		orderArray = _.range(objlist.length);

	var si = _.map(orderArray, function(index){
		return objlist[index];
	});

	var retobj = {
		name: si[0],
		period: si[1],
		subject: si[2]
	};

	var blocks = {};

	for(var i=8; i<si.length; i+=5){
		var name = si[i+1];
		if(!blocks[name]){
			blocks[name] = {
				name: name,
				questions: []
			};
		}

		var question = {};
		question.type = si[i+1];
		question.knowledges = si[i+2];
		question.difficulty = si[i+3];
		question.score = Number(si[i+4]);
		question.period = si[1];
		blocks[name].questions.push(question);
	}
	retobj.blocks = _.values(blocks);
	return retobj;
}

function getExcelFromTable(table, basename, done){
	var sharedStrings = _table2shareString(table);
	var sheet = _table2sheet(table);
	var dest = path.join(basename, String(Date.now()));
	enfscopy.copy(path.join(__dirname, 'xlsx'), dest, function(err, statistics){
		if(!err){
			console.log("Copied %d items with a total size of %d", statistics.items, statistics.size);
		}

		var sgFile = path.join(dest, 'xl', 'sharedStrings.xml');
		var stFile = path.join(dest, 'xl', 'worksheets', 'sheet1.xml');
		fs.writeFile(sgFile, sharedStrings, function(err){
			fs.writeFile(stFile, sheet, function(err){
				zipdir(dest, {
					saveTo: path.join(basename, table.name + '.xlsx')
				}, function (err, buffer) {
					rimraf(dest, function(err){
						done(err, null);
					});
				});
			});
		});
	});
}

function getTableFromExcel(filename, done){

	var dirname = path.join(__dirname, String(Date.now()));

	var xmlData = '';
	var xmlSheet = '';

	decompress(filename, dirname, {
		plugins: [
			decompressUnzip()
		]
	}).then((err) => {

		var sheet = fs.createReadStream(path.join(dirname, 'xl', 'worksheets', 'sheet1.xml'));
		var sharedStrings = fs.createReadStream(path.join(dirname, 'xl', 'sharedStrings.xml'));

		return fs.exists(sheet.path, function(exists){
			if(!exists)
				return done(new Error());

			return fs.exists(sharedStrings.path, function(exists){
				if(!exists)
					return done(new Error());
				sheet.on('data', function(data){
					xmlSheet += data.toString();
				});

				sheet.on('end', function(err){

					if(err)
						return done(err);

					parseString(xmlSheet, function (err, xmlSheetObj) {

						var orderArray = _.flatten(_.map(_.pluck(xmlSheetObj.worksheet.sheetData[0].row, 'c'), function(c){
							return _.flatten(_.pluck(c, 'v'));
						}));

						sharedStrings.on('data', function(data){
							xmlData += data.toString();
						});

						sharedStrings.on('end', function(){
							rimraf(dirname, function(err){
								parseString(xmlData, function (err, xmlObj) {
									var retobj = _xls2table(xmlObj, orderArray);
									return done(err, retobj);
								});
							});
						});
					});
				});
			})
		});
	});
}

function getTableFromExcelNew (fileName, call) {
	try {
		let xlsxObj = nodeExcel.parse(fileName);
		let worksheet = xlsxObj[0];

		//基础信息
		let baseInfoArr = worksheet.data[0];
		let baseInfo = {};
		baseInfo.name = baseInfoArr[0] ? baseInfoArr[0] : '';
		baseInfo.period = baseInfoArr[1] ? baseInfoArr[1] : '';
		baseInfo.subject = baseInfoArr[2] ? baseInfoArr[2] : '';
		baseInfo.type = baseInfoArr[3] ? baseInfoArr[3] : '';
		baseInfo.grade = baseInfoArr[4] ? baseInfoArr[4] : '';
		baseInfo.province = baseInfoArr[5] ? baseInfoArr[5] : '';
		baseInfo.blocks = [];

		//获取表中信息
		let blocks = {};
		for(let i = 2; i < worksheet.data.length; i ++){
			let details = worksheet.data[i];
			let typeName = details[1];
			if (!blocks[typeName]) {
				blocks[typeName] = {
					name : typeName,
					questions : []
				}
			}

			let questionObj = {};
			questionObj.type = details[1];
			questionObj.knowledges = details[2];
			questionObj.difficulty = details[3];
			questionObj.score = Number(details[4]);
			questionObj.period = baseInfo.period;
			blocks[typeName].questions.push(questionObj);
		}

		baseInfo.blocks = _.values(blocks);
		return call(null, baseInfo);
	} catch (error) {
		return call(error);
	}
}

function getExcelFromTableNew (table, basename) {
	let dataArr = [];

	//基础信息
	let baseArr = [];
	baseArr.push(table.name);
	baseArr.push(table.period);
	baseArr.push(table.subject);
	baseArr.push(table.type);
	baseArr.push(table.grade);
	baseArr.push(table.province);
	dataArr.push(baseArr);

	//表头信息
	let titleArr = [];
	titleArr.push('题号');
	titleArr.push('题型');
	titleArr.push('知识点');
	titleArr.push('难度');
	titleArr.push('分数');
	dataArr.push(titleArr);

	//blocks信息
	let count = 0;
	for (let i = 0; i < table.blocks.length; i ++) {
		let block = table.blocks[i];
		for (let j = 0; j < block.questions.length; j ++) {
			count ++;
			let question = block.questions[j];
			let blockArr = [];
			blockArr.push(count);
			blockArr.push(question.type);
			//知识点
			let knowledges = "";
			for (let know of question.knowledges) {
				if (knowledges.length > 0)
					knowledges += ';';
				knowledges += know.name;
			}
			blockArr.push(knowledges);
			blockArr.push(question.difficulty);
			blockArr.push(question.score);
			dataArr.push(blockArr);
		}
	}

	//构建excel
	let buffer = nodeExcel.build([
		{
			name : '工作表1',
			data : dataArr
		}
	]);
	let filePath = path.join(basename, table.name + '.xlsx');
	fs.writeFileSync(filePath, buffer);
}

function _getEventInfo (key) {
	let mapEvent = new Map();
	//首页
	mapEvent.set('sy_home_page', {id:'sy_home_page', disc:'首页打开次数', type:'sy'});
	mapEvent.set('sy_zrsj_click', {id:'sy_zrsj_click', disc:'最热试卷整体点击量', type:'sy'});
	mapEvent.set('sy_zxsj_click', {id:'sy_zxsj_click', disc:'最新试卷整体点击量', type:'sy'});
	mapEvent.set('sy_zj_click', {id:'sy_zj_click', disc:'首页所有专辑点击量', type:'sy'});
	//教材选题
	mapEvent.set('jcxt_home_page', {id:'jcxt_home_page', disc:'教材选题页面打开次数', type:'jcxt'});
	mapEvent.set('jcxt_ckjx_click', {id:'jcxt_ckjx_click', disc:'教材选题查看解析点击次数', type:'jcxt'});
	mapEvent.set('jcxt_jrstl_click', {id:'jcxt_jrstl_click', disc:'教材选题点击加入试题篮次数', type:'jcxt'});
	mapEvent.set('jcxt_sc_click', {id:'jcxt_sc_click', disc:'教材选题点击收藏次数', type:'jcxt'});
	//知识点
	mapEvent.set('zsdxt_home_page', {id:'zsdxt_home_page', disc:'知识点选题页面打开次数', type:'zsdxt'});
	mapEvent.set('zsdxt_ckjx_click', {id:'zsdxt_ckjx_click', disc:'知识点选题查看解析点击次数', type:'zsdxt'});
	mapEvent.set('zsdxt_jrstl_click', {id:'zsdxt_jrstl_click', disc:'知识点选题点击加入试题篮次数', type:'zsdxt'});
	mapEvent.set('zsdxt_sc_click', {id:'zsdxt_sc_click', disc:'知识点选题点击收藏次数', type:'zsdxt'});
	mapEvent.set('zsdxt_zsdbj_click', {id:'zsdxt_zsdbj_click', disc:'知识点选题点击知识点并集次数', type:'zsdxt'});
	mapEvent.set('zsdxt_zsdjj_time', {id:'zsdxt_zsdjj_time', disc:'知识点选题使用知识点交集选题次数', type:'zsdxt'});
	mapEvent.set('zsdxt_zsdbj_time', {id:'zsdxt_zsdbj_time', disc:'知识点选题使用知识点并集选题次数', type:'zsdxt'});
	mapEvent.set('zsdxt_dx_click', {id:'zsdxt_dx_click', disc:'知识点选题点击多选知识点次数', type:'zsdxt'});
	//试卷选题
	mapEvent.set('sjxt_home_page', {id:'sjxt_home_page', disc:'试卷选题页面打开次数', type:'sjxt'});
	mapEvent.set('sjxt_mbzj_click', {id:'sjxt_mbzj_click', disc:'模板组卷功能点击次数', type:'sjxt'});
	mapEvent.set('sjxt_ckxmb_click', {id:'sjxt_ckxmb_click', disc:'查看细目表功能点击次数', type:'sjxt'});
	mapEvent.set('sjxt_sjfx_click', {id:'sjxt_sjfx_click', disc:'试卷分析功能点击次数', type:'sjxt'});
	mapEvent.set('sjxt_sc_click', {id:'sjxt_sc_click', disc:'收藏功能点击次数', type:'sjxt'});
	//智能组卷
	mapEvent.set('znzj_home_page', {id:'znzj_home_page', disc:'智能组卷页面打开次数', type:'znzj'});
	mapEvent.set('znzj_zsd_click', {id:'znzj_zsd_click', disc:'按知识点智能组卷次数', type:'znzj'});
	mapEvent.set('znzj_zj_click', {id:'znzj_zj_click', disc:'按章节智能组卷次数', type:'znzj'});
	mapEvent.set('znzj_scsj_click', {id:'znzj_scsj_click', disc:'生成试卷点击次数', type:'znzj'});
	//细目表
	mapEvent.set('xmbzj_home_click', {id:'xmbzj_home_click', disc:'细目表组卷页面打开次数', type:'xmbzj'});
	mapEvent.set('xmbzj_myxmb_click', {id:'xmbzj_myxmb_click', disc:'我的细目表页签点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_more_click', {id:'xmbzj_more_click', disc:'【查看更多】按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_xzmb_click', {id:'xmbzj_xzmb_click', disc:'下载细目表模板按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_creat_click', {id:'xmbzj_creat_click', disc:'创建细目表按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_upload_click', {id:'xmbzj_upload_click', disc:'上传细目表按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_use_click', {id:'xmbzj_use_click', disc:'使用细目表按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_sc_click', {id:'xmbzj_sc_click', disc:'收藏细目表按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_download_click', {id:'xmbzj_download_click', disc:'下载细目表按键点击次数', type:'xmbzj'});
	mapEvent.set('xmbzj_scsj_click', {id:'xmbzj_scsj_click', disc:'生成试卷按键点击次数', type:'xmbzj'});
	//试题
	mapEvent.set('question_download_times', {id:'question_download_times', disc:'所有试题下载次数', type:'question'});
	mapEvent.set('question_sc_times', {id:'question_sc_times', disc:'所有试题收藏次数', type:'question'});
	//试卷
	mapEvent.set('exampaper_download_times', {id:'exampaper_download_times', disc:'所有试卷下载次数', type:'exampaper'});
	mapEvent.set('exampaper_sc_times', {id:'exampaper_sc_times', disc:'所有试卷收藏次数', type:'exampaper'});
	mapEvent.set('exampaper_click_times', {id:'exampaper_click_times', disc:'所有试卷的点击次数', type:'exampaper'});
	//登录方式
	mapEvent.set('land_web_times', {id:'land_web_times', disc:'直接登录', type:'land'});
	mapEvent.set('land_hfsjs_times', {id:'land_hfsjs_times', disc:'好分数教师端登陆', type:'land'});
	//组卷中心
	mapEvent.set('zjzx_save_click', {id:'zjzx_save_click', disc:'组卷中心-保存试卷', type:'zjzx'});
	mapEvent.set('zjzx_resave_click', {id:'zjzx_resave_click', disc:'组卷中心-另存为新卷', type:'zjzx'});
	mapEvent.set('zjzx_downcard01_click', {id:'zjzx_downcard01_click', disc:'组卷中心-下载答题卡', type:'zjzx'});
	mapEvent.set('zjzx_downcard02_click', {id:'zjzx_downcard02_click', disc:'组卷中心-下载答题卡-保存并下载', type:'zjzx'});
	mapEvent.set('zjzx_downpaper_click', {id:'zjzx_downpaper_click', disc:'组卷中心-下载试卷', type:'zjzx'});
	mapEvent.set('zjzx_paperanalyze_click', {id:'zjzx_paperanalyze_click', disc:'组卷中心-试卷分析', type:'zjzx'});
	//组卷记录
	mapEvent.set('zjjl_preview_click', {id:'zjjl_preview_click', disc:'组卷记录-预览', type:'zjjl'});
	mapEvent.set('zjjl_download_click', {id:'zjjl_download_click', disc:'组卷记录-下载', type:'zjjl'});
	mapEvent.set('zjjl_edit_click', {id:'zjjl_edit_click', disc:'组卷记录-编辑', type:'zjjl'});
	mapEvent.set('zjjl_downcard_click', {id:'zjjl_downcard_click', disc:'组卷记录-下载答题卡', type:'zjjl'});
	mapEvent.set('zjjl_delete_click', {id:'zjjl_delete_click', disc:'组卷记录-删除', type:'zjjl'});
	mapEvent.set('zjjl_preview_paperanalyze_click', {id:'zjjl_preview_paperanalyze_click', disc:'组卷记录-预览-试卷分析', type:'zjjl'});
	mapEvent.set('zjjl_preview_downpaper_click', {id:'zjjl_preview_downpaper_click', disc:'组卷记录-预览-下载试卷', type:'zjjl'});
	mapEvent.set('zjjl_preview_edit_click', {id:'zjjl_preview_edit_click', disc:'组卷记录-预览-编辑试卷', type:'zjjl'});
	//下载确认页
	mapEvent.set('xzqr_A4', {id:'xzqr_A4', disc:'下载确认页-A4', type:'xzqr'});
	mapEvent.set('xzqr_A3', {id:'xzqr_A3', disc:'下载确认页-A3(双栏)', type:'xzqr'});
	mapEvent.set('xzqr_B4', {id:'xzqr_B4', disc:'下载确认页-B4((双栏))', type:'xzqr'});
	mapEvent.set('xzqr_answer', {id:'xzqr_answer', disc:'下载确认页-答案', type:'xzqr'});
	mapEvent.set('xzqr_point', {id:'xzqr_point', disc:'下载确认页-考点', type:'xzqr'});
	mapEvent.set('xzqr_analyze01', {id:'xzqr_analyze01', disc:'下载确认页-解析', type:'xzqr'});
	mapEvent.set('xzqr_comment', {id:'xzqr_comment', disc:'下载确认页-点评', type:'xzqr'});
	mapEvent.set('xzqr_analyze02', {id:'xzqr_analyze02', disc:'下载确认页-解答', type:'xzqr'});
	mapEvent.set('xzqr_resourse', {id:'xzqr_resourse', disc:'下载确认页-试题来源', type:'xzqr'});
	mapEvent.set('xzqr_teacher', {id:'xzqr_teacher', disc:'下载确认页-用途-教师用卷', type:'xzqr'});
	mapEvent.set('xzqr_student', {id:'xzqr_student', disc:'下载确认页-用途-学生用卷', type:'xzqr'});
	mapEvent.set('xzqr_normal', {id:'xzqr_normal', disc:'下载确认页-用途-普通用卷', type:'xzqr'});
	mapEvent.set('xzqr_OK_click', {id:'xzqr_OK_click', disc:'下载确认页-确认并下载', type:'xzqr'});
	//搜索页
	mapEvent.set('search_knowledge_click', {id:'search_knowledge_click', disc:'试题搜索点击知识点次数', type:'search'});
	mapEvent.set('search_question_times', {id:'search_question_times', disc:'试题搜索次数', type:'search'});
	mapEvent.set('search_exampaper_times', {id:'search_exampaper_times', disc:'试卷搜索次数', type:'search'});
	//推荐专辑
	mapEvent.set('tjzj_home_page', {id:'tjzj_home_page', disc:'推荐专辑页-页面打开次数', type:'tjzj'});
	mapEvent.set('tjzj_zj_click', {id:'tjzj_zj_click', disc:'推荐专辑页-点击所有专辑次数', type:'tjzj'});
	mapEvent.set('tjzj_sc_click', {id:'tjzj_sc_click', disc:'推荐专辑页-专辑收藏次数', type:'tjzj'});

	return mapEvent;
}

const getPvPcExcelFromTable = (accessSpots, basename) => {
	// console.log(accessSpots);
	let dataArr = [];
	let mapEvent = _getEventInfo(accessSpots);

	//基础信息
	let baseArr = [];
	baseArr.push('事件ID');
	baseArr.push('事件描述');
	baseArr.push('PV');
	baseArr.push('UV');
	dataArr.push(baseArr);

	//PV + UV
	let mapPvUv = new Map();
	let events = mapEvent.keys();
	let accessArr = accessSpots;
	for (let event of events) {
		for (let access of accessArr) {
			if (access.event_id === event) {
				if (mapPvUv.has(event)) {
					let obj = mapPvUv.get(event);
					obj.pv.push(access.event_id);
					obj.uv.add(access.user_id);
					mapPvUv.set(event, obj);
				} else {
					let pv = [];
					pv.push(access.event_id);
					let uv = new Set();
					uv.add(access.user_id);
					let obj = {};
					obj.id = event;
					obj.pv = pv;
					obj.uv = uv;
					mapPvUv.set(event, obj);
				}
			}
		}
	}

	for (let val of mapPvUv.values()) {
		let arrPvUv = [];
		arrPvUv.push(val.id);
		arrPvUv.push(mapEvent.get(val.id).disc);
		arrPvUv.push(val.pv.length);
		arrPvUv.push(val.uv.size);
		dataArr.push(arrPvUv);
	}

	//构建excel
	let buffer = nodeExcel.build([
		{
			name : '工作表1',
			data : dataArr
		}
	]);
	let filePath = path.join(basename, '题库埋点事件PVUV统计表.xlsx');
	fs.writeFile(filePath, buffer, function(err){
		if (err) {
			console.log(err);
		}
	});
}

const getPvPcDataFromTable = (accessSpots, type) => {
	// console.log(accessSpots);
	let dataArr = [];
	let mapEvent = _getEventInfo(accessSpots);

	//基础信息
	let baseArr = [];
	baseArr.push('事件ID');
	baseArr.push('事件描述');
	baseArr.push('PV');
	baseArr.push('UV');
	//不要表头
	// dataArr.push(baseArr);

	//PV + UV
	let mapPvUv = new Map();
	let events = mapEvent.keys();
	let accessArr = accessSpots;
	for (let event of events) {
		for (let access of accessArr) {
			if (access.event_id === event) {
				if (mapPvUv.has(event)) {
					let obj = mapPvUv.get(event);
					obj.pv.push(access.event_id);
					obj.uv.add(access.user_id);
					mapPvUv.set(event, obj);
				} else {
					let pv = [];
					pv.push(access.event_id);
					let uv = new Set();
					uv.add(access.user_id);
					let obj = {};
					obj.id = event;
					obj.pv = pv;
					obj.uv = uv;
					mapPvUv.set(event, obj);
				}
			}
		}
	}

	for (let val of mapPvUv.values()) {
		//返回数组
		// let arrPvUv = [];
		// arrPvUv.push(val.id);
		// arrPvUv.push(mapEvent.get(val.id).disc);
		// arrPvUv.push(val.pv.length);
		// arrPvUv.push(val.uv.size);
		// dataArr.push(arrPvUv);

		//返回对象
		let objPvUv = {};
		objPvUv.id = val.id;
		objPvUv.disc = mapEvent.get(val.id).disc;
		objPvUv.pv = val.pv.length;
		objPvUv.uv = val.uv.size;

		if (type === 'all' || type === mapEvent.get(val.id).type)
			dataArr.push(objPvUv);
	}

	return dataArr;
};

module.exports = {
	// getExcelFromTable: getExcelFromTable,
	// getTableFromExcel: getTableFromExcel
	getExcelFromTable: getExcelFromTableNew,
	getTableFromExcel: getTableFromExcelNew,
	getPvPcExcelFromTable,
	getPvPcDataFromTable,
};
