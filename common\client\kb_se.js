const config = require('config');
const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const _ = require('lodash');
const server = config.get('se_kb_server');
const logger = require('../lib/logger');

module.exports = {
    filterExampapers,
}

async function filterExampapers(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/se_kb/v2/filter/exampapers',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    try {
        const result = await axios.post(url, params);
        if (!result.data || result.data.code !== 0) {
            return [];
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error('获取知识点相关试卷异常：', e);
        return [];
    }

    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    logger.info('教材获取成功:', Date.now());
    return result.data;
}
