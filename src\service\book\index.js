const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/book');


module.exports = {
    getBooks,
    getDetails,
    chapterRecommendPapers,
}


const JOI_GET_ALL = Joi.object({
    period: Joi.string().optional(),
    subject: Joi.string().optional(),
});

async function getBooks(req, res) {
    const params = await JOI_GET_ALL.validateAsync(req.query);
    return await model.getBooks(params);
}

const JOI_GET_DETAIL = Joi.object({
    book_id: Joi.number().required(),
    type: Joi.string().optional(),
    fields_type: Joi.string().optional(),
});


async function getDetails(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(_.assign(req.params, req.query));
    return await model.getBookDetail(params);
}

const JOI_CHAPTER_PAPERS = Joi.object({
    book_id: Joi.number().optional(),
    chapter_id: Joi.number().optional(),
    period: Joi.string().when('book_id', {
        is: Joi.exist(),
        then: Joi.optional().allow(''),
        otherwise: Joi.string().required()
    }),
    subject: Joi.string().when('book_id', {
        is: Joi.exist(),
        then: Joi.optional().allow(''),
        otherwise: Joi.string().required()
    }),
    grade: Joi.string().required(),
    term: Joi.string().optional().allow(''),
    type: Joi.string().optional().allow(''),
});

async function chapterRecommendPapers(req, res) {
    const params = await JOI_CHAPTER_PAPERS.validateAsync(req.body);
    return await model.chapterRecommendPapers(params, req.user);
}

