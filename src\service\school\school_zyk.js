const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/school/school_zyk');
//school_reference_books
module.exports = {
    getReferenceBooks,
    getExampaperList
}
const GET_BOOK_LIST = Joi.object({
    school_id: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional().allow(null).allow('')
});

async function getReferenceBooks(req, res) {
    const params = await GET_BOOK_LIST.validateAsync(req.query);
    return await model.getReferenceBooks(params);
}

const GET_EXAMPAPER_LIST = Joi.object({
    press_version: Joi.string().optional().allow(''),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional().allow(''),
    reference_book_id: Joi.string().optional().allow(''),
    type: Joi.string().optional().allow(''),
    school_id: Joi.number().required(),
    sort_by: Joi.string().optional().default('ctime'),
    sort_order: Joi.number().optional().default(-1),
    offset: Joi.number().required(),
    limit: Joi.number().required()
});

async function getExampaperList(req, res) {
    const params = await GET_EXAMPAPER_LIST.validateAsync(req.query);
    return await model.getExampaperList(params);
}


