const _ = require('lodash');
const client = require('../../../common/client');
const enums = require('../../../common/enums/enums');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const utils = require('../../../common/utils/utils');
const BussError = require('../../../common/exceptions/BussError');
const paper_utils = require('../../../common/utils/paper_utils');
const zykModel = require('../zyk');
const questionModel = require('../question');
const moment = require('moment');

module.exports = {
    getCategories,
    search,
    getFilters,
    getDetail,
    download,
    downloadByContent,
}

async function getCategories(params) {
    const { period, subject, all } = params;
    const categorys = await client.kb.getPaperCategories(period, subject);
    let filters = await client.kb.getPaperFilters(period, subject);
    filters = _.mapValues(_.keyBy(filters, 'period'), 'grades');
    // filters = _.object(_.pluck(filters, 'period'), _.pluck(filters, 'grades'));
    if (!!all === true) {
        loadCategoryFiltersAll(categorys, filters);
    } else {
        loadCategoryFiltersSimple(categorys, filters);
    }
    return categorys;
}


// 完整的筛选结构
const loadCategoryFiltersAll = (categorys, filters) => {
    for (let ix in filters) {
        if (filters.hasOwnProperty(ix)) {
            // filters[ix] = _.object(_.map(filters[ix], 'grade'), _.map(filters[ix], 'type'));
            filters[ix] = _.mapValues(_.keyBy(filters[ix], 'grade'), 'type');
        }
    }
    let category = categorys.category;
    for (let px in category.children) {
        if (category.children.hasOwnProperty(px)) {
            let period = category.children[px];
            for (let sx in period.children) {
                if (period.children.hasOwnProperty(sx)) {
                    let subject = period.children[sx];
                    for (let pv in subject.children) {
                        if (subject.children.hasOwnProperty(pv)) {
                            let pressVersion = subject.children[pv];
                            for (let gx in pressVersion.children) {
                                if (pressVersion.children.hasOwnProperty(gx)) {
                                    let grade = pressVersion.children[gx];
                                    grade.children = [{
                                        key: 'type',
                                        name: '全部',
                                    }];
                                    delete grade.id;
                                    for (let tx in filters[period.name][grade.name]) {
                                        if (filters[period.name][grade.name].hasOwnProperty(tx)) {
                                            let type = filters[period.name][grade.name][tx];
                                            grade.children.push({
                                                key: 'type',
                                                name: type
                                            });
                                        }
                                    }
                                }
                            }
                            pressVersion.children[0].name = '全部';
                            pressVersion.children[0].children = _.uniqBy(_.flatten(_.map(pressVersion.children, 'children')), (x) => x.name);

                        }
                    }
                    subject.children.unshift({
                        key: 'press_version',
                        name: '全部',
                        children: []
                    });
                    subject.children[0].children = _.uniqBy(_.flatten(_.map(subject.children, 'children')), (x) => x.name);
                }
            }
        }
    }
}

// 并不是完整的筛选结构是简单的，少了一层
const loadCategoryFiltersSimple = (categorys, filters) => {
    for (let ix in filters) {
        if (filters.hasOwnProperty(ix)) {
            // filters[ix] = _.object(_.map(filters[ix], 'grade'), _.map(filters[ix], 'type'));
            filters[ix] = _.mapValues(_.keyBy(filters[ix], 'grade'), 'type');
        }
    }
    let category = categorys.category;
    for (let px in category.children) {
        if (category.children.hasOwnProperty(px)) {
            let period = category.children[px];
            for (let sx in period.children) {
                if (period.children.hasOwnProperty(sx)) {
                    let subject = period.children[sx];
                    subject.children = _.uniqBy(_.flatten(_.map(subject.children, 'children')), x => x.name);
                    for (let gx in subject.children) {
                        if (subject.children.hasOwnProperty(gx)) {
                            let grade = subject.children[gx];
                            grade.children = [];
                            delete grade.id;
                            for (let tx in filters[period.name][grade.name]) {
                                if (filters[period.name][grade.name].hasOwnProperty(tx)) {
                                    let type = filters[period.name][grade.name][tx];
                                    grade.children.push({
                                        key: 'type',
                                        name: type
                                    });
                                }
                            }
                        }
                    }
                    let all = (list => {
                        let all = _.uniqBy(_.flatten(_.map(list, 'children')), (x) => x.name);
                        all.push({
                            key: 'type',
                            name: '其他'
                        });
                        return all;
                    })(subject.children);
                    subject.children.shift();
                    // ---
                    for (let gx in subject.children) {
                        if (subject.children.hasOwnProperty(gx)) {
                            let grade = subject.children[gx];
                            grade.children = all;
                        }
                    }

                }
            }
        }
    }
}

async function search(params) {
    const {provinces} = params;
    if (_.size(provinces)) params.province = provinces[0].name; //.map(e => e.name).join(',')
    return await client.kb.exampaperBySearch(params);
}

async function getFilters(params) {
    return await client.kb.exampaperFilters(params);
}

async function getDetail(params) {
    const { id, from = enums.PaperFrom.SYS } = params;
    //
    let paper = {};

    if (from === enums.PaperFrom.SYS) { // 系统试卷
        paper = await client.kb.getPaperById(params.id);
        if (!_.isEmpty(paper)) paper.source = enums.PaperSourceType.SYS;
    } else if (from === enums.PaperFrom.GROUP || from === enums.PaperFrom.EXAM) { // 用户组卷、考后巩固
        paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(id)});
    } else if (from === enums.PaperFrom.USER_PREP) { // 备课资源试卷
        const user_prep_resource = await db.collection(schema.user_prep_resource).findOne({_id: new ObjectId(id)});
        if (!_.isEmpty(user_prep_resource)) {
            if (user_prep_resource.type_id === enums.ResourceType.homework.type_id) { // 作业试卷
                paper = await client.kb.getPaperById(user_prep_resource.from_id);
                if (!_.isEmpty(paper)) paper.source = enums.PaperSourceType.SYS;
            } else if (user_prep_resource.type_id === enums.ResourceType.user_paper.type_id) { // 试卷
                paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(user_prep_resource.from_id)});
            }
        }
    } else if (from === enums.PaperFrom.ZYK) { // 校本
        paper = await db.zyk_collection(schema.zyk.exampapers).findOne({_id: new ObjectId(id)});
        if (!_.isEmpty(paper)) paper.source = enums.PaperSourceType.ZYK;
        for (const volume of paper.volumes) {
            for (const block of volume.blocks) {
                for (const q of block.questions) {
                    if (_.isNumber(q.id)) continue;
                    q.source = enums.QuestionSource.ZYK;
                    q.source_id = q.id;
                }
            }
        }
    } else if (from === enums.PaperFrom.JYZY) { // 校验平台集备作业
        return await _getChapterLessonHomework(id)
    }
    if (_.isEmpty(paper)) return {};
    if (paper._id && typeof paper._id === 'object') paper.id = paper._id.toString();

    // const result = paper_utils.init();
    const fields = [
        'id', 'name', 'period', 'grade', 'subject', 'type', 'from_year', 'to_year', 'press_version', 'province', 'city',
        'year', 'to_year', '', 'view_times', 'download_times',
        'subtitle', 'score', 'duration', 'paper_info', 'cand_info', 'attentions', 'secret_tag', 'gutter',
        'template', 'source', 'source_id', 'status', 'exam_status', 'volumes'
    ];
    let result = _.pick(paper, fields);
    if (paper.ctime) result.ctime = moment(paper.ctime).valueOf();
    if (paper.utime) result.utime = moment(paper.utime).valueOf();
    // 根据试题ID构建试卷格式
    const kb_ids = [], zyk_ids = [], zx_ids = [], ques_map = {};
    if (_.size(paper.volumes)) {
        for (const volume of paper.volumes) {
            for (const block of volume.blocks) {
                for (const q of block.questions) {
                    if (!q) continue;
                    // paper_utils.insert_questions(result, q);
                    if (q.source === enums.QuestionSource.ZX) {
                        zx_ids.push(q.id);
                    } else if (q.source === enums.QuestionSource.ZYK) {
                        zyk_ids.push(q.id);
                    }  else if (q.source === enums.QuestionSource.UPLOAD) {
                        ques_map[q.id] = q;
                    } else { // 系统
                        if (_.isNumber(q.id)) kb_ids.push(q.id);
                        // // 上传数据异常block.questions 没有 source，默认为 zx题目
                        // else zx_ids.push(q.id);
                    }
                }
            }
        }
        if (_.size(kb_ids)) {
            const list = await client.kb.getQuestionByIds(kb_ids);
            list.forEach(q => ques_map[q.id] = q);
        }
        if (_.size(zyk_ids)) {
            const list = await zykModel.getQuestionByIds(zyk_ids);
            list.forEach(q => ques_map[q.id] = q);
        }
        if (_.size(zx_ids)) {
            const list = await questionModel.getByIds(zx_ids);
            list.forEach(q => ques_map[q.id] = q);
        }
        paper_utils.traverse_questions(result, ques_map);
    } else {
        result = paper_utils.init();
        _.assign(result, _.pick(paper, fields));
        for (const block of paper.blocks) {
            for (const ques of block.questions) {
                ques_map[ques.id] = ques;
                ques.source = enums.QuestionSource.SYS;
                ques.source_id = enums.QuestionSource.SYS;
                paper_utils.insert_questions(result, ques);
            }
        }
        paper_utils.traverse_questions(result, ques_map);
        paper_utils.render_basket(result);
    }
    paper_utils.ques_process(result);
    result.from_enum = from;
    return result;
}

async function _getChapterLessonHomework(lesson_id) {
    let teamPrepDatas = await db.jzl_collection(schema.team_prep_data).find({
        edu_plan_chapter_id: lesson_id,
        key: 'homework',
        deleted: enums.BOOL.NO
    }).sort({updatedAt: -1}).toArray();
    let teamPrepIds = teamPrepDatas.map(e => new ObjectId(e.team_prep_id))
    const teamPreps = await db.jzl_collection(schema.team_prep).find({
        _id: {$in: teamPrepIds},
        deleted: enums.BOOL.NO,
        status: 'done'
    }).sort({updatedAt: -1}).limit(1).toArray();
    let teamPrep = teamPreps[0]
    if (!teamPrep) throw new BussError('未完成集备');
    let teamPrepData = teamPrepDatas.find(e => e.team_prep_id === teamPrep._id.toString())
    const plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(teamPrep.edu_plan_id)});
    if (_.isEmpty(plan)) throw new BussError('教学计划不存在');
    // 插入试题
    const kb_ids = [];
    const zx_ids = [];
    const zyk_ids = [];
    const ques_map = {};
    for (const question of teamPrepData.children) {
        if (question.source === enums.QuestionSource.ZX) {
            zx_ids.push(question.id);
        } else if (question.source === enums.QuestionSource.ZYK) {
            zyk_ids.push(question.id);
        } else {
            if (_.isNumber(question.id)) kb_ids.push(question.id);
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestionByIds(kb_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const questions = await questionModel.getByIds(zx_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zyk_ids)) {
        const questions = await zykModel.getQuestionByIds(zyk_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    const lesson_node = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(lesson_id)});
    const basket = paper_utils.init();
    basket.name = `第${utils.numberToChinese(lesson_node.index || 1)}课时 ${lesson_node.name}作业`;
    basket.period = plan.period;
    basket.subject = plan.subject;
    basket.grade = plan.grade;
    basket.type = '同步练习';
    const root_chapter_id = utils.findRootId(plan.children, lesson_id);
    const root_chapter = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(root_chapter_id)});
    if (root_chapter.key === enums.EduPlanChapterKey.EXAM) {
        basket.type = enums.EduPlanExamTypeMapping[root_chapter.name] || root_chapter.name;
    }
    for (const q of teamPrepData.children) {
        let question = ques_map[q.id];
        if (_.isEmpty(question)) continue;
        const ques = _.assign({}, question);
        ques.source = q.source;
        ques.source_id = q.source_id;
        if (q.score) ques.score = q.score;
        paper_utils.insert_questions(basket, ques);
    }
    paper_utils.traverse_questions(basket, ques_map);
    paper_utils.render_basket(basket);
    return basket;
}

async function download(req, res, params) {
    const paper = await getDetail(params);
    const doc = _.assign({}, paper);
    const blocks = [];
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            if (!_.size(block)) continue;
            blocks.push(block);
        }
    }
    doc.blocks = blocks;
    delete doc.volumes;
    const response = await client.utilbox.downloadExampaper(doc);
    // 响应数据
    res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${paper.name}.docx`)}`);
    // res.setHeader('Content-type', fileMime);
    res.setHeader('Content-type', 'application/octet-stream');
    return response.data;
}

async function downloadByContent(req, res, params) {
    //
    const {content, filename} = params;
    const download_params = {
        html: content,
        filename: filename,
    }
    const response = await client.utilbox.getDownloadInfo(download_params);
    // 响应数据
    res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${filename}}.docx`)}`);
    // res.setHeader('Content-type', fileMime);
    res.setHeader('Content-type', 'application/octet-stream');
    return response.data;
}

//
// async function buildPaper(paper, questions) {
//     const result = paper_utils.init();
//     const fields = [
//         'id', 'name', 'period', 'grade', 'subject', 'type', 'from_year', 'to_year', 'press_version', 'province', 'city',
//         'year', 'to_year', '', 'view_times', 'download_times',
//         'subtitle', 'score', 'duration', 'paper_info', 'cand_info', 'attentions', 'secret_tag', 'gutter',
//         'template', 'source', 'source_id', 'status', 'exam_status'
//     ];
//     _.assign(result, _.pick(paper, fields));
//     if (paper.ctime) result.ctime = moment(paper.ctime).valueOf();
//     if (paper.utime) result.utime = moment(paper.utime).valueOf();
//     // 插入试题
//     for (const ques of questions) {
//         paper_utils.insert_questions(result, ques);
//     }
//     //
//     const ques_map = _.keyBy(questions, 'id');
//     paper_utils.traverse_questions(result, ques_map);
//     return result;
// }
