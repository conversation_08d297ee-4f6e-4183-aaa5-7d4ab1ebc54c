---
title: 知识树管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 知识树管理接口

知识树管理接口提供知识体系的查询和展示功能，用于构建学科知识图谱。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取知识树列表

GET /v1/knowledge_tree

获取知识树列表，支持按学段和科目筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段，如：小学、初中、高中|
|subject|query|string|否|科目，如：语文、数学、英语|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/knowledge_tree?period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 2001,
      "name": "初中数学知识树",
      "period": "初中",
      "subject": "数学",
      "version": "2024版",
      "node_count": 256,
      "max_depth": 4,
      "create_time": "2024-01-01",
      "update_time": "2024-01-15",
      "status": "已发布",
      "description": "完整的初中数学知识体系，包含代数、几何、统计等主要内容",
      "coverage": {
        "七年级": 85,
        "八年级": 92,
        "九年级": 78
      }
    },
    {
      "id": 2002,
      "name": "初中数学知识树（人教版）",
      "period": "初中",
      "subject": "数学",
      "version": "人教版2024",
      "node_count": 289,
      "max_depth": 5,
      "create_time": "2024-02-01",
      "update_time": "2024-02-10",
      "status": "已发布",
      "description": "基于人教版教材的初中数学知识体系"
    }
  ]
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|array|true|none|知识树列表|知识树信息数组|
|»» id|integer|true|none|知识树ID|唯一标识|
|»» name|string|true|none|知识树名称|显示名称|
|»» period|string|true|none|学段|小学/初中/高中|
|»» subject|string|true|none|科目|具体科目名称|
|»» version|string|true|none|版本|知识树版本号|
|»» node_count|integer|true|none|节点数量|知识点总数|
|»» max_depth|integer|true|none|最大深度|知识树层级深度|
|»» status|string|true|none|状态|发布状态|
|»» coverage|object|false|none|覆盖率|各年级知识点覆盖情况|

---

## GET 获取知识树详情

GET /v1/knowledge_tree/{knowledge_tree_id}

获取指定知识树的详细结构信息，包含完整的层级关系。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|knowledge_tree_id|path|number|是|知识树ID|
|expand_level|query|number|否|展开层级，默认全部展开|
|include_stats|query|boolean|否|是否包含统计信息，默认false|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/knowledge_tree/2001?expand_level=3&include_stats=true" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 2001,
    "name": "初中数学知识树",
    "period": "初中",
    "subject": "数学",
    "version": "2024版",
    "description": "完整的初中数学知识体系",
    "tree": {
      "id": "root",
      "name": "初中数学",
      "level": 0,
      "key": "math_junior",
      "children": [
        {
          "id": "node_001",
          "name": "数与代数",
          "level": 1,
          "key": "number_algebra",
          "order": 1,
          "description": "数与代数是数学的基础内容",
          "question_count": 1250,
          "difficulty_distribution": {
            "容易": 450,
            "中等": 600,
            "困难": 200
          },
          "children": [
            {
              "id": "node_001_001",
              "name": "有理数",
              "level": 2,
              "key": "rational_numbers",
              "order": 1,
              "grade_range": ["七年级"],
              "question_count": 356,
              "children": [
                {
                  "id": "node_001_001_001",
                  "name": "有理数的概念",
                  "level": 3,
                  "key": "rational_concept",
                  "order": 1,
                  "description": "正数、负数和零的概念",
                  "question_count": 89,
                  "keywords": ["正数", "负数", "零", "数轴"],
                  "children": []
                },
                {
                  "id": "node_001_001_002",
                  "name": "有理数的运算",
                  "level": 3,
                  "key": "rational_operations",
                  "order": 2,
                  "description": "有理数的加减乘除运算",
                  "question_count": 156,
                  "keywords": ["加法", "减法", "乘法", "除法", "混合运算"],
                  "children": []
                }
              ]
            },
            {
              "id": "node_001_002",
              "name": "实数",
              "level": 2,
              "key": "real_numbers",
              "order": 2,
              "grade_range": ["八年级"],
              "question_count": 234,
              "children": [
                {
                  "id": "node_001_002_001",
                  "name": "平方根",
                  "level": 3,
                  "key": "square_root",
                  "order": 1,
                  "question_count": 67,
                  "children": []
                }
              ]
            }
          ]
        },
        {
          "id": "node_002",
          "name": "图形与几何",
          "level": 1,
          "key": "geometry",
          "order": 2,
          "description": "平面几何和立体几何相关内容",
          "question_count": 980,
          "children": [
            {
              "id": "node_002_001",
              "name": "三角形",
              "level": 2,
              "key": "triangle",
              "order": 1,
              "grade_range": ["七年级", "八年级"],
              "question_count": 445,
              "children": []
            }
          ]
        },
        {
          "id": "node_003",
          "name": "统计与概率",
          "level": 1,
          "key": "statistics_probability",
          "order": 3,
          "description": "数据统计和概率相关内容",
          "question_count": 456,
          "children": []
        }
      ]
    },
    "statistics": {
      "total_nodes": 256,
      "level_distribution": {
        "1": 5,
        "2": 32,
        "3": 156,
        "4": 63
      },
      "grade_distribution": {
        "七年级": 89,
        "八年级": 92,
        "九年级": 75
      },
      "question_coverage": {
        "total_questions": 2856,
        "coverage_rate": 0.92
      }
    },
    "metadata": {
      "create_time": "2024-01-01",
      "update_time": "2024-01-15",
      "creator": "system",
      "last_modifier": "admin",
      "tags": ["核心知识点", "初中数学", "标准版本"]
    }
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|知识树详情|完整的知识树信息|
|»» id|integer|true|none|知识树ID|唯一标识|
|»» name|string|true|none|知识树名称|显示名称|
|»» tree|object|true|none|树结构|知识点层级结构|
|»»» id|string|true|none|节点ID|节点唯一标识|
|»»» name|string|true|none|节点名称|知识点名称|
|»»» level|integer|true|none|层级|节点在树中的层级(0为根)|
|»»» key|string|true|none|节点键|节点唯一键值|
|»»» order|integer|false|none|排序|同级节点排序|
|»»» description|string|false|none|描述|知识点详细描述|
|»»» question_count|integer|false|none|题目数量|关联的题目数量|
|»»» grade_range|array|false|none|年级范围|适用的年级列表|
|»»» children|array|true|none|子节点|下级知识点列表|
|»» statistics|object|false|none|统计信息|各种统计数据|
|»» metadata|object|false|none|元数据|创建和修改信息|

### 使用场景

- **教学设计**：教师根据知识树规划教学进度
- **学习路径**：学生制定个性化学习计划
- **知识点分析**：分析知识点覆盖和关联关系
- **试题组织**：按知识树结构组织和分类试题

### 技术特性

- **动态加载**：支持按需加载指定层级的节点
- **统计增强**：提供丰富的统计信息和分析数据
- **缓存优化**：知识树结构采用缓存机制提高性能
- **版本管理**：支持知识树的版本控制和演进

### 注意事项

- 知识树结构可能很大，建议使用expand_level参数控制返回层级
- 统计信息计算较耗时，非必要时建议设置include_stats=false
- 节点的question_count是实时统计，可能存在一定延迟
- 不同版本的知识树可能存在结构差异，请注意版本兼容性