const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const path = require('path');
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getList,
}

async function getList(params, user) {
    const { period, subject } = params;
    const nowDate = new Date();
    const cond = {
        valid_from: {
            $lte: nowDate,
        },
        valid_to: {
            $gte: nowDate
        },
        shelf_status: 2, // 已发布
        del_status: 0, // 未删除,
        // platform: 'open'
    };
    const result = {
        total: 0,
        list: []
    };
    let list = await db.tiku_collection(schema.tiku.banners).find(cond).sort({ sort_num: 1 }).toArray();
    if (!_.size(list)) return result;
    list = list.filter(it => {
        if (_.size(it.limit_period)) { // 学段
            if (!period || !it.limit_period.includes(period)) return false;
        }
        if (_.size(it.limit_subject)) { // 科目
            if (!subject || !it.limit_subject.includes(subject)) return false;
        }
        if (_.size(it.limit_school)) { // 学校
            const sch = it.limit_school.find(e => e.id === user.school_id);
            if (_.isEmpty(sch)) return false;
        }
        return true;
    })
    result.total = _.size(list);
    result.list = list.map(it => {
        return {
            id: it._id.toString(),
            name: it.name,
            img_name: it.img_name, // 图片名称(utilbox上传)
            img_url: it.img_url, // 图片地址(utilbox上传)
            href: it.href, // 跳转链接
            target: it.target, // 跳转方式(_self：默认当前页面跳转, _blank：新窗口打开 )
            interval_time: it.interval_time || 0, // 间隔时间(秒)
            desc: it.desc || '', // 描述
            size: it.size || '',  // 尺寸，large: 大 | meduim：中 | small: 小
            // platform: it.platform
        }
    });
    return result;
}




