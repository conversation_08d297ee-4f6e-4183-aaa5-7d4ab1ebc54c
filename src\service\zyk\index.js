const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/zyk');


module.exports = {
    getStudyInfo,
    getCatalogInfo,
    getBookDetail,
    getCustomResource,
}

async function getStudyInfo(req, res) {
    const params = {
        school_id: req.user.school_id
    }
    return await model.getStudyInfo(params);
}

const JOI_GET_CATALOG_INFO = Joi.object({
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
});

async function getCatalogInfo(req, res) {
    const params = await JOI_GET_CATALOG_INFO.validateAsync(req.query);
    params.school_id = req.user.school_id;
    return await model.getCatalogInfo(params);
}

const JOI_GET_BOOK_DETAIL = Joi.object({
    book_id: Joi.string().required(),
});

async function getBookDetail(req, res) {
    const params = await JOI_GET_BOOK_DETAIL.validateAsync(req.params);
    params.school_id = req.user.school_id;
    return await model.getBookDetail(params);
}

async function getCustomResource(req, res) {
    const params = {
        school_id: req.user.school_id
    }
    return await model.getCustomResource(params);
}
