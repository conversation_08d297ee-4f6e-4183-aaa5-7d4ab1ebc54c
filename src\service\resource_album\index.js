const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/resource_album');
const enums = require('../../../common/enums/enums');

module.exports = {
    getAlbumList,
    getSchoolAlbumList,
    getAlbumDetail,
    getAlbumDataDetail,
    download,
}

const JOI_GET_LIST = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    grade: Joi.string().optional().allow(''),
    type: Joi.string().optional().allow(''),
    offset: Joi.number().required(),
    limit: Joi.number().required()
});

async function getAlbumList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    params.permission = 'public';
    return await model.getAlbumList(params, req.user);
}

async function getSchoolAlbumList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    params.permission = 'private';
    return await model.getAlbumList(params, req.user);
}

const JOI_GET_ALBUM_DETAIL = Joi.object({
    id: Joi.string().required(),
});

async function getAlbumDetail(req, res) {
    const params = await JOI_GET_ALBUM_DETAIL.validateAsync(req.params);
    return await model.getAlbumDetail(params, req.user);
}

async function getSchoolList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    params.permission = 'private';
    return await model.getList(params, req.user);
}

const JOI_GET_ALBUM_DATA_DETAIL = Joi.object({
    id: Joi.string().required(), //  专辑ID
    data_id: Joi.string().required(), //  专辑数据ID
});

async function getAlbumDataDetail(req, res) {
    const params = await JOI_GET_ALBUM_DATA_DETAIL.validateAsync(req.params);
    return await model.getAlbumDataDetail(params, req.user);
}

const JOI_DOWNLOAD = Joi.object({
    id: Joi.string().required(),
    data_id: Joi.string().required()
});

async function download(req, res) {
    const params = await JOI_DOWNLOAD.validateAsync(req.body);
    return await model.download(params, req.user);
}
