
const config = require('config');
const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const _ = require('lodash');
const server = config.get('rank_server');
const logger = require('../lib/logger');

module.exports = {
    getExams,
    getExamById,
}


async function getExams(ids = []) {
    if (!_.size(ids)) return {};
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/examsIndex',
        search: qs.stringify({
            from: 1,
            isVip: 1,
            withSubPapers: 1,
            examids: ids.map(e => +e).join(',')
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return {};
    }
    logger.info('教材获取成功:', Date.now());
    return result.data || {};
}

async function getExamById(exam_id) {
    const exams = await getExams([exam_id]);
    return exams[+exam_id];
}
