const express = require('express');
const router = express.Router();
const service = require('../../service/exampaper');
const template_service = require('../../service/exampaper/paper_template');
const { handler } = require('../../../common/middlewares/request');

// 模板组卷
router.post('/knowledges/exampaper', handler(1, service.knowledgePaper));
// 试卷组卷
router.post('/:id/exampaper', handler(1, service.paperToPaper));

// 一键组卷
// 试题模板
router.get('/question/template/list', handler(1, template_service.getUserList));
router.post('/question/template', handler(1, template_service.create));
router.delete('/question/template/:template_id', handler(1, template_service.deleteById));

// 分层作业推题
router.post('/recommend/level_paper/:type', handler(1, service.recommendLevelPaper));
// // 根据学生分层信息，推荐分层共性练习
// router.post('/recommend/level_paper/by_common', handler(1, service.recommendLevelPaper));
// // 根据学生分层、薄弱知识点，推荐分层练习
// router.post('/recommend/level_paper/by_knowledge', handler(1, service.recommendLevelPaper));
// // 根据学生分层、原始试卷，推荐分层平行练习
// router.post('/recommend/level_paper/by_paper', handler(1, service.recommendLevelPaper));

// 通过试题获取试卷结构
router.post('/by_questions', handler(1, service.paperByQuestions));
module.exports = router;
