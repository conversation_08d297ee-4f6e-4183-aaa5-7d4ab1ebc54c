---
title: 用户试卷管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 用户试卷管理接口

用户试卷管理接口提供个人试卷的创建、编辑、上传解析和状态管理功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取试卷列表

GET /v1/user_paper/list

获取当前用户的试卷列表，支持多种筛选条件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段|
|subject|query|string|否|科目|
|type|query|string|否|试卷类型，允许空字符串|
|grade|query|string|否|年级，允许空字符串|
|year|query|number|否|年份|
|offset|query|number|是|偏移量，从0开始|
|limit|query|number|是|每页数量|
|status|query|string|否|试卷状态，允许空字符串|
|exam_status|query|string|否|考试状态，允许空字符串|
|source|query|string|否|来源，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user_paper/list?period=初中&subject=数学&offset=0&limit=20" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 45,
    "list": [
      {
        "id": "user_paper_001",
        "name": "七年级数学第一次月考",
        "question_num": 25,
        "grade": "七年级",
        "period": "初中",
        "subject": "数学",
        "source": "组卷",
        "type": "月考",
        "from_year": 2024,
        "to_year": 2024,
        "status": "已发布",
        "exam_status": "未开始",
        "ctime": 1705123200000,
        "utime": 1705209600000,
        "error": "",
        "from_enum": "用户组卷"
      }
    ]
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|响应数据|试卷列表数据|
|»» total|integer|true|none|总数|符合条件的试卷总数|
|»» list|array|true|none|试卷列表|试卷数组|
|»»» id|string|true|none|试卷ID|唯一标识|
|»»» name|string|true|none|试卷名称|试卷标题|
|»»» question_num|integer|true|none|题目数量|试卷包含的题目数|
|»»» status|string|true|none|试卷状态|草稿/已发布等|
|»»» exam_status|string|true|none|考试状态|未开始/进行中/已结束|
|»»» ctime|integer|true|none|创建时间|时间戳|
|»»» utime|integer|true|none|更新时间|时间戳|

---

## GET 获取试卷详情

GET /v1/user_paper/{id}

获取指定试卷的详细信息，包括完整的题目结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user_paper/user_paper_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_paper_001",
    "name": "七年级数学第一次月考",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "月考",
    "from_year": 2024,
    "to_year": 2024,
    "subtitle": "第一学期第一次月考",
    "score": 100,
    "duration": 90,
    "paper_info": "本试卷共2页，25题，满分100分，考试时间90分钟",
    "cand_info": "考生须知：请在答题卡上作答",
    "score_info": "评分说明：选择题每题4分",
    "attentions": "注意事项：保持答题卡整洁",
    "secret_tag": "普通",
    "gutter": 0,
    "template": "standard",
    "status": "已发布",
    "exam_status": "未开始",
    "volumes": [
      {
        "title": "第一部分",
        "note": "基础题部分",
        "blocks": [
          {
            "id": 1,
            "name": "选择题",
            "title": "一、选择题",
            "note": "本大题共10小题，每小题4分，共40分",
            "type": "选择题",
            "default_score": 4,
            "questions": [
              {
                "id": "q_001",
                "score": 4,
                "order": 1,
                "type": "选择题",
                "difficulty": "容易",
                "content": "下列数中，是有理数的是（）",
                "options": ["A. π", "B. √2", "C. 1/3", "D. √3"],
                "answer": "C",
                "knowledges": ["有理数"]
              }
            ]
          }
        ]
      }
    ],
    "question_num": 25,
    "ctime": 1705123200000,
    "utime": 1705209600000
  }
}
```

---

## POST 保存试卷

POST /v1/user_paper

创建或更新用户试卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|否|试卷ID，24位字符串，新建时不传|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|grade|body|string|否|年级，允许空字符串，默认空|
|name|body|string|是|试卷名称|
|type|body|string|否|试卷类型，允许空字符串，默认空|
|from_year|body|number|否|起始年份|
|to_year|body|number|否|结束年份|
|subtitle|body|string|否|副标题，默认空|
|score|body|number|否|总分，默认0|
|duration|body|number|否|考试时长（分钟），默认120|
|paper_info|body|string|否|试卷信息，默认空|
|cand_info|body|string|否|考生信息，允许空字符串，默认空|
|score_info|body|string|否|评分信息，允许空字符串，默认空|
|attentions|body|string|否|注意事项，允许空字符串，默认空|
|secret_tag|body|string|否|保密标签，允许空字符串，默认空|
|gutter|body|number|否|装订线，取值0或1，默认0|
|template|body|string|否|模板类型，默认"standard"|
|partsList|body|array|否|部分列表，字符串数组|
|volumes|body|array|是|试卷卷册，至少1个，最多3个|
|» title|body|string|否|卷册标题，允许空字符串，默认空|
|» note|body|string|否|卷册说明，允许空字符串，默认空|
|» blocks|body|array|是|题块数组|
|»» id|body|number|否|题块ID|
|»» name|body|string|否|题块名称|
|»» title|body|string|否|题块标题，允许空字符串，默认空|
|»» note|body|string|否|题块说明，允许空字符串，默认空|
|»» type|body|string|否|题块类型，允许空字符串，默认空|
|»» default_score|body|number|否|默认分值，默认0|
|»» questions|body|array|是|题目数组|
|»»» id|body|number/string|是|题目ID|
|»»» score|body|number|否|题目分值，默认0|
|status|body|string|否|试卷状态，允许空字符串|
|exam_status|body|string|否|考试状态，允许空字符串|
|category|body|number|否|分类|
|province|body|string|否|省份，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user_paper" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "name": "七年级数学单元测试",
    "type": "单元测试",
    "from_year": 2024,
    "to_year": 2024,
    "score": 100,
    "duration": 90,
    "volumes": [
      {
        "title": "主卷",
        "blocks": [
          {
            "name": "选择题",
            "title": "一、选择题",
            "note": "本大题共10小题，每小题4分，共40分",
            "type": "选择题",
            "default_score": 4,
            "questions": [
              {
                "id": "q_001",
                "score": 4
              },
              {
                "id": "q_002", 
                "score": 4
              }
            ]
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_paper_002"
  }
}
```

### 错误示例

#### 试卷不存在

```json
{
  "code": 4,
  "msg": "试卷不存在",
  "data": ""
}
```

#### 不可编辑

```json
{
  "code": 4,
  "msg": "不可编辑",
  "data": ""
}
```

---

## POST 批量保存试卷

POST /v1/user_paper/batch

批量创建多份试卷。

### 请求参数

请求体为试卷对象数组，每个对象的结构与单个保存试卷相同。

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user_paper/batch" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "period": "初中",
      "subject": "数学",
      "name": "第一单元测试",
      "volumes": [...]
    },
    {
      "period": "初中", 
      "subject": "数学",
      "name": "第二单元测试",
      "volumes": [...]
    }
  ]'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "user_paper_003",
      "name": "第一单元测试"
    },
    {
      "id": "user_paper_004", 
      "name": "第二单元测试"
    }
  ]
}
```

---

## PUT 修改试卷状态

PUT /v1/user_paper/{id}/status/{status}

修改试卷的状态。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|试卷ID|
|status|path|string|是|新状态|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/user_paper/user_paper_001/status/已发布" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_paper_001"
  }
}
```

---

## DELETE 删除试卷

DELETE /v1/user_paper/{id}

删除指定的试卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/user_paper/user_paper_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_paper_001"
  }
}
```

### 错误示例

#### 试卷已关联作业

```json
{
  "code": 4,
  "msg": "试卷已关联作业，如需删除，请先去布置作业列表撤销当前作业",
  "data": ""
}
```

---

## GET 获取答题卡网关

GET /v1/user_paper/dtk/gateway

获取答题卡系统的网关配置信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user_paper/dtk/gateway" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "gateway_url": "https://dtk.example.com",
    "access_token": "dtk_access_token_here",
    "expire_time": "2024-01-16T00:00:00Z"
  }
}
```

---

## POST 上传试卷

POST /v1/user_paper/upload

上传Word文档进行试卷解析。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|body|string|是|试卷名称|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|grade|body|string|是|年级|
|type|body|string|是|试卷类型|
|from_year|body|number|是|起始年份|
|to_year|body|number|是|结束年份|
|source_url|body|string|是|文件URL|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user_paper/upload" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "七年级数学期中考试卷",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中考试",
    "from_year": 2024,
    "to_year": 2024,
    "source_url": "https://example.com/paper.docx"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "upload_task_001"
  }
}
```

---

## POST 解析回调

POST /v1/user_paper/upload/parse/callback

接收试卷解析完成的回调通知（内部接口）。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|task_id|body|string|是|任务ID|
|data|body|object|是|解析结果数据|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "status": "SUCCESS",
    "error": "",
    "paper_id": "user_paper_005"
  }
}
```

### 错误示例

#### 未获取到试题

```json
{
  "code": 4,
  "msg": "未获取到试题",
  "data": ""
}
```

---

## GET 获取上传列表

GET /v1/user_paper/upload/list

获取用户的试卷上传任务列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段|
|subject|query|string|否|科目|
|type|query|string|否|试卷类型，允许空字符串|
|grade|query|string|否|年级，允许空字符串|
|year|query|number|否|年份|
|offset|query|number|是|偏移量|
|limit|query|number|是|每页数量|
|status|query|string|否|状态，允许空字符串|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user_paper/upload/list?offset=0&limit=20" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 15,
    "list": [
      {
        "id": "upload_task_001",
        "name": "七年级数学期中考试卷",
        "question_num": 0,
        "grade": "七年级",
        "period": "初中",
        "subject": "数学",
        "type": "期中考试",
        "from_year": 2024,
        "to_year": 2024,
        "status": "解析中",
        "tiku_paper_id": "",
        "ctime": 1705123200000,
        "utime": 1705209600000,
        "error": "",
        "from_enum": "试卷上传",
        "task_type": "UPLOAD",
        "exam_status": "",
        "source": "用户上传"
      }
    ]
  }
}
```

---

## DELETE 删除上传任务

DELETE /v1/user_paper/upload/{id}

删除指定的上传任务。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|上传任务ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/user_paper/upload/upload_task_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "upload_task_001"
  }
}
```

### 错误示例

#### 数据不存在

```json
{
  "code": 4,
  "msg": "数据不存在",
  "data": ""
}
```

---

## 业务逻辑说明

### 试卷状态管理

试卷支持多种状态：
- **草稿**: 编辑中的试卷
- **已发布**: 可以使用的试卷
- **已归档**: 不再使用的试卷

### 考试状态管理

- **未开始**: 考试尚未开始
- **进行中**: 考试正在进行
- **已结束**: 考试已经结束

### 试卷结构

试卷采用三层结构：
1. **试卷**: 顶层容器
2. **卷册**: 如第Ⅰ卷、第Ⅱ卷
3. **题块**: 按题型分组的题目集合

### 上传解析流程

1. 用户上传Word文档
2. 系统创建解析任务
3. AI服务异步解析文档
4. 通过回调接口返回解析结果
5. 系统更新任务状态

### 题目扩展机制

系统支持从多个数据源获取题目详情：
- 知识库服务
- 自建题库
- 知识云平台
- 第三方题库

### 权限控制

- 用户只能操作自己创建的试卷
- 已关联作业的试卷不能删除
- 不同状态的试卷有不同的操作权限