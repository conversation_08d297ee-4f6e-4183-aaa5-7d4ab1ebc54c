const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/table');
const enums = require('../../../common/enums/enums');


module.exports = {
    getHotTable,
    search,
    getUserTableList,
    getDetail,
    postKnowledge,
    createTable,
    updateTable,
    deleteTable,
    createPaper,
    downloadTable,
    getTableByPaper,
}

const JOI_GET_HOT = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.string().optional().allow(''), // 类型
    sort_by: Joi.string().optional().allow(''), // 排序方式
});

async function getHotTable(req, res) {
    const params = await JOI_GET_HOT.validateAsync(req.query);
    return await model.getHotTable(params);
}

const JOI_SEARCH = Joi.object({
    name: Joi.string().optional().allow(''),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
    grade: Joi.array().items(Joi.string()).optional(),
    province: Joi.array().items(Joi.string()).optional(),
    year: Joi.array().items(Joi.number()).optional(),
    type: Joi.array().items(Joi.string()).optional(),
    offset: Joi.number().optional().default(0),
    limit: Joi.number().optional().default(10),
    sort_by: Joi.array().items(Joi.object()).optional(),
});

async function search(req, res) {
    const params = await JOI_SEARCH.validateAsync(req.body);
    return await model.search(params);
}

const JOI_GET_USER_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.string().optional().allow(''), // 类型
    sort_by: Joi.string().optional().allow(''), // 排序方式
});

async function getUserTableList(req, res) {
    const params = await JOI_GET_USER_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getUserTableList(params);
}


const JOI_GET_DETAIL = Joi.object({
    table_id: Joi.string().optional(),
});

async function getDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.params);
    return await model.getDetail(params);
}

const JOI_POST_KNOWLEDGE = Joi.array().items(Joi.object({
    diff: Joi.string().required(),
    kids: Joi.array().items(Joi.number()).required(),
    num: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.string().required(),
})).required().min(1);

async function postKnowledge(req, res) {
    const params = await JOI_POST_KNOWLEDGE.validateAsync(req.body);
    return await model.postKnowledge(params);
}

const JOI_CREATE_TABLE = Joi.object({
    name: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    province: Joi.string().optional(),
    type: Joi.string().required(),
    blocks: Joi.array().items(Joi.object()).required().min(1),
    from: Joi.string().optional()
});

async function createTable(req, res) {
    const params = await JOI_CREATE_TABLE.validateAsync(_.assign(req.body, req.query));
    params.user_id = req.user.id;
    return await model.createTable(params);
}

const JOI_UPDATE_TABLE = Joi.object({
    table_id: Joi.string().required(),
    name: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    type: Joi.string().required(),
    province: Joi.string().optional(),
    blocks: Joi.array().items(Joi.object()).required().min(1)
});

async function updateTable(req, res) {
    const params = await JOI_UPDATE_TABLE.validateAsync(_.assign(req.params, req.body));
    params.user_id = req.user.id;
    return await model.updateTable(params);
}

const JOI_DELETE_TABLE = Joi.object({
    table_id: Joi.string().required(),
});

async function deleteTable(req, res) {
    const params = await JOI_DELETE_TABLE.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.deleteTable(params);
}

const JOI_CREATE_PAPER = Joi.object({
    id: Joi.string().optional(),
    name: Joi.string().optional(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    type: Joi.string().required(),
    province: Joi.string().optional(),
    blocks: Joi.array().items(Joi.object()).required().min(1),
    filtered_ques: Joi.array().items(Joi.number()),
    preference: Joi.number().default(0), // 选题偏好：0:默认无偏好，1：优先使用组卷最多的试题，2:优先使用最近录入的试题，3:优先使用本地试题
});

async function createPaper(req, res) {
    const params = await JOI_CREATE_PAPER.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.createPaper(params);
}

const JOI_DOWNLOAD_TABLE = Joi.object({
    id: Joi.string().optional(),
    name: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    type: Joi.string().required(),
    province: Joi.string().optional(),
    blocks: Joi.array().items(Joi.object()).required().min(1)
});

async function downloadTable(req, res) {
    const params = await JOI_DOWNLOAD_TABLE.validateAsync(req.body);
    return await model.downloadTable(params);
}

const JOI_GET_TABLE_BY_PAPER = Joi.object({
    id: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    from: Joi.number().optional().default(enums.PaperFrom.SYS),
});

async function getTableByPaper(req, res) {
    const params = await JOI_GET_TABLE_BY_PAPER.validateAsync(_.assign(req.params, req.query));
    params.user_id = req.user.id;
    return await model.getTableByPaper(params);
}
