const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const utils = require('../../../common/utils/utils');
const BussError = require('../../../common/exceptions/BussError');
const qs = require('querystring');
const URL = require('url');
const config = require('config');
const xkwServer = config.get('xkw_server');
const aesUtils = require('./aesUtils');
const signatureUtils = require('./signatureUtils');
const client = require('../../../common/client');
const logger = require('../../../common/lib/logger');
const template = require('../../../common/enums/template');
const TYPES = template.TYPES;
const xkwQeustionSdk = require('./xkw-xop-qbmsdk');
const parser = new xkwQeustionSdk.QuestionParserService();
const basketModel = require('../basket');

module.exports = {
    getAuthUrl,
    authCallback,
    syncBasketQuestions,
}

const XkwPinYinMapping = [{
    name: '高中',
    key: 'gz',
    subject: {
        语文: 'yw',
        数学: 'sx',
        英语: 'yy',
        物理: 'wl',
        化学: 'hx',
        生物: 'sw',
        政治: 'zz',
        历史: 'ls',
        地理: 'dl',
        信息技术: 'xxjs',
        通用技术: 'tyjs',
        日语: 'ry'
    }
}, {
    name: '初中',
    key: 'cz',
    subject: {
        语文: 'yw',
        数学: 'sx',
        英语: 'yy',
        物理: 'wl',
        化学: 'hx',
        生物: 'sw',
        政治: 'zz',
        历史: 'ls',
        地理: 'dl',
        科学: 'kx',
        历史与社会: 'lsysh',
        信息技术: 'xxjs'
    }
}, {
    name: '小学',
    key: 'xx',
    subject: {
        语文: 'yw',
        数学: 'sx',
        英语: 'yy',
        道德与法治: 'zz',
        科学: 'kx'
    }
}];




async function getAuthUrl(params) {
    const {user, unify_sid, period, subject } = params;
    const hfsTeacher = await client.hfsTeacherV3.getTeacherInfo(`unify_sid=${unify_sid}`);
    if (_.isEmpty(hfsTeacher)) throw new BussError('用户不存在');
    let xkwUser = await db.collection(schema.user_xkw_relationship).findOne({user_id: user.id});
    if (_.isEmpty(xkwUser)) {
        xkwUser = {
            user_id: user.id,
            phone: hfsTeacher.phone || hfsTeacher.account || '',
            open_id: '',
            ctime: new Date(),
            utime: new Date()
        };
        const result = await db.collection(schema.user_xkw_relationship).insertOne(xkwUser);
        xkwUser._id = result.insertedId;
    }
    const signParams = {
        client_id: xkwServer.app_key,
        // extra: aesUtils.aesEncrypt(xkwUser._id.toString(), xkwServer.app_secret),
        open_id: xkwUser.open_id ? aesUtils.aesEncrypt(xkwUser.open_id, xkwServer.app_secret) : '',
        redirect_uri: `${config.get('app.url')}/v1/xkw/auth/callback`,
        service: getServiceUrl(xkwUser.open_id || '', period, subject),
        state: xkwUser._id.toString(), // 关联用户数据标识
        timespan: aesUtils.aesEncrypt(Date.now().toString(), xkwServer.app_secret),
    }
    signParams.signature = signatureUtils.generateSignature(signParams, xkwServer.app_secret);
    return `${xkwServer.protocol}://${xkwServer.hostname}/oauth2/authorize?${qs.stringify(signParams)}`;
}

async function authCallback(req, res, params) {
    const {code, state} = params;
    const xkwUser = await db.collection(schema.user_xkw_relationship).findOne({_id: new ObjectId(state)});
    if (_.isEmpty(xkwUser)) throw new BussError('非法请求');
    if (!code) {
        await db.collection(schema.user_xkw_relationship).updateOne({_id: xkwUser._id}, {$set: {remark: '用户禁止授权', utime: new Date()}});
        return;
    }
    // 获取接口凭证
    const accessTokenParams = {
        client_id: xkwServer.app_key,
        code: code,
        redirect_uri: `${config.get('app.url')}/v1/xkw/auth/callback`,
    }
    accessTokenParams.signature = signatureUtils.generateSignature(accessTokenParams, xkwServer.app_secret);
    const data = await client.xkw.getAccessToken(accessTokenParams);
    if (_.isEmpty(data)) return;
    // 保存数据
    const {access_token} = data;
    // 完善用户数据
    const xkwProfile = await client.xkw.getUserProfile(access_token);
    if (!_.isEmpty(xkwProfile)) { // 会存在open_id不一样，所以需要每次更新
        await db.collection(schema.user_xkw_relationship).updateOne({_id: xkwUser._id}, {$set: {open_id: xkwProfile.open_id, utime: new Date()}});
    }
    const url = `${xkwServer.protocol}://${xkwServer.hostname}/login?service=${getServiceUrl(xkwProfile.open_id)}`;
    logger.info('学科网重定向地址：' + url);
    res.redirect(url); // 重定向
}

function getXkPinYin(period, subject) {
    let xkInfo = '';
    for (const p of XkwPinYinMapping) {
        if (p.name === period && p.subject[subject]) {
            xkInfo = `${p.key}${p.subject[subject]}`
        }
    }
    return xkInfo;
}

function getServiceUrl(open_id, period, subject) {
    // 2.需要在https://zjse.xkw.com/{学科简拼}后增加对应模块的拼音指定路由
    // ①章节选题：https://zjse.xkw.com/gzsx/zj0/
    // ②知识点选题：https://zjse.xkw.com/{学科简拼}/zsd0/
    // ③中考：https://zjse.xkw.com/zkzq/   高考：https://zjse.xkw.com/gkzq/
    const obj = {
        _m: config.get('app.client_domain'),
        // _openid: open_id,
        // _n: '', // ⽤户试卷⽣成成功后，合作⽅接收通知地址。可空参数，采⽤⻚⾯刷新⽅式获取⽤户试卷通知时（即_callbackmode值⾮1），必传，urlencode(utf8)
        _callbackmode: '1', // ⽤户试卷通知回调⽅式。可空参数，不传时-⻚⾯刷新加载，传1时-⻚⾯⽆刷新加载
        _pmm: '1', // 是否向⽗⻚⾯发送⼴播消息。可空参数，传1时向⽗⻚⾯发送⼴播，且_callbackmode传1时有效
    }
    if (open_id) obj._openid = open_id;
    let host = xkwServer.service;
    if (period && subject) {
        const xkInfo = getXkPinYin(period, subject);
        if (xkInfo) host += `/${xkInfo}/zj0`
    }
    const url = URL.format({
        host: host,
        search: qs.stringify(obj)
    });

    return url;
}

async function syncBasketQuestions(params) {
    const { user_id, open_id, paper_id } = params;
    const headers = getHeaderParams();
    const query = {
        id: paper_id,
        user_id: open_id,
        formula_pic_format: 'svg'
    }
    headers['Xop-Sign'] = signatureUtils.generateApiSignature('/xopqbm/zj-saas/users/download-papers/details', headers, query);
    const xkwPaper = await client.xkw.getDownloadPaperDetail(headers, query);
    if (_.isEmpty(xkwPaper)) throw new BussError('获取试卷详情失败');
    // 学段科目信息
    const coursesHeaders = getHeaderParams();
    const coursesQuery = { id: xkwPaper['course_id'] };
    coursesHeaders['Xop-Sign'] = signatureUtils.generateApiSignature('/xopqbm/courses', coursesHeaders, coursesQuery);
    const courses = await client.xkw.getCourses(coursesHeaders, coursesQuery);
    const periods = ['小学', '初中', '高中', '中职'];
    const subjects = ['语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '信息技术', '通用技术', '历史与社会', '信息科技', '道德与法治'];
    const period = periods.find(e => {
        if (courses.name.includes(e)) return e;
    });
    const subject = subjects.find(e => {
        if (courses.name.includes(e)) return e;
    });
    if (!period || !subject) throw new BussError(`错误的学段科目`);
    // 解析试卷试题添加到试题篮
    const xkw_questions = [], questions_kpoint_ids = [];
    for (const body of xkwPaper.body) {
        for (const part_body of body.part_body) {
            const ques_type = part_body.type.name;
            for (const ques of part_body.questions) {
                ques.type = ques_type;
                questions_kpoint_ids.push(...ques['kpoint_ids']); // 知识点
                xkw_questions.push(ques);
            }
        }
    }
    if (!_.size(xkw_questions)) return;
    // 查询知识点
    const knowledgeHeaders = getHeaderParams();
    const knowledgeQuery = {
        ids: [...new Set(questions_kpoint_ids)].join(','),
    };
    knowledgeHeaders['Xop-Sign'] = signatureUtils.generateApiSignature('/xopqbm/knowledge-points', knowledgeHeaders, knowledgeQuery);
    const xkwKnowledges = await client.xkw.getKnowledgePoints(knowledgeHeaders, knowledgeQuery);
    const xkwKnowledgesNames = xkwKnowledges.map(e => e.name);
    let kbKnowledges = await client.kb.getKnowledgesByNames(xkwKnowledgesNames.join(';'));
    kbKnowledges = kbKnowledges.filter(e => e.period === period && e.subject === subject);
    if (!_.isEmpty(kbKnowledges)) {
        for (let xkwKnow of xkwKnowledges) {
            // 处理学科网知识点名称
            const kbKnow = kbKnowledges.find(e => e.name === xkwKnow.name);
            if (kbKnow) {
              xkwKnow.kbId = kbKnow.id;
            }
        }
    }
    const xkwKnowledgeMap = _.keyBy(xkwKnowledges, 'id');
    const questions = [];
    for (const ques of xkw_questions) {
        questions.push(transformQuestions(user_id, period, subject, ques, xkwKnowledgeMap));
    }
    // 同步到数据库
    const newQuestions = [];
    const basketQuestions =  await basketModel.getQuestions(user_id, period, subject);
    const user_questions = await db.collection(schema.user_question).find({user_id: user_id, source: enums.QuestionSource.XKW, source_id: {$in: questions.map(e => e.source_id)}}).toArray();
    for (const ques of questions) {
        let userQues = user_questions.find(e => e.source_id === ques.source_id);
        if (_.isEmpty(userQues)) {
            const insert = await db.collection(schema.user_question).insertOne(ques);
            ques._id = insert.insertedId;
        } else {
            const upDoc = _.assign({}, ques);
            delete upDoc.ctime;
            delete upDoc.user_id;
            delete upDoc.source;
            delete upDoc.source_id;
            upDoc.valid = enums.BOOL.YES;
            await db.collection(schema.user_question).updateOne({_id: userQues._id}, {$set: upDoc});
            ques._id = userQues._id;
        }
        userQues = ques;
        // 检查是否在试题篮
        const basketExists = basketQuestions.find(e => e.source === enums.QuestionSource.ZX && e.source_id === userQues._id.toString());
        if (!basketExists) {
            newQuestions.push({
                id: userQues._id.toString(),
                type: userQues.type,
                period: userQues.period,
                subject: userQues.subject,
                source: enums.QuestionSource.ZX,
                source_id: userQues._id.toString()
            });
        }
    }
    // 添加到试题篮
    let basket = null;
    if (_.size(newQuestions)) {
        basket = await basketModel.post_questions({user_id: user_id, period, subject, questions: newQuestions});
    } else {
        basket = await basketModel.get_questions({user_id: user_id, period, subject});
    }
    // 返回
    return { period, subject, questions: basket};
}
// 学科网难度映射
const XkwQuestionDiffMap = {
    '容易': 1,
    '较易': 2,
    '一般': 3,
    '较难': 4,
    '困难': 5
}

function transformQuestions(user_id, period, subject, xkwQuestion, knowledgeMap) {
    const xkType = xkwQuestion.type;
    const tkType = getQuestionType(subject, xkType);
    const blockType = getBlockType(tkType);
    const type = TYPES[tkType] || TYPES['default'];
    const question = {
        type: tkType,
        period: period,
        subject: subject,
        score: type.default_score,
        description: '', // 公共题干
        comment: '', // 点评-点睛
        blocks: {
            stems: [], // 题干信息
            answers: [], // 答案
            explanations: [], // 解析 - 分析
            solutions: [], // 解答 - 详解
            types: [blockType], // 内部类型，数量等于小题数量
            knowledges: [], // 知识点 - 根据小题来二维数组
            core_knowledges: [], //
        },
        knowledges: [], // 知识点
        refer_exampapers: [],
        difficulty: XkwQuestionDiffMap[xkwQuestion['difficulty_level'].name],
        source: enums.QuestionSource.XKW,
        source_id: xkwQuestion.id.toString(),
        user_id: user_id,
        valid: enums.BOOL.YES,
        ctime: new Date(),
        utime: new Date()
    };

    // 处理题干
    const xkwStem = parser.splitStem(xkwQuestion.stem);
    // 设置试题的公共题干
    question.description = xkwStem.html;
    if (xkwStem.og && _.size(xkwStem.og)) { // 选择题
        if (xkwStem.og.ogOps) {
            const stem = {
                stem: xkwStem.html,
                options: {}
            };
            for (const option of xkwStem.og.ogOps) {
                stem.options[option.index] = option.html;
            }
            question.blocks.stems.push(stem);
        }
    } else if (xkwStem.sqs && _.size(xkwStem.sqs)) { // 复合题存在
        question.blocks.types = []; // 使用内部
        for (const sq of xkwStem.sqs) { // 复合题存在(小题只包含选择题跟填空题), 填空不存在选项所有只有小题题干
            const stem = {
                stem: sq.html
            }
            if (sq.og && _.size(sq.og)) { // 小题是选择题
                stem.options = {};
                for (const option of sq.og.ogOps) {
                    stem.options[option.index] = option.html;
                }
            }
            question.blocks.stems.push(stem);
            // 小题题型
            const tkSpType = getQuestionType(subject, sq.type);
            const blockSpType = getBlockType(tkSpType);
            question.blocks.types.push(blockSpType);
        }
    } else { // 填空
        question.blocks.stems.push({
            stem: xkwStem.html
        });
    }
    if (question.blocks.stems.length === 1) question.description = '';

    // 答案处理 根据内部题型处理
    const answer = parser.splitAnswer(xkwQuestion.answer);
    // 多选和填空是二维数组(判断题内部是填空题), 选择和解答是一维数组
    for (const index in answer.anSqs) {
        const anSq = answer.anSqs[index];
        const stemBlockType = question.blocks.types[index]; // 小题类型
        for (const an of anSq.ans) {
            if (stemBlockType === '填空题') {
                if (question.type === '判断题') { // 适配
                    if (an.html.includes('√')) {
                        question.blocks.answers.push(["正确"]); // 正确
                    } else {
                        question.blocks.answers.push(["${\\times }$"]); // 错误
                    }
                } else{
                    if (!question.blocks.answers[index]) question.blocks.answers[index] = [];
                    question.blocks.answers[index].push(an.html);
                }
            } else if (stemBlockType === '多选题') {
                question.blocks.answers.push(Array.from(an.html));
            } else {
                question.blocks.answers.push(an.html);
            }
        }
    }
    // 解析解答
    question.blocks.solutions = question.blocks.stems.map(e => '');
    question.blocks.explanations = question.blocks.stems.map(e => '');
    const explanation = parser.splitExplanation(xkwQuestion.explanation);
    for (const exp of  explanation.explanationSegs || []) {
        if (exp.name === '分析') { // 解析
            question.blocks.explanations[0] += exp.html;
        } else if (exp.name === '详解' || exp.name.includes('题详解')) { // 解答
            question.blocks.solutions[0] += exp.html;
        }
        // else if (exp.name.test(/\(\d+\)题详解/)) { // 小题解答
        //     // const match = exp.name.match(/\(\d+\)题详解/);
        //     // question.blocks.solutions[index] = exp.html;
        // }
        else if (exp.name === '点睛') { // 点评
            question.comment += exp.html;
        } else { // 获取其他名字
            question.blocks.explanations[0] += exp.html;
        }
    }

    // 处理知识点
    const knowledges = [];
    for (const id of xkwQuestion.kpoint_ids || []) {
        const know = knowledgeMap[id];
        if (!know) continue;
        if (know.kbId) { // kb知识点存在
            knowledges.push({
                id: know.kbId,
                name: know.name,
            });
        } else { // kb知识点不存在
            knowledges.push({
                id: know.id,
                name: know.name,
                source: enums.QuestionSource.XKW
            });
        }
    }
    question.knowledges = knowledges;
    for (const index in question.blocks.stems) {
        question.blocks.knowledges[index] = knowledges;
    }
    return question;
}

function getHeaderParams() {
    return {
        'Xop-App-Id': config.get('xkw_api_server.app_key'), // 字符串，应用的AppId
        'Xop-Timestamp': utils.getTimestampSeconds(), // 请求发起的时间戳（秒）
        'Xop-Sign': '', // 接口参数签名
        'Xop-Nonce': utils.getUUid(), // 每次请求的防止重放攻击的随机串
    };
}

/**
 * 根据学科网试题类型获取题库试题类型
 * @param subject
 * @param xkwType
 * @returns {string}
 */
function getQuestionType(subject, xkwType) {
    let type = '';
    const subjectInfo = enums.TkXkwTypeMap[subject];
    if (!subjectInfo) return type;
    for (const key in subjectInfo) {
        const types = subjectInfo[key] || [];
        if (types.includes(xkwType)) {
            type = key;
            break;
        }
    }
    return type;
}

/**
 * 根据试题类型获取试题内部类型
 * @param type 题库试题类型
 * @return {string}
 */
function getBlockType(type) {
    let blockType = '';
    for (const key in enums.TypeInOutMap) {
        const types = enums.TypeInOutMap[key];
        if (types.includes(type)) {
            blockType = key;
            break;
        }
    }
    return blockType;
}





