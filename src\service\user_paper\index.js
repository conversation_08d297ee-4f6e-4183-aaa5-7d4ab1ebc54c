const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/user_paper');
const enums = require('../../../common/enums/enums');


module.exports = {
    get_list,
    get_detail,
    post_paper,
    batch_post_paper,
    delete_paper,
    getDtkGateway,
    update_paper_status,
    upload_paper,
    parse_callback,
    get_upload_list,
    delete_upload_by_id,
}


const JOI_GET_LIST = Joi.object({
    // user_id: Joi.string().required(),
    period: Joi.string().optional(),
    subject: Joi.string().optional(),
    type: Joi.string().optional().allow(''),
    grade: Joi.string().optional().allow(''),
    year: Joi.number().optional(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    status: Joi.string().optional().allow(''),
    exam_status: Joi.string().optional().allow(''),
    source: Joi.string().optional().allow(''),
    // sort_by: Joi.string().optional().allow('time'),
});
async function get_list(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_list(params);
}

const JOI_GET_DETAIL = Joi.object({
    // user_id: Joi.string().required(),
    id: Joi.string().required(),
});

async function get_detail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.get_detail(params);
}

const JOI_POST = Joi.object({
    id: Joi.string().optional().length(24),
    // user_id: Joi.string().optional().allow(''),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional().allow('').default(''),
    name: Joi.string().required(),                  // 试卷名称， 2016-2017学年度X学校4月月考卷
    type: Joi.string().optional().allow('').default(''),
    from_year: Joi.number().integer().optional(),
    to_year: Joi.number().integer().optional(),
    subtitle: Joi.string().optional().default('').allow(''),              // 副标题
    score: Joi.optional().default(0),                 // 试卷总分
    duration: Joi.optional().default(120),              // 考试时间，单位分钟
    paper_info: Joi.optional().default(''),            // 试卷信息栏，考试范围：xxx；333考试时间：100分钟；命题人：xxx
    cand_info: Joi.string().optional().default('').allow(''),             // 候选人信息栏
    score_info: Joi.string().optional().default('').allow(''),            // 得分栏
    attentions: Joi.string().optional().default('').allow(''),            // 注意事项
    secret_tag: Joi.string().optional().default('').allow(''),            // 保密标记文字
    gutter: Joi.optional().valid(0, 1).default(0),                // 1：表示有装订线； 0：表示无装订线
    template: Joi.string().optional().default('standard'),
    partsList: Joi.array().items(Joi.string()).optional(),
    volumes: Joi.array().items(Joi.object({
        title: Joi.string().optional().default('').allow(''),
        note: Joi.string().optional().default('').allow(''),
        blocks: Joi.array().items(Joi.object({
            id: Joi.number().optional(), // 学情雷达会有
            name: Joi.string().optional(), // 学情雷达会有
            title: Joi.string().optional().default('').allow(''),
            note: Joi.string().optional().default('').allow(''),
            type: Joi.string().optional().default('').allow(''),
            default_score: Joi.optional().default(0),
            questions: Joi.array().items(Joi.object({
                id: Joi.alternatives().try(
                    Joi.number(),
                    Joi.string(),
                ).required(),
                score: Joi.optional().default(0)
            }).unknown(true)
            )
        }))
    })).required().min(1).max(3),
    status: Joi.string().optional().allow(''),            // 状态
    exam_status: Joi.string().optional().allow(''),            // 状态
    category: Joi.number().optional(),
    province: Joi.string().optional().allow(''), // 省份
});

async function post_paper(req, res) {
    const params = await JOI_POST.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.post_paper(params);
}

const JOI_BATCH_POST_PAPER = Joi.array().items(JOI_POST);
async function batch_post_paper (req, res) {
    let params = await JOI_BATCH_POST_PAPER.validateAsync(req.body)
    params = params.map(e => {
        e.user_id = req.user.id
        return e
    })
    return await model.batch_post_paper(params)
}


const JOI_UPDATE_STATUS = Joi.object({
    // user_id: Joi.string().required(),
    id: Joi.string().required(),
    status: Joi.string().required(),
});

async function update_paper_status(req, res) {
    const params = await JOI_UPDATE_STATUS.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.update_paper_status(params);
}

const JOI_DELETE = Joi.object({
    // user_id: Joi.string().required(),
    id: Joi.string().required(),
});

async function delete_paper(req, res) {
    const params = await JOI_DELETE.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.delete_paper(params);
}

async function getDtkGateway(req, res) {
    const params = {
        host: req.get('origin') || req.get('referer'),
        user: req.user
    };
    return await model.getDtkGateway(params);
}


const JOI_UPLOAD_PAPER = Joi.object({
    name: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    type: Joi.string().required(),
    from_year: Joi.number().integer().required(),
    to_year: Joi.number().integer().required(),
    source_url: Joi.string().required(),
});

async function upload_paper(req, res) {
    const params = await JOI_UPLOAD_PAPER.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.upload_paper(params);
}

const JOI_PARSE_CALLBACK = Joi.object({
    task_id: Joi.string().required(), // 任务信息
    data: Joi.object().required()
});

async function parse_callback(req, res) {
    const params = await JOI_PARSE_CALLBACK.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.parse_callback(params);
}

const JOI_GET_UPLOAD_LIST = Joi.object({
    // user_id: Joi.string().required(),
    period: Joi.string().optional(),
    subject: Joi.string().optional(),
    type: Joi.string().optional().allow(''),
    grade: Joi.string().optional().allow(''),
    year: Joi.number().optional(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    status: Joi.string().optional().allow(''),
    // exam_status: Joi.string().optional().allow(''),
    // source: Joi.string().optional().allow(''),
    // sort_by: Joi.string().optional().allow('time'),
});

async function get_upload_list(req, res) {
    const params = await JOI_GET_UPLOAD_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_upload_list(params);
}

const JOI_DELETE_UPLOAD = Joi.object({
    id: Joi.string().required(), // 任务信息
});

async function delete_upload_by_id(req, res) {
    const params = await JOI_DELETE_UPLOAD.validateAsync(req.params);
    return await model.delete_upload_by_id(params, req.user);
}


