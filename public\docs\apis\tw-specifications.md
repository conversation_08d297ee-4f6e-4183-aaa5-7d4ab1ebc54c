---
title: 双向细目表接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 双向细目表接口

双向细目表接口提供教育考试中双向细目表的管理功能，支持创建、查询、修改、删除细目表，以及基于细目表智能组卷等核心功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取热门细目表列表

GET /v1/tw_specifications/hot

获取系统中的热门双向细目表列表，支持分页和筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|limit|query|number|是|分页大小|
|offset|query|number|是|分页偏移量|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|学科名称|
|type|query|string|否|细目表类型，可为空|
|sort_by|query|string|否|排序方式，可为空|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/tw_specifications/hot?limit=20&offset=0&period=初中&subject=数学&type=期中测试" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 45,
    "list": [
      {
        "id": "tw_spec_001",
        "name": "七年级数学期中测试细目表",
        "period": "初中",
        "subject": "数学",
        "type": "期中测试",
        "grade": "七年级",
        "province": "北京市",
        "year": 2024,
        "view_times": 156,
        "download_times": 89,
        "ctime": "2024-03-15T08:00:00Z"
      }
    ]
  }
}
```

---

## GET 获取用户细目表列表

GET /v1/tw_specifications/list

获取当前用户创建的双向细目表列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|limit|query|number|是|分页大小|
|offset|query|number|是|分页偏移量|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|学科名称|
|type|query|string|否|细目表类型，可为空|
|sort_by|query|string|否|排序方式（time/use_times），可为空|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/tw_specifications/list?limit=20&offset=0&period=初中&subject=数学&sort_by=time" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 12,
    "list": [
      {
        "id": "tw_spec_002",
        "name": "我的数学期中测试细目表",
        "period": "初中",
        "subject": "数学",
        "type": "期中测试",
        "grade": "七年级",
        "province": "北京市",
        "year": 2024,
        "view_times": 25,
        "download_times": 8,
        "ctime": "2024-04-10T14:30:00Z"
      }
    ]
  }
}
```

---

## GET 获取细目表详情

GET /v1/tw_specifications/{table_id}/detail

获取指定细目表的详细信息，包括完整的题块结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|table_id|path|string|是|细目表ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/tw_specifications/tw_spec_001/detail" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "tw_spec_001",
    "name": "七年级数学期中测试细目表",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中测试",
    "province": "北京市",
    "year": 2024,
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "period": "初中",
            "subject": "数学",
            "difficulty": "容易",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ],
    "total_score": 100,
    "question_count": 25,
    "ctime": "2024-03-15T08:00:00Z",
    "utime": "2024-03-20T16:45:00Z"
  }
}
```

---

## POST 知识点匹配检查

POST /v1/tw_specifications/knowledge

检查指定的知识点和题型组合是否可用于组卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|knowledge_checks|body|array|是|知识点检查数组，至少1个|
|» diff|body|string|是|难度（容易/较易/中等/较难/困难）|
|» kids|body|array|是|知识点ID列表|
|» num|body|number|是|题目数量|
|» period|body|string|是|学段|
|» subject|body|string|是|学科|
|» type|body|string|是|题型|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/tw_specifications/knowledge" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "diff": "容易",
      "kids": [1001, 1002],
      "num": 5,
      "period": "初中",
      "subject": "数学",
      "type": "选择题"
    }
  ]'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "unavailable_knowledges": [
      {
        "knowledge_id": 1002,
        "knowledge_name": "无理数",
        "reason": "题目数量不足"
      }
    ],
    "available_count": 4,
    "total_requested": 5
  }
}
```

---

## POST 创建细目表

POST /v1/tw_specifications

创建新的双向细目表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|body|string|是|细目表名称|
|period|body|string|是|学段|
|subject|body|string|是|学科|
|grade|body|string|是|年级|
|type|body|string|是|细目表类型|
|province|body|string|否|省份|
|blocks|body|array|是|题块信息数组，至少1个|
|» name|body|string|是|题块名称|
|» type|body|string|是|题块类型|
|» questions|body|array|是|题目配置数组|
|»» type|body|string|是|题目类型|
|»» difficulty|body|string|是|难度|
|»» score|body|number|是|分值|
|»» knowledges|body|array|是|知识点数组|
|from|body|string|否|复制来源细目表ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/tw_specifications" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的数学期中测试细目表",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中测试",
    "province": "北京市",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "difficulty": "容易",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "tw_spec_003"
  }
}
```

### 错误示例

#### 添加细目表失败

```json
{
  "code": 4,
  "msg": "添加细目表失败",
  "data": ""
}
```

---

## PUT 修改细目表

PUT /v1/tw_specifications/{table_id}

修改已存在的双向细目表信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|table_id|path|string|是|细目表ID|
|name|body|string|是|细目表名称|
|period|body|string|是|学段|
|subject|body|string|是|学科|
|grade|body|string|是|年级|
|type|body|string|是|细目表类型|
|province|body|string|否|省份|
|blocks|body|array|是|题块信息数组，至少1个|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/tw_specifications/tw_spec_003" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的数学期中测试细目表",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中测试",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "difficulty": "中等",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "tw_spec_003"
  }
}
```

---

## DELETE 删除细目表

DELETE /v1/tw_specifications/{table_id}

删除指定的双向细目表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|table_id|path|string|是|细目表ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/tw_specifications/tw_spec_003" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "tw_spec_003"
  }
}
```

---

## POST 基于细目表组卷

POST /v1/tw_specifications/exampapers

根据双向细目表配置进行智能组卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|否|细目表ID|
|name|body|string|否|试卷名称|
|period|body|string|是|学段|
|subject|body|string|是|学科|
|grade|body|string|是|年级|
|type|body|string|是|试卷类型|
|province|body|string|否|省份|
|blocks|body|array|是|题块信息数组，至少1个|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/tw_specifications/exampapers" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "tw_spec_001",
    "name": "基于细目表的数学测试卷",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中测试",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "difficulty": "容易",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "basket_id": "basket_001",
    "question_count": 25,
    "total_score": 100,
    "generated_time": 1698825600000,
    "questions": [
      {
        "id": 12345,
        "type": "选择题",
        "difficulty": "容易",
        "score": 4,
        "knowledge_names": ["有理数"]
      }
    ]
  }
}
```

### 错误示例

#### 组卷失败

```json
{
  "code": 4,
  "msg": "组卷失败",
  "data": ""
}
```

---

## POST 下载细目表Excel

POST /v1/tw_specifications/xlsx

生成并下载双向细目表的Excel文件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|否|细目表ID|
|name|body|string|是|细目表名称|
|period|body|string|是|学段|
|subject|body|string|是|学科|
|grade|body|string|是|年级|
|type|body|string|是|细目表类型|
|province|body|string|否|省份|
|blocks|body|array|是|题块信息数组，至少1个|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/tw_specifications/xlsx" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "七年级数学期中测试细目表",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "type": "期中测试",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "difficulty": "容易",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "url": "https://example.com/download/tw_spec_excel_001.xlsx"
  }
}
```

---

## GET 查看试卷的细目表

GET /v1/tw_specifications/exampapers/{id}

获取指定试卷对应的双向细目表信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|试卷ID|
|from|query|number|否|来源类型，默认为系统|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/tw_specifications/exampapers/paper_001?from=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "auto_generated_tw_spec",
    "name": "数学试卷细目表",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "questions": [
          {
            "type": "选择题",
            "difficulty": "容易",
            "score": 4,
            "knowledges": [
              {
                "id": 1001,
                "name": "有理数"
              }
            ]
          }
        ]
      }
    ],
    "total_score": 100,
    "question_count": 25,
    "generated_from_paper": true
  }
}
```

---

## 返回数据结构

### 细目表信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|细目表ID|唯一标识|
|name|string|true|none|细目表名称|细目表标题|
|period|string|true|none|学段|小学/初中/高中|
|subject|string|true|none|学科|学科名称|
|grade|string|true|none|年级|适用年级|
|type|string|true|none|类型|如期中测试、期末测试|
|province|string|false|none|省份|地区信息|
|year|number|false|none|年份|创建年份|
|view_times|number|false|none|查看次数|使用统计|
|download_times|number|false|none|下载次数|下载统计|
|ctime|string|true|none|创建时间|ISO格式时间|

### 题块结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|题块名称|如"选择题"|
|type|string|true|none|题块类型|题目类型|
|questions|array|true|none|题目配置|题目列表|

### 题目配置结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|string|true|none|题目类型|选择题/填空题等|
|difficulty|string|true|none|难度|容易/较易/中等/较难/困难|
|score|number|true|none|分值|题目分数|
|knowledges|array|true|none|知识点|知识点列表|

### 知识点结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|知识点ID|唯一标识|
|name|string|true|none|知识点名称|知识点标题|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|12|参数错误|请求参数格式错误或缺失必要参数|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 双向细目表概念

双向细目表是一种教学评价工具，用于科学设计试卷：
- **横向维度**: 知识点分布，确保试卷覆盖重要知识点
- **纵向维度**: 认知层次分布，确保试卷难度合理
- **双向平衡**: 在知识点和难度两个维度上实现平衡

### 创建流程

1. **基础信息**: 设置细目表名称、学段、学科、年级等
2. **题块设计**: 定义不同类型的题块（选择题、填空题等）
3. **题目配置**: 为每个题块配置题目的难度、分值、知识点
4. **保存存储**: 细目表保存到知识库，本地记录用户关联关系

### 组卷逻辑

1. **参数预处理**: 过滤空知识点，处理"不限"难度
2. **算法调用**: 调用外部算法服务进行智能组卷
3. **结果处理**: 清空试题篮，填入新生成的题目
4. **难度随机化**: 对"不限"难度的题目随机分配具体难度

### 权限和安全

- **用户隔离**: 每个用户只能管理自己创建的细目表
- **数据验证**: 所有输入数据都进行严格的Joi验证
- **私有权限**: 新创建的细目表默认为私有状态
- **外部依赖**: 依赖知识库服务进行实际的数据存储和管理

### 使用场景

1. **教师备考**: 教师设计期中、期末考试试卷
2. **标准化考试**: 制定统一的考试标准和题目分布
3. **教学评估**: 分析试卷的知识点覆盖和难度分布
4. **试卷优化**: 基于细目表持续优化试卷质量