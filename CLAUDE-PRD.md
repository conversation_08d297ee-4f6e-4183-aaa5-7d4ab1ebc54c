# CLAUDE-PRD.md - AI协作需求描述规范

## 概述

本文档定义了与 Claude AI 进行需求沟通的标准化框架，旨在提高需求描述的准确性和开发效率。遵循此规范可以确保AI更好地理解业务需求并提供精准的技术方案。

## 需求描述模板

### 1. 需求概述

```markdown
## 需求标题：[简洁明了的需求名称]

### 需求背景
- **当前状态**：[描述现有情况或待解决的问题]
- **期望状态**：[希望达到的目标状态]
- **业务价值**：[实现此需求的价值和意义]

### 目标用户
- **主要用户**：[谁会使用这个功能]
- **使用频率**：[日常/周期性/偶尔]
- **用户规模**：[预计用户数量级]
```

### 2. 用户故事

```markdown
### 用户故事

作为 [用户角色]
我想要 [功能/能力描述]
以便于 [达成的业务目标或价值]

### 验收标准
- [ ] AC1: [具体可验证的验收条件]
- [ ] AC2: [具体可验证的验收条件]
- [ ] AC3: [具体可验证的验收条件]
```

### 3. 业务场景

```markdown
### 场景描述

#### 场景一：[场景名称]
**Given** (前置条件)：[系统或用户的初始状态]
**When** (触发动作)：[用户执行的操作或系统事件]
**Then** (预期结果)：[系统的响应和状态变化]

#### 场景二：[场景名称]
**Given**：[前置条件]
**When**：[触发动作]
**Then**：[预期结果]
```

### 4. 功能需求

```markdown
### 功能需求清单

#### 核心功能
| 编号 | 功能名称 | 功能描述 | 优先级 |
|------|----------|----------|--------|
| FR1  | [功能1]  | [详细描述] | P0/P1/P2 |
| FR2  | [功能2]  | [详细描述] | P0/P1/P2 |

#### 功能详细说明
**FR1: [功能名称]**
- **输入**：[需要的输入数据或参数]
- **处理逻辑**：[核心业务逻辑描述]
- **输出**：[产生的结果或响应]
- **异常处理**：[错误情况的处理方式]
```

### 5. 非功能需求

```markdown
### 非功能需求

#### 性能要求
- **响应时间**：[例如：< 2秒]
- **并发支持**：[例如：支持1000并发用户]
- **数据处理量**：[例如：单次处理10万条记录]

#### 安全要求
- **认证方式**：[JWT/Session/OAuth2.0等]
- **授权控制**：[角色权限说明]
- **数据安全**：[加密要求、敏感数据处理]

#### 可用性要求
- **可用率**：[例如：99.9%]
- **容错机制**：[故障处理策略]
- **用户体验**：[UI/UX特殊要求]
```

### 6. 数据需求

```markdown
### 数据模型设计

#### 实体：[实体名称]
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | String | 主键，唯一 | 唯一标识符 |
| name | String | 必填，最大50字符 | 名称 |
| status | Enum | 必填 | 状态：active/inactive |
| created_at | DateTime | 必填 | 创建时间 |

#### 数据关系
- [实体A] 1:N [实体B]：[关系说明]
- [实体B] N:M [实体C]：[关系说明]
```

### 7. 接口需求

```markdown
### API接口定义

#### 接口：[接口名称]
- **端点**：`POST /api/v1/[resource]`
- **描述**：[接口功能说明]
- **认证**：需要 Bearer Token

**请求参数**：
```json
{
  "param1": "string, 必填, 参数说明",
  "param2": "number, 选填, 参数说明",
  "param3": {
    "subParam1": "string, 必填, 子参数说明"
  }
}
```

**响应格式**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "12345",
    "result": "处理结果"
  }
}
```

**错误码**：
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数 |
| 401 | 未授权 | 检查认证信息 |
| 500 | 服务器错误 | 联系技术支持 |
```

### 8. 约束与依赖

```markdown
### 技术约束
- **技术栈限制**：[必须使用的框架或技术]
- **兼容性要求**：[浏览器、设备兼容性]
- **集成要求**：[需要集成的第三方服务]

### 业务约束
- **合规要求**：[法律法规要求]
- **业务规则**：[特殊的业务限制]
- **时间要求**：[上线时间、迭代周期]

### 外部依赖
- **依赖系统**：[依赖的外部系统或服务]
- **依赖数据**：[需要的外部数据源]
```

## 需求沟通最佳实践

### 1. 渐进式细化原则

1. **第一轮**：描述整体需求和目标
2. **第二轮**：提供具体用户场景和业务流程
3. **第三轮**：明确技术细节、数据结构和接口定义
4. **第四轮**：补充边界条件和异常处理

### 2. 提供充分的上下文

- 相关的业务流程图或架构图
- 现有系统的相关代码片段
- 竞品或参考系统的功能说明
- 已有的设计文档或PRD

### 3. 明确优先级

- **P0 - 必须实现**：核心功能，没有这些功能产品无法使用
- **P1 - 应该实现**：重要功能，显著影响用户体验
- **P2 - 可以实现**：优化功能，锦上添花

### 4. 提供示例数据

始终提供真实或接近真实的示例数据，帮助AI更好地理解需求：

```markdown
### 示例数据

#### 输入示例
```json
{
  "user_id": "USER_123456",
  "order_items": [
    {
      "product_id": "PROD_001",
      "quantity": 2,
      "price": 99.99
    }
  ],
  "coupon_code": "SAVE20"
}
```

#### 输出示例
```json
{
  "order_id": "ORDER_789012",
  "total_amount": 179.98,
  "discount_amount": 20.00,
  "final_amount": 159.98,
  "status": "pending_payment"
}
```
```

### 5. 需求验证清单

在提交需求前，请确保：

- [ ] 需求背景和价值描述清晰
- [ ] 用户故事符合标准格式
- [ ] 验收标准具体可测量
- [ ] 核心场景都有描述
- [ ] 功能优先级已标注
- [ ] 提供了必要的示例数据
- [ ] 异常情况有考虑
- [ ] 技术约束已说明

## 与AI协作的特殊提示

### 1. 使用明确的指令

- ✅ 好的指令："基于订单系统，设计一个支持多种支付方式的支付模块"
- ❌ 模糊指令："做一个支付功能"

### 2. 分阶段实施

```markdown
阶段一：实现基础的订单创建功能
阶段二：添加优惠券和折扣计算
阶段三：集成支付和退款功能
阶段四：添加订单状态追踪和通知
```

### 3. 明确排除项

告诉AI什么不需要做，避免过度设计：

- 不需要考虑国际化
- 不需要实时数据同步
- 不需要支持批量导入

### 4. 指定技术偏好

- 使用 RESTful API 而不是 GraphQL
- 数据库使用 MongoDB 而不是 MySQL
- 前端使用 Vue3 而不是 React

### 5. 迭代优化方式

```markdown
// 第一次请求
"实现用户注册功能，包含邮箱验证"

// 优化请求
"基于刚才的注册功能，添加以下优化：
1. 支持手机号注册
2. 添加图形验证码
3. 实现注册频率限制"
```

## 需求模板使用示例

```markdown
## 需求标题：会员积分兑换系统

### 需求背景
- **当前状态**：用户获得积分后无法使用，导致用户活跃度低
- **期望状态**：建立完整的积分兑换体系，提升用户粘性
- **业务价值**：提高用户留存率30%，增加用户日活20%

### 用户故事
作为 普通会员用户
我想要 使用积分兑换商品或优惠券
以便于 获得实际权益，提升购物体验

### 验收标准
- [ ] 用户可以查看可兑换商品列表
- [ ] 支持按积分筛选可兑换商品
- [ ] 兑换成功后积分实时扣减
- [ ] 生成兑换记录供用户查询

[继续按模板填写其他部分...]
```

## 总结

使用本规范可以：
1. 减少需求理解偏差
2. 提高开发效率
3. 确保交付质量
4. 便于后续维护

请在每次需求沟通时参考此模板，根据实际情况调整内容详细程度。记住：**清晰、具体、可验证**是好需求的三个关键特征。