const _ = require('lodash');
const enums = require('../../../common/enums/enums');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getSysList,
    getUserList,
    create,
    deleteById,
}

async function getSysList() {
    const sys_list = await db.collection(schema.exampaper_question_template).find({ type: enums.PaperTemplateType.SYS }).toArray();
    for (const item of sys_list) {
        item['template_id'] = item._id.toString();
        delete item._id;
        delete item.ctime;
        delete item.utime;
        delete item.user_id;
    }
    return sys_list;
}

async function getUserList(params) {
    const {user_id, period, subject} = params;
    // 查询自定义
    const result = await db.collection(schema.exampaper_question_template).find({ user_id: user_id, period: period, subject: subject, type: enums.PaperTemplateType.CUSTOM }).toArray();
    // 处理数据
    if (_.size(result)) {
        for (const item of result) {
            item['template_id'] = item._id.toString();
            delete item._id;
            delete item.ctime;
            delete item.utime;
            delete item.user_id;
        }
    }
    return result;
}

async function create (params) {
    const nowDate = new Date();
    const insertData = _.assign({}, params);
    insertData.type = enums.PaperTemplateType.CUSTOM;
    insertData.ctime = nowDate;
    insertData.utime = nowDate;
    const insertResult = await db.collection(schema.exampaper_question_template).insertOne(insertData);
    return { template_id: insertResult.insertedId.toString() };
}

async function deleteById (params) {
    const { user_id, template_id } = params;
    const data = await db.collection(schema.exampaper_question_template).findOne({_id: new ObjectId(template_id) });
    if (_.isEmpty(data) || data.user_id !== user_id) throw new BussError('删除数据不存在');
    await db.collection(schema.exampaper_question_template).deleteOne({_id: new ObjectId(template_id)});
}
