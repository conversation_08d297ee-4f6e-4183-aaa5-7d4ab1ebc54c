const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const moment = require('moment');

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../../common/enums/template');
const enums = require('../../../common/enums/enums');
const utils = require('../../../common/utils/utils');
const BussError = require('../../../common/exceptions/BussError');
const { searchRegionByIp } = require('../../../common/utils/ip2region');
const paper_utils = require('../../../common/utils/paper_utils');

module.exports = {
    getClasses,
    getClassStudents,
    getUserInfo,
    getHfsTeacherInfo,
    getYjTeacherInfo,
    getUserProperty,
    updateUserProperty,
    getIpRegion,
    getProfile,
}

async function getClasses(params) {
    const {user_id} = params;
    // 调用教师端接口
    let list = await client.hfsTeacherV3.getTeacherClasses(user_id);
    if (_.size(list)) { // 过滤本学年的班级
        const currentYear = new Date().getFullYear();
        list = list.filter(e => {
            let year = Math.floor((+e.id) / 10000000) + 2000;
            return year >= currentYear;
        });
    }
    return list;
}

async function getClassStudents(params) {
    const {user_id, class_id} = params;
    return await client.hfsTeacherV3.getTeacherClassStudents(user_id, class_id);
}

async function getUserInfo(params) {
    const {user_id} = params;
    return await client.yj.getUserInfo(user_id);
}

async function getHfsTeacherInfo(params) {
    const {unify_sid} = params;
    const teacher = await client.hfsTeacherV3.getTeacherInfo(`unify_sid=${unify_sid}`);
    if (!_.isEmpty(teacher)) {
        const teacherSelecteInfo = await client.yj.getTeacherSelecteInfo(teacher.id, teacher.schoolId);
        const period = teacherSelecteInfo.xueduan;
        const subject = teacherSelecteInfo.subject;
        const grade = teacherSelecteInfo.grade;
        // teacher.standard_period = period;
        // teacher.standard_subject = subject;
        // teacher.standard_grade = grade;
        // teacher.curr_property = await getUserProperty(teacher.id, [{key: 'period', value: period}, {key: 'subject', value: subject}]);
        teacher.curr_property = {
            period: period || '高中',
            subject: subject || '数学',
            grade: grade,
        };
        teacher.authRoles = await client.yj.getTeacherAuthRoles(teacher.id);
        //
        const teachers = await client.yj.getTeachers(teacher.schoolId);
        const curTeacher = teachers.find(e => e.userId === +teacher.id);
        if (curTeacher) {
            teacher.roleInfos = _.pick(curTeacher, ['[role]', '_role', '_roleBJ', '_roleKM', '_roleNJ']);
        }
        if (teacher.phone) teacher.phone = utils.maskPhoneNumber(teacher.phone);
        delete teacher.account;
    }
    return teacher;
}

async function getYjTeacherInfo(params) {
    const { user_id } = params;
    const teachers = await client.yj.getTeachersByFilter({userIds: [user_id]});
    const teacher = teachers.find(e => e.userId === +user_id);
    if (!_.isEmpty(teacher)) {
        if (teacher.phone) teacher.phone = utils.maskPhoneNumber(teacher.phone);

        teacher.roles = (teacher.roles || []).map(e => {
            if (!_.isEmpty(e.nianji)) {
                const gradeInfo = enums.PeriodGradeMapping.find(map => map.yj_grade === e.nianji[0]);
                e.grade = gradeInfo.grade;
                e.period = gradeInfo.period;
            }
            return e;
        })

        teacher.gradeGroups = await client.yj.getGradeGroup(teacher.schoolId);
        delete teacher.loginName;
        const schoolSetting = await client.yj.getSchoolSetting(teacher.schoolId);
        teacher.schoolEduSystem = schoolSetting.eduSystem || ''
    }
    return teacher;
}

async function getUserProperty(user_id, items) {
    let doc = await db.collection(schema.user_property).findOne({user_id: user_id});
    if (_.isEmpty(doc)) {
        doc = {
            user_id: user_id,
            ctime: new Date(),
            utime: new Date()
        }
        if (_.size(items)) {
            for (const item of items) {
                doc[item.key] = item.value;
            }
        }
        await db.collection(schema.user_property).insertOne(doc);
    }
    delete doc._id;
    delete doc.user_id;
    delete doc.utime;
    delete doc.ctime;
    return doc;
}

async function updateUserProperty(params) {
    const {user_id, items} =  params;
    const doc = {
        user_id: user_id,
        utime: new Date()
    }
    for (const item of items) {
        doc[item.key] = item.value;
    }
    await db.collection(schema.user_property).updateOne({user_id: user_id}, {$set: doc});
}

const YJ_GRADES = [
    "初一",
    "初二",
    "初三",
    "初四",
    "高一",
    "高二",
    "高三",
    "四年制高一",
    "四年制高二",
    "四年制高三",
    "四年制高四",
    "直升初一",
    "直升初二",
    "直升高一",
    "国际部高一",
    "国际部高二",
    "国际部高三",
    "国际部高四",
    "一年级",
    "二年级",
    "三年级",
    "四年级",
    "五年级",
    "六年级",
    "七年制初一",
    "七年制初二",
    "七年制初三",
    "二四制初一",
    "二四制初二"
];

const YJ_GRADE_PERIOD = {
    "初一":"初中",
    "初二":"初中",
    "初三":"初中",
    "初四":"初中",
    "高一":"高中",
    "高二":"高中",
    "高三":"高中",
    "四年制高一":"高中",
    "四年制高二":"高中",
    "四年制高三":"高中",
    "四年制高四":"高中",
    "直升初一":"初中",
    "直升初二":"初中",
    "直升高一":"高中",
    "国际部高一":"高中",
    "国际部高二":"高中",
    "国际部高三":"高中",
    "国际部高四":"高中",
    "一年级":"小学",
    "二年级":"小学",
    "三年级":"小学",
    "四年级":"小学",
    "五年级":"小学",
    "六年级":"小学",
    "七年级":"初中",
    "八年级":"初中",
    "九年级":"初中",
    "七年制初一":"初中",
    "七年制初二":"初中",
    "七年制初三":"初中",
    "二四制初一":"初中",
    "二四制初二":"初中"
}

const RoleMatchRules = {
    '语文': ['语文'],
    '数学': ['数学'],
    '英语': ['英', '英语'],
    '物理': ['物理'],
    '化学': ['化学'],
    '生物': ['生物'],
    '地理': ['地理'],
    '历史': ['历', '史'],
    '政治': ['政', '思想品德', '思品', '道法', '道德与法治', '道德法治', '道德与法治 / 政治']
};

/**
 * 获取教师年级信息
 * @param teacher
 * @return {{period: string, subject: (string|string), grade: string}}
 */
function getTeacherGradeInfo(teacher) {
    let subject = '';
    let grade = '';
    let period = '';
    let role = '';
    const classes = teacher['classes'] || [];
    for (const cls of classes) {
        if (cls.grade) grade = cls.grade;
        const roles = cls.roles || [];
        if (_.size(roles)) role = roles[0];
    }
    if (grade) {
        // grade = YJ_GRADES[grade];
        period = YJ_GRADE_PERIOD[grade];
        if (role) {
            let pickedRole = null;
            for (let standardRole in RoleMatchRules) {
                let rules = RoleMatchRules[standardRole] || [];
                for (const key in rules) {
                    if (_.includes(role, rules[key])) {
                        pickedRole = standardRole;
                        break;
                    }
                }
            }
            if (pickedRole) role = pickedRole;
            else role = null;
        }
    }
    if (period === '小学' && role === '政治') {
        subject = '道德与法治';
    } else {
        subject = role;
    }
    // if (subject === '')
    const result = {
        period: period || '高中',
        grade: grade || '',
        subject: subject || '数学',
    }
    return result;
}

async function getIpRegion(req) {
    const ip = utils.getClientIp(req);
    const region = await searchRegionByIp(ip);
    if (!region.region) {
        return null;
    }
    let tempRegion = region.region.split('|');
    return {
        country: tempRegion[0],
        region: tempRegion[1],
        province: tempRegion[2].replace('省', ''),
        city: tempRegion[3].replace('市', ''),
        isp: tempRegion[4],
    };
}

async function getProfile(params, user) {
    const { period, subject } = params;
    const result = {
        comment_homework: { // 作业讲评
            total: 0,
            list: [],
        },
        comment_exam: { // 考试数据
            total: 0,
            list: [],
        },
        team_prep: { // 集备
            total: 0,
            doing: 0,
            done: 0,
            list: []
        },
        user_paper: { // 组卷
            total: 0,
            list: [],
        },
        upload_paper: { // 上传试卷
            total: 0,
            list: [],
        },
        disk_file: { // 网盘
            total: 0,
            list: [],
            share_list: []
        }
    }

    // 作业
    let list = await client.hfsTeacherV3.getTeacherExamList(user.id);
    list = list.filter(e => e.type === 12);
    result.comment_homework.total = _.size(list);
    if (result.comment_homework.total) result.comment_homework.list = list.slice(0, 3);
    // 考试
    let query = {
        user_id: user.id,
        status: 'done',
        period: period,
        subject: subject
    };
    result.comment_exam.total = await db.collection('user_exam_comment').find(query).count();
    if (result.comment_exam.total) {
        list = await db.collection('user_exam_comment').find(query).sort({utime: -1}).limit(1).toArray();
        for (const exam of list) {
            const data = _.pick(exam, ['exam_id', 'exam_name', 'paper_id', 'period', 'subject', 'grade', 'formal']);
            data.id = exam._id.toString();
            const classes = _.get(data, 'formal.class_info', []);
            data.class_info = [];
            (classes).forEach(e => {
                e['question_num'] = _.size(e.questions);
                delete e['questions'];
                if (e.question_num) data.class_info.push(e);
            });
            delete data.formal;
            data.ctime = exam.ctime.getTime();
            data.utime = exam.utime.getTime();
            result.comment_exam.list.push(data);
        }
    }
    // 集备 - 2 条数据
    const { semester, to_year } = utils.getSemesterAndYear();
    query = {
        period: period,
        subject: subject,
        to_year: to_year,
        semester: semester,
        school_id: user.school_id,
        status: {$ne: enums.TeamPrepStatus.INIT},
        deleted: enums.BOOL.NO,
    };
    const user_id = Number(user.id).toString();
    list = await db.jzl_collection(schema.team_prep).find(query).sort({createdAt: -1}).toArray();
    list = list.filter(e => e.main_teacher_id === user_id || e.teachers.includes(user_id));
    result.team_prep.total = _.size(list);
    if (result.team_prep.total) {
        result.team_prep.doing = list.filter(e => e.status === enums.TeamPrepStatus.DOING).length;
        result.team_prep.done = list.filter(e => e.status === enums.TeamPrepStatus.DONE).length;
        list = list.slice(0, 2);
        for (const data of list) {
            data.id = data._id.toString();
            delete data._id;
            delete data.pBranch;
            delete data.creator;
            delete data.operator;
            delete data.deleted;
            delete data['__v'];
            data.overtime = enums.BOOL.NO;
            if (data.status !== enums.TeamPrepStatus.DONE
                && moment().isAfter(moment(data.end_time).add(1, 'day'))) data.overtime = enums.BOOL.YES;
            result.team_prep.list.push(data);
        }
    }
    // 组卷
    query = {
        user_id: user.id,
        period,
        subject,
        source: enums.PaperSourceType.ASSEMBLE,
        valid: enums.BOOL.YES
    }
    result.user_paper.total = await db.collection(schema.user_paper).find(query).count();
    if (result.user_paper.total) {
        let proj = { _id: 1, name: 1, download_time: 1, volumes: 1, period: 1, subject: 1, grade: 1, press_version: 1, share_sort: 1, expired_time: 1, invalid_time: 1, dtk_id: 1, last_sync_time: 1, type: 1, status: 1, ctime:1, utime: 1, source: 1, error: 1, from_year: 1, to_year: 1 };
        list = await db.collection(schema.user_paper).find(query).sort({ctime: -1}).limit(3).project(proj).toArray();
        for (const item of list) {
            result.user_paper.list.push({
                id: item._id.toString(),
                name: item.name,
                question_num: paper_utils.get_question_num(item),
                grade: item.grade || '',
                period: item.period,
                subject: item.subject,
                source: item.source,
                type: item.type,
                from_year: item.from_year,
                to_year: item.to_year,
                status: item.status,
                exam_status: item.exam_status,
                ctime: item.ctime.getTime(),
                utime: item.utime.getTime(),
                error: item.error || '',
                from_enum: enums.PaperFrom.GROUP
            });
        }
    }
    // 上传 3
    query = {
        user_id: Number(user_id).toString(),
        period,
        subject,
        // source: enums.PaperSourceType.ASSEMBLE,
        deleted: enums.BOOL.NO
    };
    result.upload_paper.total = await db.jzl_collection(schema.parse_task).find(query).count();
    if (result.upload_paper.total) {
        list = await db.jzl_collection(schema.parse_task).find(query).sort({createdAt: -1}).limit(3).toArray();
        const paper_ids = [];
        for (const item of list) {
            if (item.tiku_paper_id) paper_ids.push(item.tiku_paper_id);
            result.upload_paper.list.push({
                id: item._id.toString(),
                name: item.name,
                question_num: 0,
                grade: item.grade || '',
                period: item.period,
                subject: item.subject,
                // source: item.source,
                type: item.type,
                from_year: item.from_year,
                to_year: item.to_year,
                status: item.status,
                tiku_paper_id: item.tiku_paper_id,
                // exam_status: enums.ExamStatus.EDITABLE,
                ctime: item.createdAt.getTime(),
                utime: item.updatedAt.getTime(),
                error: item.error || '',
                from_enum: enums.PaperFrom.GROUP,
                task_type: item.task_type
            });
        }
        if (_.size(paper_ids)) {
            const papers = await db.collection(schema.user_paper).find({_id: {$in: paper_ids.map(e => new ObjectId(e))}}).toArray();
            for (const data of result.upload_paper.list) {
                const paper = papers.find(e => data.tiku_paper_id === e._id.toString());
                if (paper) {
                    data.question_num = paper_utils.get_question_num(paper);
                    data.exam_status = paper.exam_status;
                    data.source = paper.source;
                }
            }
        }
    }
    // 网盘 5
    const jzl_user = await db.jzl_collection(schema.jzl.users_permissions_user).findOne({yjUserId: Number(user.id).toString()});
    let pBranch = null;
    if (!_.isEmpty(jzl_user)) {
        query = {
            parent_id: '0',
            period,
            subject,
            creator: jzl_user._id,
            deleted: enums.BOOL.NO
        }
        result.disk_file.total = await db.jzl_collection(schema.jzl.disk_file).find(query).count();
        if (result.disk_file.total) {
            list = await db.jzl_collection(schema.jzl.disk_file).find(query).sort({is_top: -1, updatedAt: -1}).limit(5).toArray();
            for (const data of list) {
                handleFileFields(data);
                data.creator = {
                    id: jzl_user._id.toString(),
                    username: jzl_user.username
                };
                result.disk_file.list.push(data);
            }
        }
        pBranch = jzl_user.pBranch;
    }
    if (!pBranch) {
        const branch = await db.jzl_collection(schema.jzl.users_permissions_branch).findOne({yjSchoolId: user.school_id});
        if (!_.isEmpty(branch)) pBranch = branch._id;
    }
    if (pBranch) {
        query = {
            period,
            subject,
            is_folder: enums.BOOL.NO,
            shared: enums.BOOL.YES,
            pBranch: pBranch,
            deleted: enums.BOOL.NO
        }
        list = await db.jzl_collection(schema.jzl.disk_file).find(query).sort({is_top: -1, updatedAt: -1}).limit(5).toArray();
        if (_.size(list)) {
            const ids = list.map(e => e.creator);
            const users = await db.jzl_collection(schema.jzl.users_permissions_user).find({_id: {$in: ids}}).toArray();
            for (const data of list) {
                const creator = users.find(e => e._id.toString() === data.creator.toString());
                handleFileFields(data);
                data.creator = {
                    id: creator._id.toString(),
                    username: creator.username
                };
                result.disk_file.share_list.push(data);
            }
        }
    }
    return result;
}

function handleFileFields(data) {
    // data.creator = utils.pickFields(data.creator, ['id', 'username']);
    // data.source_user = utils.pickFields(data.source_user, ['id', 'username']);
    delete data._id;
    delete data.type;
    delete data.source_id;
    delete data.pBranch;
    delete data.deleted;
    delete data.operator;
    return data;
}
