
const _ = require('lodash');
const enums = require('../../../common/enums/enums');
const client = require('../../../common/client');
const BussError = require('../../../common/exceptions/BussError');
const utils = require('../../../common/utils/utils');
const schema = require('../../../common/enums/schema');
const db = require('../../../common/db');

module.exports = {
    getGradeGroup,
    getTeachers,
    getResourceStatistics,
}

async function getGradeGroup(params) {
    const { school_id } = params;
    return await client.yj.getGradeGroup(school_id);
}

async function getTeachers(params) {
    const { school_id } = params;
    const teachers = await client.yj.getTeachers(school_id);

    const result = [];
    for (const item of teachers || []) {
        let t = {
            id: item.userId.toString(),
            name: item.name,
            phone: item.phone,
            title: [],
            xueke: [],
            nianji: []
        };
        // if (item['_roleNJ']) t.title.push(...new Set(item['_roleNJ'].split(',')));
        if (item['_roleKM']) t.xueke.push(...new Set(item['_roleKM'].split(',')));
        if (item['_roleNJ']) t.nianji.push(...new Set(item['_roleNJ'].split(',')));
        result.push(t);
    }
    return result;
}

async function getResourceStatistics(params, user) {
    const {period, subject, grade} = params;
    const {school_id} = user;
    const result = {
        team_prep: { // 集备资源
            total: 0
        },
        school_album_data: { // 套卷
            total: 0
        },
        textbook: { // 教辅
            total: 0
        },
        disk_file: { // 网盘共享
            total: 0
        }
    };
    if (!school_id) return result;
    // 集备
    let query = {
        period,
        subject,
        school_id,
        deleted: enums.BOOL.NO
    };
    const planList = await db.jzl_collection(schema.edu_plan).find(query).toArray();
    if (_.size(planList)) {
        for (const plan of planList) {
            const chapters = [];
            for (const week of plan.weeks || []) {
                if (!_.size(week.children)) continue;
                const children = utils.flattenTree(week.children).filter(e => e.key === enums.EduPlanChapterKey.LESSON);
                if (!_.size(children)) continue;
                chapters.push(...children);
            }
            if (_.size(chapters)) {
                const cond = {
                    edu_plan_chapter_id: {$in: chapters.map(e => e.id)},
                    status: enums.TeamPrepStatus.DONE,
                    deleted: enums.BOOL.NO
                };
                const dataList = await db.jzl_collection(schema.team_prep_data).find(cond).sort({updatedAt: -1}).toArray();
                const keySet = new Set();
                for (const item of dataList) {
                    if (!_.size(item.children)) continue;
                    const key = `${item.edu_plan_chapter_id}_${item.key}`;
                    if (keySet.has(key)) continue;
                    keySet.add(key);
                    result.team_prep.total += 1;
                }
            }
        }
    }
    // 套卷
    query = {
        period,
        subject,
        permission: 'private',
        offset: 0,
        limit: 9999
    }
    const album = await client.kb.getAlbumList(query);
    if (album.total) {
        result.school_album_data.total = _.sumBy(album.list, 'resource_count');
    }
    // 教辅
    query = {
        period,
        subject,
        school_id: school_id,
        deleted: enums.BOOL.NO,
    };
    result.textbook.total = await db.jzl_collection(schema.jzl.textbook).find(query).count();
    // 网盘
    query = {
        period,
        subject,
        is_folder: 0,
        school_id: school_id,
        shared: 1,
        deleted: enums.BOOL.NO
    }
    result.disk_file.total = await db.jzl_collection(schema.jzl.disk_file).find(query).count();
    return result;
}

