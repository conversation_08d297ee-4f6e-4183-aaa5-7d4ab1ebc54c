
const _ = require('lodash');
const enums = require('../../../common/enums/enums');
const client = require('../../../common/client');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getGradeGroup,
    getTeachers
}

async function getGradeGroup(params) {
    const { school_id } = params;
    return await client.yj.getGradeGroup(school_id);
}

async function getTeachers(params) {
    const { school_id } = params;
    const teachers = await client.yj.getTeachers(school_id);

    const result = [];
    for (const item of teachers || []) {
        let t = {
            id: item.userId.toString(),
            name: item.name,
            phone: item.phone,
            title: [],
            xueke: [],
            nianji: []
        };
        // if (item['_roleNJ']) t.title.push(...new Set(item['_roleNJ'].split(',')));
        if (item['_roleKM']) t.xueke.push(...new Set(item['_roleKM'].split(',')));
        if (item['_roleNJ']) t.nianji.push(...new Set(item['_roleNJ'].split(',')));
        result.push(t);
    }
    return result;
}

