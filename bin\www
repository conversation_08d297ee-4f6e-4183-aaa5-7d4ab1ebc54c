#!/usr/bin/env node
const config = require('config');

const utils = require('../common/utils/utils');
const enums = require('../common/enums');
const logger = require('../common/lib/logger');
const db = require('../common/db');
let redis = require('../common/redis');

(async () => {
    // 初始日志
    // logger.init();

    try {
        await db.init();
        // await redis.init();
        const app = require('./app');
        const server = app.listen(config.app.port, () => {
            logger.info(`Startup ${config.app.name} in env ${process.env.NODE_ENV} on port ${config.app.port}`);
            logger.info(`Local Address is: ${utils.getLocalAddressAndPort()}`);
        });
        server.on('error', onError);
    } catch (e) {
        logger.error(e);
        process.exit(-1);
    }
})();

function onError (error) {
    if (error.syscall !== 'listen') {
        throw error;
    }

    switch (error.code) {
        case 'EACCES': {
            logger.error(`${config.app.port} requires elevated privileges`);
            process.exit(1);
            break;
        }
        case 'EADDRINUSE':
            logger.error(`${config.app.port}  is already in use`);
            process.exit(1);
            break;
        default: {
            throw error;
        }
    }
}

// Global Error handlers
process.on('uncaughtException', (err) => {
    const errMsg = {
        api: 100,
        msg: 'Global caught exception!',
        errStack: err.stack || err
    };
    logger.error(`=== Error Code: `, enums.code.GLOBAL_ERR);
    logger.error(`=== Error Msg: `, errMsg);
});

// 监听 SIGINT 信号
process.on('SIGINT', async () => {

    // 关闭 Express 服务器
    // server.close(() => {
    //     logger.info('server closed.');
    // });

    // 关闭 Redis 连接
    await redis.quit();
    console.log('Redis connection closed.');

    // 退出进程
    process.exit(0);
});
