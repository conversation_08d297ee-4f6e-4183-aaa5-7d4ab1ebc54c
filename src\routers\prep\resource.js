const express = require('express');
const router = express.Router();
const service = require('../../service/prep/resource');
const { handler } = require('../../../common/middlewares/request');

// 获取目录
router.get('/catalog', handler(1, service.getCatalogList));
// 获取目录资源
router.get('/resource/list', handler(1, service.getResourceList));
// 获取资源详细
router.get('/resource/info', handler(1, service.getResourceDetail));
// 下载文件
router.post('/resource/download', handler(1, service.downloadResource));

module.exports = router;
