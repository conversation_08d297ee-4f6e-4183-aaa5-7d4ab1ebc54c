
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const client = require('../../../common/client');
const enums = require('../../../common/enums/enums');
const schema = require('../../../common/enums/schema');
const templateModel = require('../exampaper/paper_template');
const redis = require('../../../common/redis');
const utils = require('../../../common/utils/utils');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getFilters,
    search,
    search2,
    getAnswerImage,
    getAnswer,
    getKnowledge,
    getQuestionSame,
    getQuestionReco,
    syncPaperQuestions,
    getByIds,
    updateQuestion,
    getQuestionDetail,
}

async function getFilters() {
    const result = await client.kb.getQuestionFilters();
    const questionTemplateList = await templateModel.getSysList();
    return addAttach(result, questionTemplateList);
}

const addAttach = (res, questionTemplateList) => {
    for (let info in res) {
        let subjects = res[info].subjects
        for (let subject_info in subjects) {
            let subjectInfo = subjects[subject_info];
            // subjectInfo.category = ['常考题', '经典题', '压轴题', '精品题', '名优校题']
            subjectInfo.question_template = questionTemplateList.filter(item => item.period === res[info].period && item.subject === subjectInfo.subject);
        }
    }
    return res
}

async function search(params) {

    const offset_value = params.offset;
    const limit_value = params.limit;
    if ((offset_value + limit_value) <= 500) {
        params.offset = 0;
        params.limit = 500;
    }
    const result = await client.kb.questionSearch(params);
    // 排序
    const user_period = params.period;
    const user_province = params.provinces && params.provinces[0] && params.provinces[0].name;
    if ((offset_value + limit_value) <= 500) {
        result.questions = _sortQuestions({ user_period, user_province, sort_by: params.sort_by, offset_value, limit_value, questions: result.questions });
    }
    params.province = params.province || user_province;
    _addShowTags(result.questions, params);
    return result;
}

async function search2(params) {

    const offset_value = params.offset;
    const limit_value = params.limit;
    // if ((offset_value + limit_value) <= 500) {
    //     params.offset = 0;
    //     params.limit = 500;
    // }
    const result = await client.kb.questionSearch2(params);
    // 排序
    // const user_period = params.period;
    // const user_province = params.province && params.province[0];
    // if ((offset_value + limit_value) <= 500) {
    //     result.questions = _sortQuestions({ user_period, user_province, sort_by: params.sort_by, offset_value, limit_value, questions: result.questions });
    // }
    // params.province = params.province || user_province;
    _addShowTags2(result.questions, params);
    return result;
}

async function getAnswer(params) {
    const { question_id } = params;
    const question = await client.kb.getQuestionById(question_id);
    return { blocks: question.blocks };
}


async function getAnswerImage(params) {
    const { question_id } = params;
    return await client.utilbox.getQuestionAnswerImage(question_id);
}

async function getKnowledge(params) {
    const { question_id } = params;
    const question = await client.kb.getQuestionById(question_id);
    const result = {}
    if (!_.isEmpty(question)) {
        result.blocks = { knowledges: (question.blocks || {}).knowledges || [] };
        result.knowledges = question.knowledges || [];
    }
    return result;
}

async function getQuestionSame(params) {
    const { question_id, change_times } = params;
    let result = {};
    if (_.isNumber(question_id)) {
        result = await client.kb.getQuestionSame(question_id, change_times);
    } else {
        result = {
            total_num: 0,
            questions: []
        };
        const question = await db.collection(schema.user_question).findOne({_id: new ObjectId(question_id)});
        if (_.isEmpty(question)) return result;
        const knowMap = new Map();
        if (_.size(question.knowledges)) {
            for (const know of question.knowledges) {
                if (know.hasOwnProperty('source')) continue;
                knowMap.set(know.id, know);
            }
        }
        for (const array of question.blocks.knowledges || []) {
            for (const know of array) {
                if (know.hasOwnProperty('source')) continue;
                knowMap.set(know.id, know);
            }
        }
        const knowledges = Array.from(knowMap.values());
        if (!_.size(knowledges)) return result;
        const algo_params = {
            period: question.period,
            subject: question.subject,
            grade: question.grade,
            ques_type: question.type,
            order: 'time',
            knowledges: knowledges.map(e => {
                return {id: e.id, name: e.name};
            })
        };
        const algo_result = await client.algo.recommendQuestions(algo_params);
        result.total_num = algo_result.total;
        const limit = 10;
        const offset = change_times * limit;
        const ids = algo_result.questions.slice(offset, offset + limit).map(e => e.id);
        result.questions = await client.kb.getQuestionByIds(ids);
    }
    if (!_.isEmpty(result) && _.size(result.questions)) _addShowTags(result.questions, params);
    return result;
}

async function getQuestionReco(params) {
    const { question_id } = params;
    const cacheKey = `open:question:reco:${question_id}`;
    const cacheData = await redis.get(cacheKey);
    if (cacheData) return JSON.parse(cacheData);
    const result = [];
    const question = await client.kb.getQuestionById(question_id);
    if (_.isEmpty(question)) return [];
    const reco_questions = await client.algo.getRecoQuestions(question_id);
    let questionIds = reco_questions.map(e => e.id);
    if (!_.size(questionIds)) return result;
    questionIds = questionIds.slice(0, 10);
    let questions = await client.kb.getQuestionByIds(questionIds);
    if (!_.size(questions)) return result;
    questions = questions.map(e => {
        if (!_.isEmpty(e.blocks)) {
            delete e.blocks.answers;
            delete e.blocks.solutions;
            delete e.blocks.explanations;
        }
        return e;
    });
    _addShowTags(questions, {}); // 增加标签
    await redis.set(cacheKey, JSON.stringify(questions), 60 * 60 * 24 * 2); // 两天有效期
    return questions;
}

async function updateQuestion(params, user) {
    const {question_id, blocks, zujuanId} = params;
    const paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(zujuanId)});
    if (_.isEmpty(paper)) throw new BussError('组卷不存在');
    let ques = null;
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            for (const q of block.questions) {
                if (q.id.toString() === question_id) {
                    ques = q;
                    break;
                }
            }
        }
    }
    if (_.isEmpty(ques)) throw new BussError('试题不存在');
    const source = ques['source'] || enums.QuestionSource.SYS;
    const question = await getQuestion(question_id, source);
    if (source === enums.QuestionSource.ZX) {
        const doc = {
            blocks: _.assign({}, question.blocks, blocks),
            utime: new Date()
        };
        await db.collection(schema.user_question).updateOne({_id: new ObjectId(question_id)}, {$set: doc});
        return {id: question_id};
    } else {
        const doc = _.pick(question, ['type', 'period', 'subject', 'grade', 'blocks', 'knowledges', 'audio', 'description', 'comment', 'difficulty', 'year']);
        doc.user_id = user.id;
        doc.source = source;
        doc.source_id = question_id;
        doc.ctime = new Date();
        doc.ctime = new Date();
        doc.valid = enums.BOOL.YES;
        _.assign(doc, blocks);
        const insert = await db.collection(schema.user_question).insertOne(doc);
        // 替换原卷试题
        for (const volume of paper.volumes) {
            for (const block of volume.blocks) {
                for (const q of block.questions) {
                    if (q.id.toString() === question_id) {
                        q.id = insert.insertedId.toString();
                        q.source = enums.QuestionSource.ZX;
                        q.source_id = insert.insertedId.toString();
                    }
                }
            }
        }
        await db.collection(schema.user_paper).updateOne({_id: paper._id}, {$set: {volumes: paper.volumes}});
        return {id: insert.insertedId.toString()};
    }
}

async function getQuestionDetail(params) {
    const {id, paper_id} = params;
    let question = null;
    if (_.isNumber(id)) {
        question = await client.kb.getQuestionById(id);
    } else {
        question = await db.collection(schema.user_question).findOne({_id: new ObjectId(id)});
        if (!_.isEmpty(question)) {
            question.id = question._id.toString();
            delete question._id;
            delete question.user_id;
            // delete question.utime;
            // delete question.ctime;
            delete question.source;
            delete question.source_id;
            delete question.valid;
        }
    }
    return question;
}

async function getQuestion(id, source = enums.QuestionSource.SYS) {
    if (!id) return null;
    let question = null;
    if (source === enums.QuestionSource.SYS) {
        question = await client.kb.getQuestionById(id);
    } else if (source === enums.QuestionSource.ZX) {
        question = await db.collection(schema.user_question).findOne({_id: new ObjectId(id)});
    } else if (source === enums.QuestionSource.ZYK) {
        question = await db.zyk_collection(schema.zyk.questions).findOne({_id: new ObjectId(id)});
        if (!_.isEmpty(question)) {
            const knowledge_set = new Set();
            (question.knowledges || []).forEach(e => {
                if (!_.isEmpty(e)) knowledge_set.add(e.id);
            })
            const block_knowledges = _.get(question, 'blocks.knowledges', []);
            block_knowledges.forEach(e => {
                (e || []).forEach(ee => {
                    if (!_.isEmpty(ee)) knowledge_set.add(ee.id)
                })
            });
            // 加载知识点
            if (knowledge_set.size) {
                const knowledges = await db.zyk_collection(schema.zyk.knowledges).find({_id: {$in: [...knowledge_set] }}).toArray();
                knowledges.forEach(e => {
                    e.id = e._id.toString();
                    delete e._id;
                });
                for (const i in question.knowledges || []) {
                    const k = question.knowledges[i];
                    if (!k) continue;
                    const knowledge = knowledges.find(e => e.id === k.id.toString());
                    if (knowledge) question.knowledges[i] = knowledge;
                }
                if (_.size(block_knowledges)) {
                    for (const i in question['blocks']['knowledges']) {
                        for (const j in question['blocks']['knowledges'][i]) {
                            const k = question['blocks']['knowledges'][i][j];
                            if (!k) continue;
                            const knowledge = knowledges.find(e => e.id === k.id.toString());
                            if (knowledge) question['blocks']['knowledges'][i][j] = knowledge;
                        }
                    }
                }
            }
        }
    }
    if (!_.isEmpty(question) && question['_id']) {
        question.id = question['_id'].toString();
        delete question['_id'];
    }
    return question;
}

function _sortQuestions(params) {
    const { user_period = '', user_province = '', sort_by = '', offset_value, limit_value, questions = [] } = params;
    let current_year = new Date().getFullYear();
    let score_index = {};
    for (let index in questions) {
        let ques_score = 0;
        let one_ques = questions[index];
        // 按组卷次数 or 引用次数排序
        if (sort_by === 'use_times') {
            let use_times = one_ques.use_times || 0;
            ques_score = use_times * 1000;
        } else if (sort_by === 'cite_num') {
            let refer_times = one_ques.refer_times || 0;
            ques_score = refer_times * 1000;
        } else if (sort_by === 'year') {
            let ques_year = one_ques.year || 0;
            ques_score = ques_year * 1000;
        }

        // 年份加权
        let ques_year = one_ques.year || 0;
        if (ques_year && typeof ques_year == 'number') {
            ques_score += 200 * Math.exp(-0.5 * Math.abs(ques_year - current_year));
        }
        // 精品题加权
        let ques_elite = one_ques.elite || 0;
        if (ques_elite === 1) {
            ques_score += 5;
        }
        // 试卷引用
        let locale_score = 0;
        let category_score = [];
        let refer_exampapers = one_ques.refer_exampapers || [];
        for (let s = 0; s < refer_exampapers.length; s++) {
            let one_refer = refer_exampapers[s];
            // 过滤平台组的试卷
            let paper_from = one_refer.from || '';
            if (paper_from === 'ai_organize_paper') {
                continue
            }
            // 地区
            let province = one_refer.province || '';
            if (user_period === '初中' && user_province === province) {
                locale_score = 3
            }
            // 类型
            let paper_name = one_refer.name || '';
            let category = one_refer.category || '';
            if (category.match('真题|真卷') || paper_name.match('真题|真卷')) {
                category_score.push(1);
            } else if (category.match('联考|模拟') || paper_name.match('联考|模拟')) {
                category_score.push(0.8);
            } else if (category.match('期中|期末') || paper_name.match('期中|期末')) {
                category_score.push(0.6);
            }
        }
        ques_score += locale_score;
        if (category_score.length > 0) {
            ques_score += category_score.reduce((a, b) => Math.max(a, b), -Infinity);
        }
        // 试题难度加权
        let difficulty = one_ques.difficulty || 1;
        if (typeof difficulty == 'number') {
            ques_score += 1 / (Math.abs(difficulty - 4) + 1) * 2;
        }
        // 有解答
        let has_solutions = one_ques.has_solutions || 0;
        if (has_solutions === 1) {
            ques_score += 1.5
        }

        // 有解析
        let has_explanations = one_ques.has_explanations || 0;
        if (has_explanations === 1) {
            ques_score += 1.5
        }

        // 压轴题
        let is_final = one_ques.is_final || 0;
        if (is_final === 1) {
            ques_score += 0.2
        }

        // 试题题干
        let description = one_ques.description || '';
        let stems = one_ques.blocks.stems || [];
        for (let t = 0; t < stems.length; t++) {
            description += stems[t].stem || ''
        }


        // 名优校试题
        let attach = one_ques.attach || {};
        let is_famous = attach.is_famous || 0;
        if (is_famous === 1) {
            ques_score += 0.2
        }

        // 经典题
        let is_classic = attach.is_classic || 0;
        if (is_classic === 1) {
            ques_score += 0.2
        }
        score_index[index] = ques_score
        one_ques['recommend_score'] = ques_score
    }
    let new_questions = []
    // 排序返回指定的范围数据
    if (questions.length > 0) {
        let result = Object.keys(score_index).sort(function (a, b) { return score_index[b] - score_index[a] });
        let result_slice = result.slice(offset_value, Math.min(offset_value + limit_value, result.length));
        for (let j = 0; j < result_slice.length; j++) {
            let new_ques = questions[result_slice[j]];
            new_questions.push(new_ques)
        }
    }
    return new_questions
}

/**
 * 试题增加标签展示
 * @param questions
 */
function _addShowTags(questions = [], params) {
    let year = new Date().getFullYear();
    for (const question of questions) {
        const show_tags = [];
        if (question.type) show_tags.push(question.type); // 题型
        // 标签顺序 1 年份标签
        if (params.year) {
            let years = params.year.split(',');

            let refer_exampapers = question.refer_exampapers || [];

            let year;
            refer_exampapers.forEach(exampaper => {
                if (exampaper.year && years.includes('' + exampaper.year) && (!year || exampaper.year > year)) year = exampaper.year;
            });
            show_tags.push(year ? '' + year : '' + question.year);
        } else {
            show_tags.push(question.year ? '' + question.year : '');
        }
        if (question.difficulty) show_tags.push(enums.QuestionDifficultyName[question.difficulty]); // 难度
        if (question.elite) show_tags.push('精品题'); // 是否精品题
        question.show_tags = show_tags;
    }
}

function _addShowTags2(questions = [], params) {
    for (const question of questions) {
        const show_tags = [];
        if (question.type) show_tags.push(question.type); // 题型
        // 标签顺序 1 年份标签
        if (params.year) {
            let years = params.year;

            let refer_exampapers = question.refer_exampapers || [];

            let year;
            refer_exampapers.forEach(exampaper => {
                if (exampaper.year && years.includes('' + exampaper.year) && (!year || exampaper.year > year)) year = exampaper.year;
            });
            show_tags.push(year ? '' + year : '' + question.year);
        } else {
            show_tags.push(question.year ? '' + question.year : '');
        }
        if (question.difficulty) show_tags.push(enums.QuestionDifficultyName[question.difficulty]); // 难度
        if (question.elite) show_tags.push('精品题'); // 是否精品题
        question.show_tags = show_tags;
    }
}

async function syncPaperQuestions(user_id, paper) {
    if (!paper) return;
    const { to_year } = utils.getAcademicYear();
    // 同步试题
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            for (const index in block.questions) {
                const ques = block.questions[index];
                if (ques.source !== enums.QuestionSource.UPLOAD) {
                    continue;
                }
                const newQues = _.pick(ques, [
                    'type', 'period', 'subject', 'grade', 'difficulty', 'score', 'description', 'comment', 'year',
                    'knowledges', 'audio', 'source', 'source_id', 'blocks'
                ]);
                newQues.grade = paper.grade;
                newQues.user_id = user_id;
                newQues.valid = enums.BOOL.YES;
                newQues.ctime = new Date();
                newQues.utime = new Date();
                if (!newQues.year) newQues.year = to_year;
                if (!newQues.refer_exampapers) newQues.refer_exampapers = [];
                if (!newQues.difficulty) newQues.difficulty = 3;
                const insert = await db.collection(schema.user_question).insertOne(newQues);
                block.questions[index] = {
                    id: insert.insertedId.toString(),
                    source: enums.QuestionSource.ZX,
                    source_id: insert.insertedId.toString(),
                    score: ques.score,
                    type: ques.type,
                    period: ques.period,
                    subject: ques.subject,
                };
            }
        }
    }
}

async function getByIds(ids) {
    const questions = await db.collection(schema.user_question).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    for (const q of questions){
        q.id = q._id.toString();
        delete q._id;
    }
    return questions;
}
