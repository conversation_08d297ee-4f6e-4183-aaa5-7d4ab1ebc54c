---
title: 健康检查接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 健康检查接口

健康检查接口用于运维检查服务状态，是系统监控的重要组成部分。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 健康检查

GET /healthcheck/health

用于检查服务是否正常运行，通常被负载均衡器、监控系统调用。

### 请求参数

此接口无需任何参数。

### 请求示例

```shell
curl -X GET "http://localhost:8055/healthcheck/health"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": "Health OK"
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|string|true|none|响应数据|健康检查结果，固定返回"Health OK"|

### 使用场景

- **负载均衡器**: 检查服务实例健康状态
- **容器编排**: Kubernetes等平台的健康检查探针
- **监控系统**: 定期检查服务可用性
- **运维脚本**: 自动化部署和维护脚本

### 技术说明

- 接口响应极快，适合高频调用
- 无需认证，任何客户端都可访问
- 返回固定格式，便于自动化处理
- 服务异常时会返回HTTP错误状态码

### 注意事项

- 此接口仅检查HTTP服务可用性
- 不检查数据库连接状态
- 不检查依赖服务状态
- 建议结合其他监控指标综合判断服务健康状态