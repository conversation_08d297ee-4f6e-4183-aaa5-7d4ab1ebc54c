const config = require('config');
const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const _ = require('lodash');
const server = config.get('kb_api_server');
const logger = require('../lib/logger');

module.exports = {
    getBooks,
    getBookDetail,
    getBooksByIds,
    getQuestionFilters,
    questionSearch,
    questionSearch2,
    getQuestionById,
    getQuestionByIds,
    getKnowledgeTrees,
    getKnowledgeTreeDetail,
    getPaperById,
    getPaperByIds,
    getPaperCategories,
    getPaperFilters,
    getTableById,
    getRegionSimple,
    getQuestionSame,
    getUnableKnowledge,
    exampaperBySearch,
    exampaperFilters,
    getKnowledgeByIds,
    getHotTable,
    searchTables,
    getTableByIds,
    createTable,
    updateTable,
    deleteTableById,
    transmit,
    downloadTimesInc,
    getTableByRefId,
    request,
    getEduFileList,
    getEduFileById,
    updateEduFileTimes,
    knowledgeAnalysis,
    getKnowledgesByNames,
}

async function getBooks(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/books/',
        search: qs.stringify({
            api_key: server.appKey,
            ...(!_.isEmpty(params) ? params : null)
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    logger.info('教材获取成功:', Date.now());
    return result.data;
}

async function getBookDetail({ book_id, type, fields_type } = params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/books/${book_id}`,
        search: qs.stringify({
            api_key: server.appKey,
            type,
            fields_type
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return result.data;
}

/**
 * 获取多个教材
 * @param ids
 * @return {Promise<null|T>}
 */
async function getBooksByIds(ids) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/books/detail`,
        search: qs.stringify({
            api_key: server.appKey,
            ids
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return result.data;
}


async function getQuestionFilters() {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/questions/filters/',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return result.data;
}

async function questionSearch(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/questions/by_search/',
        search: qs.stringify({
            api_key: server.appKey
        })
    });

    const result = await axios.post(url, params);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function questionSearch2(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/questions/by_search2',
        search: qs.stringify({
            api_key: server.appKey
        })
    });

    const result = await axios.post(url, params);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function getQuestionById(question_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/questions/${question_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function getQuestionByIds(ids) {
    if (!_.size(ids)) return [];
    let data = {
        question_ids: ids,
        fields_type: 'full',
    };
    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/questions/',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const response = await axios.post(url, data);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`KB获取试题信息失败:`);
        return null;
    }
    return response.data;
}


async function getKnowledgeTrees(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/knowledge_trees/`,
        search: qs.stringify({
            api_key: server.appKey,
            ...(!_.isEmpty(params) ? params : null)
        })
    });

    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return result.data;
}


async function getKnowledgeTreeDetail(knowledge_tree_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/knowledge_trees/${knowledge_tree_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url:`);
        return null;
    }
    return result.data;
}


async function getPaperById(paper_id) {
    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/exampapers/${paper_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`KB获取试卷失败exampaper_id`);
        return null;
    }
    return response.data;
}

async function getPaperByIds(ids) {
    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/exampapers/${ids.join(',')}/list`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`KB获取试卷失败exampaper_id`);
        return null;
    }
    return response.data;
}

async function getPaperCategories(period, subject) {
    // 系统试卷库
    const query = {
        api_key: server.appKey
    }
    if (period) query.period = period;
    if (subject) query.subject = subject;
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/exampapers/categorys`,
        search: qs.stringify(query)
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`KB获取试卷失败exampaper_id`);
        return null;
    }
    return response.data;
}

async function getPaperFilters(period, subject) {
    const query = {
        api_key: server.appKey
    }
    if (period) query.period = period;
    if (subject) query.subject = subject;
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/exampapers/filters',
        search: qs.stringify(query)
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`KB获取试卷失败exampaper_id:`);
        return null;
    }
    return response.data;
}


async function getTableById(id, viewOnly = true) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/tw_specifications/${id}/info`,
        search: qs.stringify({
            api_key: server.appKey,
            view_only: viewOnly === true ? 'true' : 'false'
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教材树失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data;
}

async function getRegionSimple() {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/regions/simple/',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教材树失败: url: ${url}`);
        return null;
    }
    return result.data;
}

async function getQuestionSame(id, change_times) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/questions/${id}/change_some`,
        search: qs.stringify({
            api_key: server.appKey,
            change_times
        })
    });
    try {
        const result = await axios.get(url);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB获取教材树失败: url: ${url}`);
            return null;
        }
        return result.data;
    } catch (e) {
        logger.error(`获取相似题失败: url: ${url}`);
        return null;
    }

}

async function createTable(table, userId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.post(url, table);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB添加细目表失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(table)}`);
        return null;
    }
    return result.data;
}

async function updateTable(table_id, table, userId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/tw_specifications/${table_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.put(url, table);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB更新细目表失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(table)}`);
        return null;
    }
    return result.data;
}

async function getUnableKnowledge(knowledges) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/knowledge/',
        search: qs.stringify({
            api_key:  server.appKey
        })
    });
    const result = await axios.post(url, knowledges);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB添加细目表失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data;
}


async function exampaperBySearch(params) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/exampapers/by_search/',
        search: qs.stringify({
            api_key:  server.appKey
        })
    });
    params.filter_mkp = 'true';
    params.need_ques_num = 'true';
    const result = await axios.post(url, params);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB添加细目表失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data;
}


async function exampaperFilters(params) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/exampapers/filters/',
        search: qs.stringify({
            api_key:  server.appKey,
            period: params.period
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB添加细目表失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data;
}


async function getKnowledgeByIds(ids, fields = ['name']) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/knowledges/batch/',
        search: qs.stringify({
            api_key: server.appKey,
            ids: ids.join(','),
            filter_fields: fields.join(',')
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取知识点失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function getHotTable(params) {
    params.api_key = server.appKey;
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/hot',
        search: qs.stringify(params)
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取细目表失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取细目表失败');
    }
    return result.data;
}

async function getTableByIds(ids) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/' + ids.join(',') + '/list',
        search: qs.stringify({
            api_key: server.appKey,
            access: 'mine'
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取知识点失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}


async function deleteTableById(table_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/' + table_id,
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    await axios.delete(url);
}

async function updateTable(table_id, data) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/' + table_id,
        search: qs.stringify({
            api_key: server.appKey,
            access: 'mine'
        })
    });
    const result = await axios.put(url, data);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取知识点失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function createTable(data) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications',
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    const result = await axios.post(url, data);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取知识点失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function transmit(method = 'GET', path, query = {}) {
    query.api_key = server.appKey;
    const options = {
        url: URL.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: `/kb_api/v2${path}`,
            search: qs.stringify(query)
        }),
        method: method,
    }
    const result = await axios.request(options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB接口失败: url: ${JSON.stringify(options)}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function downloadTimesInc(table_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/' + table_id + '/download-times',
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    await axios.post(url);
}

async function getTableByRefId(id, viewOnly) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications',
        search: qs.stringify({
            api_key: server.appKey,
            relevance_id: id,
            view_only: viewOnly === true ? 'true' : 'false'
        })
    });
    try {
        const result = await axios.get(url);
        return result.data;
    } catch (e) {
        // logger.error(`KB接口失败: url: ${url}, status: ${result.status}`);
        return null;
    }

    // if (!result || result.status !== 200 || !result.data) {
    //     logger.error(`KB接口失败: url: ${url}, status: ${result.status}`);
    //     throw new Error('获取细目表失败');
    // }
    // if (result.data.code !== 0) {
    //     return null;
    // }
    // return result.data;
}


async function knowledgeAnalysis(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/knowledges/analysis',
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    try {
        const result = await axios.post(url, params);
        return result.data;
    } catch (e) {
        logger.error('KB知识点分析异常', e);
        // logger.error(`KB接口失败: url: ${url}, status: ${result.status}`);
        return null;
    }
}

async function searchTables(params) {
    params.api_key = server.appKey;
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/search',
        search: qs.stringify(params)
    });
    const result = await axios.post(url, params);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取细目表失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取细目表失败');
    }
    return result.data;
}


async function request(path, { pathParams, params, data, method } = {}) {
    try{
        let url = path;
        if (pathParams) {
            Object.keys(pathParams).forEach(key => {
                let value = encodeURIComponent(pathParams[key]);
                url = url.replace(new RegExp(`:${key}`), value);
            });
        }
        const baseURL = `${server.protocol}://${server.hostname}:${server.port}`;
        const response = await axios.request({
            method: method || 'get',
            baseURL: baseURL,
            url: url,
            ...(data ? {data: data} : {}),
            params: {
                api_key: server.appKey,
                ...(params ? params : {}),
            }
        });
        if (response.data && (response.data.code !== 0 && response.data.msg)) {
            logger.error(`KB请求失败: baseUrl: ${baseURL}, url: ${url}`);
            return null;
        }
        return response.data;
    } catch (e) {
        logger.error(e);
        throw new Error('KB');
    }
}

async function getEduFileList(params) {
    return await request('kb_api/v2/education_assistant_files/list', {
        params: params
    });
}


async function getEduFileById(id) {
    return await request('kb_api/v2/education_assistant_files/:id', {
        pathParams: {
            id: id
        }
    });
}


async function updateEduFileTimes(id, action) {
    // 增加浏览次数
    await request('kb_api/v2/education_assistant_files/times', {
        method: 'put',
        data: {
            id: id,
            action: action
        }
    });
}


async function getKnowledgesByNames(names) {
    return await request('kb_api/v2/knowledges/names/:names', {
        pathParams: {
            names: names
        }
    });
}


