
const express = require('express');
const router = express.Router();
const service = require('../../service/edu_plan/team_prep');
const { handler } = require('../../../common/middlewares/request');

// 查询教学计划
router.get('/list', handler(1, service.getList));
// 获取详细
router.get('/:id', handler(1, service.getDetail));
// 保存
router.post('', handler(1, service.save));
// 获取课程推荐试卷
router.put('/:id/status/:status', handler(1, service.updateStatus));
// 删除
router.delete('/:id', handler(1, service.deleteById));
// 获取课时资源
router.get('/chapter/lesson/resource', handler(1, service.getLessonResource));
// 保存课时资源
router.post('/chapter/lesson/resource', handler(1, service.saveLessonResource));

module.exports = router;
