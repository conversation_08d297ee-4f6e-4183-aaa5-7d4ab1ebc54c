const express = require('express');
const router = express.Router();
const service = require('../../service/edu_plan');
const { handler } = require('../../../common/middlewares/request');

// 查询教学计划
router.get('', handler(1, service.getByParams));
// 获取详细
router.get('/:id', handler(1, service.getById));
// 查看课时选项
router.get('/chapter/lesson/items', handler(1, service.getLessonItems));
// 保存
router.post('', handler(1, service.save));
// 获取课程推荐试卷
router.get('/chapter/papers', handler(1, service.getChapterPapers));
// 获取章节资源列表
router.get('/chapter/resource', handler(1, service.getChapterResource));
// 获取课时资源
router.get('/chapter/lesson/resource', handler(1, service.getChapterLessonResource));

module.exports = router;
