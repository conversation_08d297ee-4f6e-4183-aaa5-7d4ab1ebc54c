
const BOOL = {
    YES: 1,
    NO: 0
};

const QuestionDifficultyNumber = {
    VERY_EASY: 1,
    EASY: 2,
    NORMAL: 3,
    HARD: 4,
    VERY_HARD: 5
};

const QuestionDifficultyName = {
    [QuestionDifficultyNumber.VERY_EASY]: '容易',
    [QuestionDifficultyNumber.EASY]: '较易',
    [QuestionDifficultyNumber.NORMAL]: '中等',
    [QuestionDifficultyNumber.HARD]: '较难',
    [QuestionDifficultyNumber.VERY_HARD]: '困难',
}

const ExamPaperOtherType = '其他';

const PaperSourceType = {
    SYS: 'sys', // 系统
    ASSEMBLE: 'assemble', // 个人组卷
    EXAM: 'exam', // 考后巩固
    UPLOAD: 'upload', //上传
    ZYK: 'zyk', // 资源库
}

// 科目
const Subject = {
    ALL: 'all',
    CHINESE: '语文',
    MATHEMATICS: '数学',
    ENGLISH: '英语',
    PHYSICS: '物理',
    CHEMISTRY: '化学',
    BIOLOGY: '生物',
    POLITICS: '政治',
    HISTORY: '历史',
    GEOGRAPHY: '地理',
};

const PaperFrom = {
    SYS: 1, // 系统
    GROUP: 2, // 用户组卷
    EXAM: 3, // 考后巩固
    USER_PREP: 4, // 我的备课
    ZYK: 5, // 校本资源库
    JYZY: 6, // 集备作业
}

const PaperStatus = {
    // 前两个状态后续要统一
    EDITABLE: 'editable',
    NOTEDITABLE: 'notEditable',
    INIT: 'init',
    EDIT: 'edit',
    DONE: 'done',
    ERROR: 'error',
}

const PrepResourceType = {
    // 学案
    learn: 1,
    // 课件
    courseware: 2,
    // 作业
    homework: 3,
    // 我的组卷
    user_paper: 4,
}

// 用户备课资源来源
const UserPrepResourceFrom = {
    // 系统
    SYS: 1,
    // 用户自定义
    USER: 2,
    // ZYK-校本
    ZYK: 3,
}
/**
 * 学段年级关系
 * @type {({period: string, yj_grade: string, grade: string}|{period: string, yj_grade: string, grade: string}|{period: string, yj_grade: string, grade: string}|{period: string, yj_grade: string, grade: string}|{period: string, yj_grade: string, grade: string})[]}
 */
const PeriodGradeMapping = [
    { yj_grade: '一年级', grade: '一年级', period: '小学'},
    { yj_grade: '二年级', grade: '二年级', period: '小学'},
    { yj_grade: '三年级', grade: '三年级', period: '小学'},
    { yj_grade: '四年级', grade: '四年级', period: '小学'},
    { yj_grade: '五年级', grade: '五年级', period: '小学'},
    { yj_grade: '六年级', grade: '六年级', period: '小学'},
    { yj_grade: '初一', grade: '七年级', period: '初中'},
    { yj_grade: '初二', grade: '八年级', period: '初中'},
    { yj_grade: '初三', grade: '九年级', period: '初中'},
    { yj_grade: '初四', grade: '九年级', period: '初中'},
    { yj_grade: '直升初一', grade: '七年级', period: '初中'},
    { yj_grade: '直升初二', grade: '八年级', period: '初中'},
    { yj_grade: '七年制初一', grade: '七年级', period: '初中'},
    { yj_grade: '七年制初二', grade: '八年级', period: '初中'},
    { yj_grade: '七年制初三', grade: '九年级', period: '初中'},
    { yj_grade: '二四制初一', grade: '七年级', period: '初中'},
    { yj_grade: '二四制初二', grade: '八年级', period: '初中'},
    { yj_grade: '高一', grade: '高一', period: '高中'},
    { yj_grade: '高二', grade: '高二', period: '高中'},
    { yj_grade: '高三', grade: '高三', period: '高中'},
    { yj_grade: '直升高一', grade: '高一', period: '高中'},
    { yj_grade: '四年制高一', grade: '高一', period: '高中'},
    { yj_grade: '四年制高二', grade: '高二', period: '高中'},
    { yj_grade: '四年制高三', grade: '高三', period: '高中'},
    { yj_grade: '四年制高四', grade: '高三', period: '高中'},
    { yj_grade: '国际部高一', grade: '高一', period: '高中'},
    { yj_grade: '国际部高二', grade: '高二', period: '高中'},
    { yj_grade: '国际部高三', grade: '高三', period: '高中'},
    { yj_grade: '国际部高四', grade: '高三', period: '高中'},
];

const ResourceCategory = {
    COMMON: 'common', // 正常校本资源
    XYZ: 'xyz', // 小语种
}
// 备课文件类型
const ResourceType = {
    // 作业
    homework: {
        type: '作业',
        type_id: 'homework',
    },
    // 我的组卷
    user_paper: {
        type: '用户组卷',
        type_id: 'user_paper',
    },
    study_plan: {
        type: '学案',
        type_id: 'study_plan',
    },
    lesson_ware: {
        type: '课件',
        type_id: 'lesson_ware',
    },
    teach_plan: {
        type: '教案',
        type_id: 'teach_plan',
    },
    video: {
        type: '视频',
        type_id: 'video',
    },
    image: {
        type: '图片',
        type_id: 'image',
    },
    material: {
        type: '素材',
        type_id: 'material',
    },
    sys_paper: {
        type: '系统试卷',
        type_id: 'sys_paper',
    }

}

const ResourceSchool = {
    xyz: 110934, // 小语种学校
    common: 999999, // 系统资源
}
// 试卷模板类型
const PaperTemplateType = {
    SYS: 'sys',
    CUSTOM: 'custom'
};

const QuestionSource = {
    SYS: 'sys',
    UPLOAD: 'upload', // 上传
    // REF: 'ref', // 引用
    JY: 'jy', // 校验平台
    ZX: 'zx', // 智学
    ZYK: 'zyk', // 资源库
    XKW: 'xkw', // 学科网
}

const ExamStatus = {
    NOTEDITABLE: 'notEditable', // 不可编辑
    EDITABLE: 'editable', // 可编辑
}


// 题库试题类型与学科网映射关系
const TkXkwTypeMap = {
    "语文": {
        "单选题": ["选择题"],
        "填空题": ["填空题", "排序题", "判断题"],
        "默写题": ["名句名篇默写", "名篇名句默写"],
        "文言文翻译": ["翻译"],
        "文言文阅读": ["文言文阅读"],
        "古诗词鉴赏": ["诗歌鉴赏", "古代诗歌阅读", "诗词曲鉴赏", "古代诗词阅读"],
        "现代文阅读": ["现代文阅读", "小阅读"],
        "名著阅读": ["名著阅读"],
        "综合读写": ["字词书写", "书写", "信息匹配", "修改病句", "听读鉴赏", "基础知识综合", "语言表达", "语言表达题", "综合题", "语言文字运用", "综合性学习", "语言文字运用", "文言小题", "整本书阅读", "其他", "连线题", "简答题", "句子训练"],
        "写作": ["写作", "作文", "书面表达", "应用文", "微写作"]
    },
    "数学": {
        "选择题": ["单选题", "选择题"],
        "多选题": ["多选题"],
        "判断题": ["判断题"],
        "填空题": ["填空题", "排序题"],
        "解答题": ["解答题", "简答题", "证明题"],
        "计算题": ["计算题"],
        "操作题": ["连线题", "作图题", "改错题"],
    },
    "英语": {
        "单选题": ["单项选择", "选择题"],
        "填空题": ["填空题", "翻译", "翻译题", "匹配题", "画图题", "仿写", "配对", "连词成句", "连线题", "抄写题", "字母题", "完成句子", "补全对话/短文", "补全对话", "句型转换", "判断题", "单词拼写", "词汇拼写", "单词题", "语音题", "补全对话", "情景运用", "读写综合", "排序题", "其他"],
        "语法填空": ["语法填空", "短文填空", "选词填空", "对话填空"],
        "完形填空": ["完形填空", "语法选择"],
        "阅读理解": ["阅读理解", "任务型阅读"],
        "七选五": ["七选五"],
        "短文改错": ["改错", "改错题", "短文改错"],
        "书面表达": ["书面表达", "书信写作", "写作"]
    },
    "物理": {
        "选择题": ["选择题", "单选题"],
        "多选题": ["多选题"],
        "填空题": ["填空题", "课内填空", "知识点填空题"],
        "实验探究题": ["实验题", "综合题"],
        "作图题": ["作图题"],
        "解答题": ["计算题", "解答题", "简答题"],
        "材料分析题": ["材料分析题", "科普阅读题"],
        "判断题": ["判断题"],
    },
    "化学": {
        "选择题": ["单选题", "选择题"],
        "多选题": ["多选题"],
        "填空题": ["填空题", "判断题", "填空与简答", "课内填空"],
        "实验探究题": ["工业流程题", "原理综合题", "结构与性质", "实验探究题", "有机推断题", "无机推断题", "综合应用题", "科学探究题", "实验题"],
        "解答题": ["计算题", "解答题"],
        "选择填空题": ["选择填充题"]
    },
    "生物": {
        "解答题": ["解答题", "综合题", "材料分析题", "非选择题", "实验题", "解答题组", "资料分析题"],       // 放在前面, 避免非选择题和选择题匹配冲突
        "选择题": ["选择题", "选择题组", "单选题", "单选"],
        "多选题": ["多选题"],
        "填空题": ["填空题"],
        "连线题": ["连线题"],
        "判断题": ["判断题"],
        "实验探究题": ["实验探究题"]
    },
    "政治": {
        "选择题": ["选择题", "单选题", "题组", "单题", "最优选择题"],
        "多选题": ["多选题"],
        "填空题": ["填空题"],
        "辨析题": ["辨析题", "开放性试题"],
        "简答题": ["连线题", "简答题", "情境探究题", "复合题"],
        "论述题": ["综合探究题", "论述题"],
        "判断题": ["判断题", "判断说理题"],
        "材料分析题": ["分析说明题", "主观题", "材料分析题", "图表题"]
    },
    "历史": {
        "选择题": ["选择题", "单选题", "单选", "单题"],
        "多选题": ["多选题"],
        "填空题": ["填空题"],
        "判断题": ["判断题"],
        "改错题": ["改错题"],
        "简答题": ["简答题", "列举题", "识图题"],
        "连线题": ["连线题"],
        "辨析题": ["辨析题"],
        "论述题": ["论述题"],
        "材料分析题": ["材料分析题", "综合题", "材料题"],
    },
    "地理": {
        "多选题": ["多选单题", "多选题组", "多选题"],
        "选择题": ["单题", "题组", "选择题", "单选题"],
        "填空题": ["填空题"],
        "判断题": ["判断题"],
        "连线题": ["连线题"],
        "解答题": ["综合题", "解答题"]
    },
    '道德与法治': {
        '选择题': ['选择题'],
        '填空题': ['填空题', '排序题'],
        '连线题': ['连线题'],
        '判断题': ['判断题'],
        '简答题': ['简答题', '综合题'],
        '辨析题': ['辨析题']
    },
    '科学': {
        '选择题': ['选择题'],
        '填空题': ['填空题', '排序题'],
        '连线题': ['连线题'],
        '判断题': ['判断题'],
        '简答题': ['简答题'],
        '解答题': ['解答题'],
        '综合能力题': ['综合题'],
        '实验探究题': ['实验题', '探究题']
    },
    '信息科技':{
        '选择题': ['选择题'],
        '填空题': ['填空题', '排序题'],
        '判断题': ['判断题'],
        '简答题': ['简答题', '操作题'],
        '综合能力题': ['综合题'],
    },
    '历史与社会':{
        '综合题': ['非选择题'],
        '判断题': ['判断题'],
        '选择题': ['选择题', '单项选择题', '选择题组']
    },
    '信息技术':{
        '选择题': ['选择题'],
        '填空题': ['填空题'],
        '判断题': ['判断题'],
        '简答题': ['简答题', '操作题'],
        '综合能力题': ['综合题'],
    },
    '通用技术':{
        '选择题': ['选择题', '选择题组'],
        '填空题': ['填空题'],
        '判断题': ['判断题'],
        '简答题': ['简答题', '作图题'],
        '分析题': ['分析题'],
        '综合能力题': ['综合题']
    }
}
// 内部试题类型映射关系
const TypeInOutMap = {
    '选择题': ['选择题', '单选题'],
    '多选题': ['多选题'],
    '填空题': ['判断题', '填空题', '选择填空题', '默写题', "排序题"],
    '解答题': ['解答题', '计算题', '操作题', '综合能力题', '综合题', '分析题', '实验探究题', '作图题', '材料分析题', '简答题', '辨析题', '连线题', '改错题', '非选择题', '实验题', '解答题组', '论述题', '文言文翻译', '文言文阅读', '古诗词鉴赏', '现代文阅读', '名著阅读', '综合读写', '写作', "作文", "语法填空", "完形填空", "阅读理解", "七选五", "短文改错", "书面表达"],
}
// 内部题型
const InternalTypes = ['选择题', '多选题', '填空题', '解答题'];

const FileAction = {
    view_times: 'view_times',
    download_times: 'download_times'
}


const KbFileCategoryMap = {
    'courseware': ResourceType.lesson_ware.type_id,
    'lesson_plan': ResourceType.teach_plan.type_id,
    'study_plan': ResourceType.study_plan.type_id
}

const TypeIdCategoryMap = {
    [ResourceType.lesson_ware.type_id]: 'courseware',
    [ResourceType.teach_plan.type_id]: 'lesson_plan',
    [ResourceType.study_plan.type_id]: 'study_plan',
}

const BasketCategory = {
    BASKET: 'basket',
    HOMEWORK: 'homework',
}

const EduPlanChapterKey = {
    EXAM: 'exam', // 考试
    CHAPTER: 'chapter', // 章节
    LESSON: 'lesson', // 课时
}
// '单元测试', '同步练习', '月考试卷', '期中试卷', '期末试卷', '开学考试', '高考模拟', '高考复习',
// '中考模拟', '中考复习', '小升初模拟' , '小升初复习'
const EduPlanExamTypeMapping = {
    '月考': '月考试卷',
    '期中考试': '期中试卷',
    '期末考试': '期末试卷'
}

const TeamPrepStatus = {
    INIT: 'init',
    DOING: 'doing',
    DONE: 'done'
}

const EduPlanResourceType = {
    HOMEWORK: 'homework'
}

const SubjectRules = {
    '语文': ['语文'],
    '数学': ['数学', '数'],
    '英语': ['英语', '英'],
    '物理': ['物理'],
    '化学': ['化学'],
    '生物': ['生物'],
    '地理': ['地理'],
    '历史': [ '历', '史'],
    '政治': ['政', '思想品德', '思品', '道法', '道德与法治', '道德法治', '道德与法治 / 政治'],
    '道德与法治': ['政', '思想品德', '思品', '道法', '道德与法治', '道德法治', '道德与法治 / 政治']
};

const AlbumResourceType = {
    KNOWLEDGE: 10,
    QUESTION: 11,
    EXAMPAPER: 12,
    EDU_FILE: 13,
    EDU_TOOL: 14,
    TEXT_QUESTION: 15
}

module.exports = {
    BOOL,
    QuestionDifficultyNumber,
    QuestionDifficultyName,
    ExamPaperOtherType,
    PaperSourceType,
    Subject,
    PaperFrom,
    PaperStatus,
    PrepResourceType,
    UserPrepResourceFrom,
    PeriodGradeMapping,
    ResourceCategory,
    ResourceType,
    ResourceSchool,
    PaperTemplateType,
    QuestionSource,
    ExamStatus,
    TkXkwTypeMap,
    TypeInOutMap,
    InternalTypes,
    FileAction,
    KbFileCategoryMap,
    TypeIdCategoryMap,
    BasketCategory,
    EduPlanChapterKey,
    EduPlanExamTypeMapping,
    TeamPrepStatus,
    EduPlanResourceType,
    SubjectRules,
    AlbumResourceType,
}
