---
title: 智能组卷接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 智能组卷接口

智能组卷接口提供基于知识点和模板的智能试卷生成功能，支持知识点模板组卷、试卷组卷、试题模板管理和分层作业推荐等功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

# 智能组卷

## POST 知识点模板组卷

POST /v1/exampaper/knowledges/exampaper

基于知识点和模板进行智能组卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|difficulty|body|string|是|难度|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|type|body|string|是|试卷类型|
|knowledges_ids|body|array|是|知识点ID数组|
|blocks|body|array|是|题块配置|
|province|body|string|否|省份|
|city|body|string|否|城市|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/exampaper/knowledges/exampaper" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "difficulty": "中等",
    "period": "初中",
    "subject": "数学",
    "type": "期中测试",
    "knowledges_ids": [1001, 1002, 1003],
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "count": 10,
        "score": 40
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "paper_id": "generated_paper_001",
    "name": "数学期中测试卷",
    "question_count": 25,
    "total_score": 100,
    "generated_time": 1698825600000
  }
}
```

---

## POST 试卷组卷

POST /v1/exampaper/{id}/exampaper

基于已有试卷生成新的试卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|原始试卷ID|
|name|body|string|否|新试卷名称|
|difficulty_adjust|body|number|否|难度调整系数|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/exampaper/paper_001/exampaper" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "基于原试卷的新测试卷",
    "difficulty_adjust": 1.1
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "paper_id": "new_paper_001",
    "name": "基于原试卷的新测试卷",
    "original_paper_id": "paper_001",
    "question_count": 25,
    "total_score": 100
  }
}
```

---

## GET 获取试题模板列表

GET /v1/exampaper/question/template/list

获取可用的试题模板列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/exampaper/question/template/list?period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "template_001",
      "name": "初中数学期中测试模板",
      "period": "初中",
      "subject": "数学",
      "blocks": [
        {
          "name": "选择题",
          "type": "选择题",
          "count": 10,
          "score": 40
        },
        {
          "name": "填空题",
          "type": "填空题",
          "count": 8,
          "score": 32
        }
      ],
      "total_score": 100
    }
  ]
}
```

---

## POST 创建试题模板

POST /v1/exampaper/question/template

创建新的试题模板。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|body|string|是|模板名称|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|blocks|body|array|是|题块配置|
|description|body|string|否|模板描述|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/exampaper/question/template" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "初中数学月考模板",
    "period": "初中",
    "subject": "数学",
    "blocks": [
      {
        "name": "选择题",
        "type": "选择题",
        "count": 12,
        "score": 48
      },
      {
        "name": "填空题",
        "type": "填空题",
        "count": 6,
        "score": 18
      },
      {
        "name": "解答题",
        "type": "解答题",
        "count": 5,
        "score": 34
      }
    ],
    "description": "适用于初中数学月考"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "template_id": "template_new_001",
    "created": true
  }
}
```

---

## DELETE 删除试题模板

DELETE /v1/exampaper/question/template/{template_id}

删除指定的试题模板。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|template_id|path|string|是|模板ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/exampaper/question/template/template_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "deleted": true
  }
}
```

---

## POST 分层作业推题

POST /v1/exampaper/recommend/level_paper/{type}

根据学生能力进行分层作业推荐。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|path|string|是|推荐类型|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/exampaper/recommend/level_paper/ability" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "high_level": [
      {
        "id": 12345,
        "content": "解不等式组题目",
        "difficulty": "困难"
      }
    ],
    "medium_level": [
      {
        "id": 12346,
        "content": "一元二次方程题目",
        "difficulty": "中等"
      }
    ],
    "basic_level": [
      {
        "id": 12347,
        "content": "有理数计算题目",
        "difficulty": "容易"
      }
    ]
  }
}
```

---

## 返回数据结构

### 试卷生成结果结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paper_id|string|true|none|试卷ID|生成的试卷唯一标识|
|name|string|true|none|试卷名称|试卷标题|
|question_count|number|true|none|题目数量|试卷包含的题目总数|
|total_score|number|true|none|总分|试卷满分|
|generated_time|number|true|none|生成时间|时间戳|

### 试题模板结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|模板ID|唯一标识|
|name|string|true|none|模板名称|模板标题|
|period|string|true|none|学段|适用学段|
|subject|string|true|none|科目|适用科目|
|blocks|array|true|none|题块配置|题目组成部分|
|total_score|number|true|none|总分|模板满分|

### 题块配置结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|题块名称|如"选择题"、"填空题"|
|type|string|true|none|题目类型|题目的类型标识|
|count|number|true|none|题目数量|该题块包含的题目数|
|score|number|true|none|分值|该题块的总分值|

### 推荐题目结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|题目ID|唯一标识|
|content|string|true|none|题目内容|题目文本|
|difficulty|string|true|none|难度|容易/中等/困难|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|12|参数错误|请求参数格式错误或缺失必要参数|
|14|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 智能组卷原理

智能组卷功能基于以下核心算法：
- **知识点匹配**: 根据指定的知识点范围筛选合适的题目
- **难度控制**: 根据设定的难度水平调整题目分布
- **题型配置**: 按照模板配置生成不同类型的题目组合
- **重复检测**: 避免相似或重复题目的出现

### 模板系统

试题模板系统提供：
- **标准模板**: 预定义的常用考试模板
- **自定义模板**: 支持用户自定义题型和分值配置
- **模板验证**: 确保模板配置的合理性和完整性
- **模板复用**: 模板可在多次组卷中重复使用

### 分层推荐机制

分层作业推荐基于学生能力进行：
- **能力评估**: 基于历史学习数据评估学生能力水平
- **梯度设计**: 为不同能力层次提供适合的题目难度
- **个性化推荐**: 结合学生的学习特点进行个性化推题
- **动态调整**: 根据学习反馈动态调整推荐策略

### 质量保证

- **题目质量**: 所有题目都经过严格的质量审核
- **答案准确**: 确保所有题目都有标准答案和解析
- **版权合规**: 题目来源合法，符合版权要求
- **定期更新**: 题库定期更新，保持内容的时效性