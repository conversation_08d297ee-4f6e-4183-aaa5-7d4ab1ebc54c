const db = require('../../../common/db');
const logger = require('../../../common/lib/logger');
const { ObjectId } = require('mongodb');
const BussError = require('../../../common/exceptions/BussError');
const schema = require('../../../common/enums/schema');
const client = require('../../../common/client');

module.exports = {
    createTextbook,
    getTextbookList,
    updateTextbook,
    deleteTextbook,
    getTextbookById
};

async function createTextbook(textbookData) {
    try {
        const collection = db.jzl_collection(schema.jzl.textbook);
        if (textbookData.disk_file && textbookData.disk_file.id) { 
            const diskFile = await db.jzl_collection(schema.jzl.disk_file).findOne({ _id: new ObjectId(textbookData.disk_file.id), deleted: 0 , school_id: textbookData.school_id });
            if (!diskFile) {
                throw new BussError('关联的文件不存在');
            }
            textbookData.disk_file = diskFile._id.toString();
        } else if (textbookData.disk_file && textbookData.disk_file.url) {
            const diskFile = await client.jiaoyan.addDiskFile({
                ...textbookData.disk_file,
                subject: textbookData.subject,
                period: textbookData.period,
            }, textbookData.unify_sid);
            textbookData.disk_file = diskFile.id;
        } else {
            throw new BussError('教辅必须关联网盘文件');
        }
        delete textbookData.unify_sid; // 确保不存储冗余信息
        const result = await collection.insertOne({
            ...textbookData,
            createdAt: new Date(),
            updatedAt: new Date()
        });
        
        if (result.insertedId) {
            return await getTextbookById(result.insertedId);
        }
        throw new BussError('创建教辅失败');
    } catch (error) {
        logger.error('创建教辅失败:', error);
        throw error;
    }
}

async function getTextbookList(params) {
    try {
        const { offset = 0, limit = 20, period, grade, subject, keyword, user_id, school_id } = params;
        const collection = db.jzl_collection(schema.jzl.textbook);
        
        const query = {
            deleted: 0,
            school_id: school_id,
        };
        
        if (period) query.period = period;
        if (grade) query.grade = grade;
        if (subject) query.subject = subject;
        if (user_id) query.user_id = user_id;
        if (keyword) {
            query.$or = [
                { name: new RegExp(keyword, 'i') },
                { description: new RegExp(keyword, 'i') },
                { remark: new RegExp(keyword, 'i') }
            ];
        }
        
        const skip = parseInt(offset);
        const total = await collection.countDocuments(query);
        let list = await collection.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit))
            .toArray();
        const diskFileIds = list.map(item => item.disk_file).filter(Boolean);
        const diskFiles = await db.jzl_collection(schema.jzl.disk_file).find({ _id: { $in: diskFileIds.map(id => new ObjectId(id)) } }).toArray();
        list.forEach(item => {
            if (item.disk_file) {
                const diskFile = diskFiles.find(file => file._id.toString() === item.disk_file.toString());
                item.disk_file_info = diskFile || null;
            }
        });
            
        return {
            list,
            total,
            offset: parseInt(offset),
            limit: parseInt(limit)
        };
    } catch (error) {
        logger.error('获取教辅列表失败:', error);
        throw error;
    }
}

async function getTextbookById(id) {
    try {
        const collection = db.jzl_collection(schema.jzl.textbook);
        const textbook = await collection.findOne({
            _id: new ObjectId(id),
            deleted: 0
        });
        return textbook;
    } catch (error) {
        logger.error('获取教辅详情失败:', error);
        throw error;
    }
}

async function updateTextbook(id, updateData) {
    try {
        const collection = db.jzl_collection(schema.jzl.textbook);
        
        const existingTextbook = await getTextbookById(id);
        if (!existingTextbook) {
            throw new BussError('教辅不存在');
        }
        
        const result = await collection.updateOne(
            { _id: new ObjectId(id), deleted: 0 },
            { 
                $set: {
                    ...updateData,
                    updatedAt: new Date()
                }
            }
        );
        
        if (result.modifiedCount > 0) {
            return await getTextbookById(id);
        }
        throw new BussError('更新教辅失败');
    } catch (error) {
        logger.error('更新教辅失败:', error);
        throw error;
    }
}

async function deleteTextbook(id) {
    try {
        const collection = db.jzl_collection(schema.jzl.textbook);
        
        const existingTextbook = await getTextbookById(id);
        if (!existingTextbook) {
            throw new BussError('教辅不存在');
        }
        
        const result = await collection.updateOne(
            { _id: new ObjectId(id), deleted: 0 },
            { 
                $set: {
                    deleted: 1,
                    updatedAt: new Date()
                }
            }
        );
        
        return result.modifiedCount > 0;
    } catch (error) {
        logger.error('删除教辅失败:', error);
        throw error;
    }
}