---
title: 教师管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 教师管理接口

教师管理接口提供教师信息查询、班级管理、学生信息获取和属性设置等功能，整合好分数和阅卷系统数据。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取教师基本信息

GET /v1/teacher/info

获取当前教师的基本信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/info" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "teacher_001",
    "name": "张老师",
    "phone": "138****1234",
    "email": "<EMAIL>",
    "school_id": "school_001",
    "school_name": "示例中学",
    "department": "数学组",
    "title": "高级教师",
    "subject": "数学",
    "grades": ["七年级", "八年级"]
  }
}
```

---

## GET 获取好分数教师详细信息

GET /v1/teacher/hfs/info

获取教师在好分数系统中的详细信息，包括年级、科目、权限等。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/hfs/info" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "teacher_001",
    "name": "张老师",
    "phone": "138****1234",
    "school_id": "school_001",
    "school_name": "示例中学",
    "grades": [
      {
        "id": "grade_001", 
        "name": "七年级",
        "classes": [
          {
            "id": "class_001",
            "name": "七年级1班"
          }
        ]
      }
    ],
    "subjects": ["数学"],
    "period": "初中",
    "subject": "数学",
    "roles": [
      {
        "code": "teacher",
        "name": "任课教师"
      }
    ],
    "permissions": {
      "can_view_grade": true,
      "can_manage_class": true,
      "can_create_exam": true
    }
  }
}
```

---

## GET 获取阅卷教师信息

GET /v1/teacher/yj/info

获取教师在阅卷系统中的详细信息和角色权限。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/yj/info" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "yj_teacher_001",
    "name": "张老师",
    "login_name": "zhang****@example.com",
    "phone": "138****1234",
    "school_id": "school_001",
    "school_name": "示例中学",
    "grade_groups": [
      {
        "id": "gg_001",
        "name": "七年级组",
        "grades": ["七年级"],
        "period": "初中"
      }
    ],
    "education_system": {
      "type": "九年制",
      "stages": ["小学", "初中"]
    },
    "roles": [
      {
        "id": "role_001",
        "name": "年级组长",
        "permissions": ["管理年级", "查看成绩"]
      }
    ]
  }
}
```

---

## GET 获取教师班级列表

GET /v1/teacher/classes

获取当前教师任教的班级列表，仅返回当前学年的班级。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/classes" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "class_001",
      "name": "七年级1班",
      "grade": "七年级",
      "subject": "数学",
      "student_count": 45,
      "year": "2024",
      "term": "春季",
      "is_current": true
    },
    {
      "id": "class_002", 
      "name": "七年级2班",
      "grade": "七年级",
      "subject": "数学",
      "student_count": 43,
      "year": "2024",
      "term": "春季",
      "is_current": true
    }
  ]
}
```

---

## GET 获取班级学生列表

GET /v1/teacher/class/{class_id}/students

获取指定班级的学生列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|class_id|path|string|是|班级ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/class/class_001/students" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "student_001",
      "name": "张三",
      "student_number": "20240001",
      "gender": "男",
      "class_id": "class_001",
      "class_name": "七年级1班",
      "seat_number": 1,
      "status": "在读",
      "parent_phone": "139****5678"
    },
    {
      "id": "student_002",
      "name": "李四",
      "student_number": "20240002", 
      "gender": "女",
      "class_id": "class_001",
      "class_name": "七年级1班",
      "seat_number": 2,
      "status": "在读",
      "parent_phone": "138****9012"
    }
  ]
}
```

---

## PUT 更新用户属性

PUT /v1/teacher/property

更新教师的学段和科目属性信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|properties|body|array|是|属性数组，至少1个元素|
|» key|body|string|是|属性键，只能是 period 或 subject|
|» value|body|any|是|属性值|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/teacher/property" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "key": "period",
      "value": "初中"
    },
    {
      "key": "subject", 
      "value": "数学"
    }
  ]'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

### 参数说明

#### 属性键说明

|属性键|说明|可选值示例|
|---|---|---|
|period|学段|小学、初中、高中|
|subject|科目|语文、数学、英语、物理、化学等|

---

## 返回数据结构

### 教师信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|教师ID|唯一标识|
|name|string|true|none|教师姓名|真实姓名|
|phone|string|true|none|手机号|脱敏显示|
|school_id|string|true|none|学校ID|所属学校标识|
|school_name|string|true|none|学校名称|所属学校名称|
|subject|string|false|none|主要科目|任教科目|
|period|string|false|none|学段|任教学段|

### 班级信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|班级ID|唯一标识|
|name|string|true|none|班级名称|如"七年级1班"|
|grade|string|true|none|年级|所属年级|
|subject|string|true|none|科目|任教科目|
|student_count|integer|true|none|学生数量|班级人数|
|year|string|true|none|学年|如"2024"|
|is_current|boolean|true|none|当前学年|是否为当前学年|

### 学生信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|学生ID|唯一标识|
|name|string|true|none|学生姓名|真实姓名|
|student_number|string|true|none|学号|学生编号|
|gender|string|true|none|性别|男/女|
|class_id|string|true|none|班级ID|所属班级|
|seat_number|integer|false|none|座位号|班级座位编号|
|status|string|true|none|状态|在读/休学/转学等|

### 权限角色结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|角色ID|角色标识|
|name|string|true|none|角色名称|如"任课教师"|
|code|string|false|none|角色代码|角色编码|
|permissions|array|false|none|权限列表|角色权限|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|10|参数错误|请求参数格式错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 教师信息整合

系统整合了多个数据源的教师信息：
- **好分数系统**: 提供班级管理、学生信息等教务数据
- **阅卷系统**: 提供教师角色、权限、年级组等信息
- **本地数据库**: 存储用户属性和偏好设置

### 数据过滤规则

- **班级列表**: 只返回当前学年的班级，通过班级ID中的年份标识判断
- **学生信息**: 仅返回教师有任课权限的班级学生
- **敏感信息**: 手机号、邮箱等敏感信息进行脱敏处理

### 缓存策略

- 教师基本信息支持Redis缓存，提高响应速度
- 班级和学生列表实时从外部系统获取，保证数据准确性

### 权限控制

- 所有接口需要用户认证，通过 unify_sid cookie 验证身份
- 教师只能查看和管理自己任教的班级和学生
- 角色权限影响功能可用性，如年级组长可查看年级数据