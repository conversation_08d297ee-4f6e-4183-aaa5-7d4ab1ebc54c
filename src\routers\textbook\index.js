const express = require('express');
const router = express.Router();
const service = require('../../service/textbook');
const { handler } = require('../../../common/middlewares/request');

// 创建教辅
router.post('/', handler(1, service.createTextbook));

// 获取教辅列表
router.get('/', handler(1, service.getTextbookList));

// 获取教辅详情
router.get('/:id', handler(1, service.getTextbookById));

// 更新教辅
router.put('/:id', handler(1, service.updateTextbook));

// 删除教辅
router.delete('/:id', handler(1, service.deleteTextbook));

module.exports = router;