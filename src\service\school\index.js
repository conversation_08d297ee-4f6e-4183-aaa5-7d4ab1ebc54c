const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/school');

module.exports = {
    getGradeGroup,
    getTeachers,
    getResourceStatistics,
}

async function getGradeGroup(req, res) {
    const params = req.user;
    return await model.getGradeGroup(params);
}

async function getTeachers(req, res) {
    const params = req.user;
    return await model.getTeachers(params);
}

const JOI_STATISTICS = Joi.object({
    period: Joi.string().required(), // 学段
    subject: Joi.string().required(), // 科目
    grade: Joi.string().optional().allow(''),
});

async function getResourceStatistics(req, res) {
    const params = await JOI_STATISTICS.validateAsync(req.query);
    return await model.getResourceStatistics(params, req.user);
}

