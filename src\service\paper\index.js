const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/paper');


module.exports = {
    getCategories,
    search,
    getFilters,
    getDetail,
    download,
    downloadByContent
}

const JOI_GET_CATEGORIES = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    all: Joi.boolean().optional().default(false),
});

async function getCategories(req, res) {
    const params = await JOI_GET_CATEGORIES.validateAsync(req.query);
    return await model.getCategories(params);
}

const JOI_POST_SEARCH = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    grade: Joi.string().optional().allow(''),
    subject: Joi.string().required(),
    // difficulty: Joi.string().optional(),
    // exam_type: Joi.string().optional(),
    chapter_id: Joi.string().optional(),
    sort_by: Joi.string().optional().default('integrated'),
    type: Joi.string().optional(),
    // set_mode: Joi.object().optional(),
    filter_mkp: Joi.string().optional().default('true'),
    to_year: Joi.string().optional(), // .default('2025,2024,2023,2022,2021,2020'),
    provinces: Joi.array().optional(), // name:string , cities:[]
});

async function search(req, res) {
    const params = await JOI_POST_SEARCH.validateAsync(req.body);
    return await model.search(params)
}

const JOI_GET_FILTERS = Joi.object({
    period: Joi.string().optional(),
});

async function getFilters(req, res) {
    const params = await JOI_GET_FILTERS.validateAsync(req.query);
    return await model.getFilters(params)
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.alternatives().try(
        Joi.number().required(),
        Joi.string().required(),
    ).required(),
    from: Joi.number().optional().default(1)
});

async function getDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(_.assign(req.params, req.query));
    return await model.getDetail(params);
}


async function download(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(_.assign(req.params, req.query));
    return await model.download(req, res, params);
}

const JOI_DOWNLOAD_BY_CONTENT = Joi.object({
    filename: Joi.string().required(),
    content: Joi.string().required(),
});

async function downloadByContent(req, res) {
    const params = await JOI_DOWNLOAD_BY_CONTENT.validateAsync(req.body);
    return await model.downloadByContent(req, res, params);
}



