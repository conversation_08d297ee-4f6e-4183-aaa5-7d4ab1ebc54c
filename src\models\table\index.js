const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const enums = require('../../../common/enums/enums');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const basketModel = require('../basket/index');
const BussError = require('../../../common/exceptions/BussError');
const excel = require('../../../common/lib/excel');
const logger = require('../../../common/lib/logger');
const moment = require('moment');
const utils = require('../../../common/utils/utils');
const { TYPES } = require('../../../common/enums/template');


module.exports = {
    getHotTable,
    getUserTableList,
    getDetail,
    postKnowledge,
    createTable,
    updateTable,
    deleteTable,
    createPaper,
    downloadTable,
    getTableByPaper,
    search,
}

async function getHotTable(params) {
    return await client.kb.getHotTable(params);
}

async function search(params) {
    return await client.kb.searchTables(params);
}

async function getUserTableList(params) {
    const { user_id, offset, limit, period, subject, type, sort_by } = params;
    let cond = {
        user_id: user_id
    };
    if (period) cond.period = period;
    if (subject) cond.subject = subject;
    if (type) cond.type = type;
    const result = {
        total: 0,
        list: []
    };
    // result.total = await db.collection(schema.tw_specification).find(cond).count();
    // if (!result) return result;
    let docs = await db.collection(schema.tw_specification).find(cond).sort({ ctime: -1 }).toArray();
    let tableIds = [];
    for (const doc of docs) {
        tableIds.push(doc.table_id.toString());
    }
    if (!_.size(tableIds)) return result;
    const tables = await client.kb.getTableByIds(tableIds);
    //构建返回信息
    let resArr = [];
    for (let ele of tables) {
        let obj = {};
        obj.id = ele.id;
        obj.name = ele.name;
        obj.period = ele.period ? ele.period : '';
        obj.type = ele.type ? ele.type : '';
        obj.subject = ele.subject ? ele.subject : '';
        obj.ctime = ele.ctime ? ele.ctime : '';
        obj.view_times = ele.view_times ? ele.view_times : 0;
        obj.download_times = ele.download_times ? ele.download_times : 0;
        obj.province = ele.province ? ele.province : '';
        obj.grade = ele.grade ? ele.grade : '';
        if (ele.year)
            obj.year = Number(ele.year);
        else {
            if (ele.utime) {
                obj.year = moment(ele.utime).get('year');
            } else if (ele.ctime) {
                obj.year = moment(ele.ctime).get('year');
            } else {
                obj.year = '';
            }
        }

        if (period && period !== obj.period)
            continue;
        if (subject && subject !== obj.subject)
            continue;
        if (!type)
            resArr.push(obj);
        else {
            if (type === obj.type)
                resArr.push(obj);
        }
    }

    // 按时间排序
    if (sort_by === 'time') {
        resArr.sort(function (x, y) {
            const tmpX = docs.find(e => e.table_id === x.id);
            const tmpY = docs.find(e => e.table_id === y.id);
            return moment(tmpY.ctime).valueOf() - moment(tmpX.ctime).valueOf();
            // Number(y.year) - Number(x.year);
        });
    }
    //按使用次数排序
    if (sort_by === 'use_times') {
        resArr.sort(function (x, y) {
            return y.view_times - x.view_times;
        });
    }

    let count = resArr.length;
    if (limit > 0)
        resArr = resArr.slice(offset, offset + limit);
    return {
        total: count,
        list: resArr
    };
}

async function getDetail(params) {
    const {table_id} = params;
    return await client.kb.getTableById(table_id);
}


async function postKnowledge(params) {
    return await client.kb.getUnableKnowledge(params);
}

async function createTable(params) {
    const {from, user_id} = params;
    // if (from) {
    //     const kb_table = await client.kb.getTableById(from);
    //     if (_.isEmpty(kb_table)) throw new BussError('细目表不存在');
    //     for (let i = 0; i < kb_table.blocks.length; i++) {
    //         let block = kb_table.blocks[i];
    //         for (let j = 0; j < block.questions.length; j++) {
    //             let question = block.questions[j];
    //             if ((question.score && question.score === 0) || !question.score)
    //                 question.score = 5;
    //         }
    //     }
    // } else { //
    //     params.permission = 'private';
    //     params.relevance_type = null;
    //     params.relevance_id = null;
    // }
    delete params.user_id;
    delete params.from;
    params.permission = 'private';
    params.relevance_type = null;
    params.relevance_id = null;
    // 调用kb接口添加细目表
    const new_table = await client.kb.createTable(params);
    if (_.isEmpty(new_table)) throw new BussError('添加细目表失败');
    // 保存到数据库
    await db.collection(schema.tw_specification).insertOne({
        user_id: user_id,
        table_id: new_table.id,
        ctime: new Date(),
        period: params.period,
        subject: params.subject,
        type: params.type,
        grade: params.grade,
        province: params.province,
    });
    return {id: new_table.id};
}

async function updateTable(params) {
    const {user_id, table_id } = params;
    const doc = await db.collection(schema.tw_specification).findOne({table_id, user_id});
    if (_.isEmpty(doc)) throw new BussError('数据不存在');
    // 调用kb修改细目表
    delete params.permission;
    delete params.relevance_type;
    delete params.relevance_id;
    delete params.table_id;
    delete params.user_id;
    await client.kb.updateTable(table_id, params);
    return {id: table_id};
}


async function deleteTable(params) {
    const {table_id, user_id} = params;
    // 删除用
    const writeResult = await db.collection(schema.tw_specification).deleteOne({table_id, user_id});
    if (writeResult.result.ok) {
        await client.kb.deleteTableById(table_id);
    }
    return {id: table_id};
}

async function createPaper(params) {
    const {user_id, period, subject, filtered_ques, province, preference} = params;

    const algo_params = {
        period: params.period,
        subject: params.subject,
        grade: params.grade || '',
        type: '细目表组卷',
        // school_id: user && user.school_id,
        user_id: params.user_id,
        blocks: params.blocks,
        filtered_ques: filtered_ques || [],
        province: province,
        preference: preference || 0, // 选题偏好：0:默认无偏好，1：优先使用组卷最多的试题，2:优先使用最近录入的试题，3:优先使用本地试题
    };
    _filter_table_empty_knowledges(algo_params);
    _handler_question_diff(algo_params);
    const algo_paper = await client.algo.detailTablePaper(algo_params);
    if (_.isEmpty(algo_paper)) throw new BussError('组卷失败');
    // 添加试题
    const question_ids = [];
    for (const b of algo_paper.blocks) {
        question_ids.push(...b.questions.map(q => q.id));
    }
    // 获取试题
    const kb_questions = await client.kb.getQuestionByIds(question_ids);
    // 清空试题篮
    await basketModel.delete_basket({user_id, period, subject})
    // 写入试题篮
    const questions = [];
    for (const id of question_ids) {
        const q = kb_questions.find(e=> e.id === id);
        if (!q) continue;
        questions.push({
            id: q.id,
            type: q.type,
            period: period,
            subject: subject
        });
    }
    return await basketModel.post_questions({ period: period, subject: subject, questions, user_id });
}

function _handler_question_diff(table) {
    const diffArray = ['容易', '较易', '中等', '较难', '困难'];
    for (const block of table.blocks || []) {
        for (const q of block.questions || []) {
            const randomIndex = Math.floor(Math.random() * diffArray.length);
            // 不限难度的暂时随机
            if (q.difficulty === '不限') q.difficulty = diffArray[randomIndex];
        }
    }
}

function _filter_table_empty_knowledges(table) {
    for (const block of table.blocks || []) {
        block.questions = (block.questions || []).filter(e => _.isArray(e.knowledges) && _.size(e.knowledges));
    }
    table.blocks = (table.blocks || []).filter(e => _.isArray(e.questions) && _.size(e.questions));
}

async function downloadTable(params) {
    const {id} = params;
    if (id) { // 增加下载次数
        await client.kb.downloadTimesInc(id);
    }
    // 生成文件
    // const table = {};
    return generateExcel(params);
}

function generateExcel(table) {
    excel.getExcelFromTable(table, 'data/xlsx');
    return {
        url: config.get('app.url') + '/download/xlsx/' + table.name + '.xlsx'
    };
}

async function getTableByPaper(params) {
    const {user_id, id, from} = params;

    let table = await client.kb.getTableByRefId(id, true);
    if (!_.isEmpty(table)) {
        for (const block of table.blocks) {
            for (const ques of block.questions) {
                const type = ques['type'];
                const type_t = TYPES[type] || TYPES['default'];
                ques.score = ques.score || type_t.default_score;
            }
        }
        return table;
    }
    const paper = await client.kb.getPaperById(id);
    const ids = [];
    for (const block of paper.blocks) {
        ids.push(...block.questions.map(e => e.id));
    }
    const questions = await client.kb.getQuestionByIds(ids);
    for (const block of paper.blocks) {
        for (const ques of block.questions) {
            const type = ques['type'];
            const type_t = TYPES[type] || TYPES['default'];
            const question = questions.find(e => e.id === ques.id);
            ques.score = ques.score || type_t.default_score;
            ques.knowledges = question.knowledges.map(k => {
                return {
                    id: k.id,
                    name: k.name
                }
            })
        }
    }
    table = _exampaper2table(paper);
    table.relevance_type = 'exampaper';
    table.relevance_id = id;
    table.permission = 'public';
    const new_table = await client.kb.createTable(table);
    table.id = new_table.id;
    return table;
    // // 保存到用户关联表中
    // await db.collection(schema.tw_specification).insertOne({
    //     user_id: user_id,
    //     table_id: new_table.id,
    //     ctime: new Date(),
    //     period: table.period,
    //     subject: table.subject,
    //     type: table.type,
    //     grade: table.grade,
    //     province: table.province
    // });
}

function _exampaper2table(exampaper) {
    const {to_year} = utils.getAcademicYear();
    var retobj = {};
    retobj.name = exampaper.name;
    retobj.period = exampaper.period;
    retobj.subject = exampaper.subject;
    retobj.province = exampaper.province;
    retobj.type = exampaper.type;
    retobj.grade = exampaper.grade;
    retobj.year = exampaper.to_year || to_year;
    retobj.blocks = [];
    for (const block of exampaper.blocks) {
        const b = {
            type: block.questions[0].type,
            name: block.title
        };
        b.questions = block.questions.map(question => {
            return {
                type: question.type,
                period: question.period,
                subject: question.subject,
                difficulty: mapDiff(question.difficulty),
                knowledges: question.knowledges,
                score: Math.floor(block.score / block.questions.length) || question.score,
            };
        });
        retobj.blocks.push(b);
    }
    return retobj;
}

function mapDiff(difficult) {
    const diff = Math.floor(difficult / 2);
    switch (diff) {
        case 5:
            return '困难';
        case 4:
            return '困难';
        case 3:
            return '较难';
        case 2:
            return '中等';
        case 1:
            return '较易';
        default:
            return '容易';
    }
}
