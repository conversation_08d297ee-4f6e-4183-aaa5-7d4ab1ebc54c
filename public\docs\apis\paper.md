---
title: 试卷管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 试卷管理接口

试卷管理接口提供试卷搜索、详情查看、分类筛选和下载功能，支持多种试卷来源。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取试卷分类

GET /v1/paper/categories

获取试卷的分类结构，用于构建筛选界面的层级选项。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段，如：小学、初中、高中|
|subject|query|string|是|科目，如：语文、数学、英语|
|all|query|boolean|否|是否获取完整筛选结构，默认false|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/paper/categories?period=初中&subject=数学&all=true" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "category": {
      "children": [
        {
          "name": "初中",
          "children": [
            {
              "name": "数学",
              "children": [
                {
                  "key": "press_version",
                  "name": "人教版",
                  "children": [
                    {
                      "key": "type",
                      "name": "期中考试",
                      "count": 156
                    },
                    {
                      "key": "type", 
                      "name": "期末考试",
                      "count": 234
                    },
                    {
                      "key": "type",
                      "name": "单元测试",
                      "count": 445
                    }
                  ]
                },
                {
                  "key": "press_version",
                  "name": "北师大版",
                  "children": [
                    {
                      "key": "type",
                      "name": "期中考试",
                      "count": 89
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|分类数据|试卷分类树形结构|
|»» category|object|true|none|分类根节点|分类层级结构|
|»»» children|array|true|none|子分类|下级分类数组|
|»»»» name|string|true|none|分类名称|显示名称|
|»»»» key|string|false|none|分类键|用于筛选的键值|
|»»»» count|integer|false|none|数量|该分类下的试卷数量|

---

## POST 试卷搜索

POST /v1/paper/by_search

根据条件搜索试卷，支持复杂的筛选和排序。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|limit|body|number|是|每页数量，最大100|
|offset|body|number|是|偏移量，从0开始|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|grade|body|string|否|年级，允许空字符串|
|chapter_id|body|string|否|章节ID|
|sort_by|body|string|否|排序方式，默认"integrated"|
|type|body|string|否|试卷类型|
|filter_mkp|body|string|否|过滤标记，默认"true"|
|to_year|body|string|否|截止年份|
|provinces|body|array|否|省份信息数组|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/paper/by_search" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 20,
    "offset": 0,
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "sort_by": "integrated",
    "type": "期中考试",
    "to_year": "2024",
    "provinces": [
      {
        "name": "北京市",
        "cities": ["海淀区", "朝阳区"]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "count": 1256,
    "papers": [
      {
        "id": "paper_001",
        "name": "2024年北京市七年级数学期中考试卷",
        "period": "初中",
        "grade": "七年级",
        "subject": "数学",
        "type": "期中考试",
        "year": "2024",
        "province": "北京市",
        "city": "海淀区",
        "press_version": "人教版",
        "score": 100,
        "duration": 120,
        "difficulty": "中等",
        "question_count": 25,
        "view_times": 1234,
        "download_times": 567,
        "create_time": "2024-03-15",
        "source": "系统",
        "tags": ["重点学校", "期中考试"]
      }
    ]
  }
}
```

### 排序方式说明

|排序值|说明|
|------|-----|
|integrated|综合排序（推荐）|
|newest|最新优先|
|download|下载最多|
|view|浏览最多|
|difficulty|按难度排序|

---

## GET 获取筛选条件

GET /v1/paper/filters

获取试卷搜索的筛选条件选项。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段，用于获取该学段的筛选选项|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/paper/filters?period=初中" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "periods": ["小学", "初中", "高中"],
    "subjects": ["语文", "数学", "英语", "物理", "化学", "生物"],
    "grades": ["七年级", "八年级", "九年级"],
    "types": [
      {"id": "midterm", "name": "期中考试"},
      {"id": "final", "name": "期末考试"},
      {"id": "monthly", "name": "月考"},
      {"id": "unit", "name": "单元测试"},
      {"id": "mock", "name": "模拟考试"}
    ],
    "years": ["2024", "2023", "2022", "2021", "2020"],
    "difficulties": ["容易", "较易", "中等", "较难", "困难"],
    "press_versions": ["人教版", "北师大版", "苏教版", "华师大版"],
    "provinces": [
      {
        "name": "北京市",
        "cities": ["海淀区", "朝阳区", "西城区", "东城区"]
      },
      {
        "name": "上海市", 
        "cities": ["黄浦区", "徐汇区", "长宁区"]
      }
    ],
    "sort_options": [
      {"id": "integrated", "name": "综合排序"},
      {"id": "newest", "name": "最新优先"},
      {"id": "download", "name": "下载最多"},
      {"id": "view", "name": "浏览最多"}
    ]
  }
}
```

---

## GET 获取试卷详情

GET /v1/paper/{id}

获取指定试卷的详细信息，包括完整的题目结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|试卷ID|
|from|query|number|否|来源类型，默认1（系统试卷）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 试卷来源说明

|from值|说明|数据来源|
|------|-----|--------|
|1|系统试卷|知识库服务|
|2|用户组卷|MongoDB user_paper|
|3|考后巩固|MongoDB user_paper|
|4|备课资源|MongoDB user_prep_resource|
|5|校本试卷|ZYK数据库|
|6|教研平台集备|JZL数据库|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/paper/paper_001?from=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "paper_001",
    "name": "2024年北京市七年级数学期中考试卷",
    "period": "初中",
    "grade": "七年级",
    "subject": "数学",
    "type": "期中考试",
    "score": 100,
    "duration": 120,
    "subtitle": "第一学期期中测试",
    "paper_info": "本试卷共3页，25题，满分100分，考试时间120分钟",
    "cand_info": "考生须知：1.本试卷分第Ⅰ卷和第Ⅱ卷两部分",
    "attentions": "注意事项：请在答题卡上作答",
    "secret_tag": "普通",
    "gutter": 20,
    "source": "系统",
    "from_enum": 1,
    "create_time": "2024-03-15T08:00:00Z",
    "update_time": "2024-03-15T10:30:00Z",
    "volumes": [
      {
        "name": "第Ⅰ卷",
        "blocks": [
          {
            "type": "选择题",
            "title": "一、选择题",
            "note": "本大题共10小题，每小题3分，共30分",
            "default_score": 3,
            "questions": [
              {
                "id": "q_001",
                "description": "下列数中，是有理数的是（）",
                "score": 3,
                "difficulty": "容易",
                "type": "选择题",
                "blocks": {
                  "stems": ["下列数中，是有理数的是（）"],
                  "options": ["A. π", "B. √2", "C. 1/3", "D. √3"],
                  "answers": ["C"],
                  "explanations": ["有理数是可以表示为两个整数之比的数"],
                  "knowledges": ["有理数", "数的分类"],
                  "types": ["概念题"]
                },
                "knowledges": ["有理数"],
                "order": 1
              }
            ]
          }
        ]
      },
      {
        "name": "第Ⅱ卷",
        "blocks": [
          {
            "type": "解答题",
            "title": "三、解答题", 
            "note": "本大题共5小题，共50分",
            "questions": []
          }
        ]
      }
    ],
    "statistics": {
      "total_questions": 25,
      "by_type": {
        "选择题": 10,
        "填空题": 10,
        "解答题": 5
      },
      "by_difficulty": {
        "容易": 8,
        "中等": 12,
        "较难": 5
      }
    }
  }
}
```

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|响应码|0表示成功|
|» msg|string|true|none|响应消息|通常为空字符串|
|» data|object|true|none|试卷详情|完整的试卷信息|
|»» id|string|true|none|试卷ID|唯一标识|
|»» name|string|true|none|试卷名称|完整标题|
|»» volumes|array|true|none|试卷卷册|试卷结构数组|
|»»» name|string|true|none|卷册名称|如"第Ⅰ卷"|
|»»» blocks|array|true|none|题块数组|按题型分组的题目|
|»»»» type|string|true|none|题块类型|选择题/填空题/解答题等|
|»»»» title|string|true|none|题块标题|如"一、选择题"|
|»»»» questions|array|true|none|题目列表|该题块包含的题目|

---

## POST 试卷下载

POST /v1/paper/{id}/download

下载指定试卷的Word文档。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|试卷ID|
|from|body|number|否|来源类型，默认1|
|format|body|string|否|文件格式，支持docx/pdf，默认docx|
|include_answer|body|boolean|否|是否包含答案，默认true|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/paper/paper_001/download" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "from": 1,
    "format": "docx",
    "include_answer": true
  }' \
  --output "试卷.docx"
```

### 返回结果

#### 成功响应

> 200 Response

返回Word文档的二进制流，浏览器会自动下载文件。

**响应头设置：**
```
Content-Disposition: attachment; filename=试卷名称.docx
Content-Type: application/octet-stream
```

### 错误示例

#### 试卷不存在

```json
{
  "code": 5,
  "msg": "试卷不存在",
  "data": ""
}
```

---

## POST 内容下载

POST /v1/paper/content_download

根据提供的HTML内容生成并下载Word文档。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|filename|body|string|是|文件名（不含扩展名）|
|content|body|string|是|HTML格式的试卷内容|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/paper/content_download" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "自定义试卷",
    "content": "<h1>试卷标题</h1><div>试卷内容...</div>"
  }' \
  --output "自定义试卷.docx"
```

### 返回结果

#### 成功响应

> 200 Response

返回Word文档的二进制流，浏览器会自动下载文件。

**响应头设置：**
```
Content-Disposition: attachment; filename=文件名.docx
Content-Type: application/octet-stream
```

---

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|3|参数错误|请求参数不正确或缺少必需参数|
|4|业务错误|业务逻辑处理错误|
|5|空数据|查询不到相关数据|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 试卷来源处理

系统支持多种试卷来源，每种来源有不同的处理逻辑：

1. **系统试卷**: 从知识库服务获取，数据最全面
2. **用户试卷**: 从本地数据库获取，支持个人定制
3. **校本试卷**: 从学校资源库获取，体现学校特色
4. **教研试卷**: 从教研平台获取，体现教研成果

### 搜索算法

试卷搜索采用多维度匹配：
- **基础匹配**: 学段、科目、年级等
- **内容匹配**: 章节、知识点等
- **质量评分**: 下载量、浏览量等
- **地域匹配**: 优先显示本地区试卷

### 下载机制

- 支持在线预览和离线下载
- 自动处理图片和公式渲染
- 支持批量下载和格式转换
- 记录下载统计用于推荐算法

### 缓存策略

- 试卷详情采用多级缓存
- 搜索结果缓存30分钟
- 分类数据缓存1小时
- 下载文件缓存24小时