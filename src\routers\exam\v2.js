const express = require('express');
const router = express.Router();
const service = require('../../service/exam/v2');
const { handler } = require('../../../common/middlewares/request');

// 获取班级列表
router.get('/:exam_id/paper/:paper_id/classes', handler(1, service.getExamPaperClasses));
// 获取试题列表
router.get('/:exam_id/paper/:paper_id/questions', handler(1, service.getExamPaperQuestions));
// 保存教师选择试题信息
router.put('/:exam_id/paper/:paper_id/comment', handler(1, service.putExamPaperComment));
// 保存试题相似题
router.put('/:exam_id/paper/:paper_id/question/:key/sim', handler(1, service.putExamPaperQuestionSim));
// 搜索相似题
router.post('/:exam_id/paper/:paper_id/question/search', handler(1, service.searchExamPaperQuestion));
// 答题卡标记
router.put('/:exam_id/paper/:paper_id/question/:key/answer/mark', handler(1, service.putExamPaperQuestionAnswerMark));
// 标记状态
router.put('/:exam_id/paper/:paper_id/status/:status', handler(1, service.putExamPaperStatus));
// 获取讲评列表
router.get('/comment/list', handler(1, service.getCommentList));
// 获取讲评详细
router.get('/comment/:id/class/:class_id/type/:type', handler(1, service.getClassCommentDetail));
// 获取平行组卷
router.get('/:exam_id/paper/:paper_id/category/:category/paper', handler(1, service.getExamPaperCategoryPaper));


module.exports = router;
