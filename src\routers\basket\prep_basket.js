const express = require('express');
const router = express.Router();
const service = require('../../service/basket/prep_basket');
const { handler } = require('../../../common/middlewares/request');

// 获取基本信息
router.get('/resource', handler(1, service.getBasketSimple));
// 获取详细
router.get('/resource/detail', handler(1, service.getBasketDetail));
// 更新
router.post('/resource', handler(1, service.updateBasket));

module.exports = router;
