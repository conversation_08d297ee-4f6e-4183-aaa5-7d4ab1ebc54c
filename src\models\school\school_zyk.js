const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getReferenceBooks,
    getExampaperList,
}

async function getReferenceBooks(params) {
    const {school_id, period ,subject, grade } = params;
    const query = {
        school_id,
        period,
        subject
    }
    if (grade) query.grade = grade;
    const books = await db.zyk_collection(schema.zyk.school_reference_books).find(query).toArray();
    const list = books.map(e => {
        return {
            id: e._id.toString(),
            book_name: e.book_name
        }
    });
    return list;
}

async function getExampaperList(params) {
    const {press_version, period, subject, grade, reference_book_id, type, school_id} = params;
    const query = {
        is_del: false
    }
    if (press_version) query.press_version = press_version;
    if (period) query.period = period;
    if (subject) query.subject = subject;
    if (grade) query.grade = grade;
    if (reference_book_id) query.reference_book_id = reference_book_id;
    if (type) query.type = type;
    if (school_id) query.school_id = school_id;
    const result = {
        total: 0,
        list: []
    }
    result.total = await db.zyk_collection(schema.zyk.exampapers).find(query).count();
    if (!result.total) return result;
    const list = await db.zyk_collection(schema.zyk.exampapers).find(query).sort({[params.sort_by]: params.sort_order}).skip(params.offset).limit(params.limit).toArray();
    result.list = list.map(e => {
        return {
            id: e._id.toString(),
            name: e.name,
            origin_kb_id: e.origin_kb_id,
            period: e.period,
            subject: e.subject,
            type: e.type,
            user_id: e.user_id,
            user_name: e.user_name,
            ctime: e.ctime.getTime(),
            utime: e.utime.getTime(),
            view_times: e.view_times || 0,
            download_times: e.view_times || 0,
            from_enum: enums.PaperFrom.ZYK
        };
    });
    return result;
}

