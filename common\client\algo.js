/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const ALGO_SERVER = config.get('algo_server');
const ai_ques_server = config.get('ai_ques_server');
const utils = require('../utils/utils');

const options = { headers: {}, timeout: 50000 };

module.exports = {
    enhancePaper,
    recommendPaper,
    levelPaper,
    detailTablePaper,
    mergeFile,
    recommendQuestions,
    knowledgesPaper,
    getRecoQuestions,
    recommendLevelPaperByCommon,
    recommendLevelPaperByKnowledge,
    recommendLevelPaperByPaper,
    recommendSyncPaper,
    recommendLevelPaperByQues,
};

/**
 * 考后巩固组卷
 * @param params
 * @returns {Promise<*>}
 */
async function enhancePaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/enhance_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);

    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 教师计划试卷推荐
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=29688486
 * @param params
 * @returns {Promise<unknown>}
 */
async function recommendPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/organize_paper',
        port: ALGO_SERVER.port,
    });
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return result;
}

/**
 * 考后巩固组卷（简单卷，中等卷，培优卷）
 * @param params
 * @returns {Promise<*>}
 */
async function levelPaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/level_paper',
        port: ALGO_SERVER.port,
    });
    try {
        let result = await axios.post(url, params, options);
        if (!result.data || result.data.code !== 0 || !result.data.data) {
            logger.error(`算法考后巩固组卷失败,url:[${url}]`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
        return null;
    }
}


/**
 * 薄弱知识点组卷
 * @param params
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function detailTablePaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/detail_table/paper',
        port: ALGO_SERVER.port,
    });
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function mergeFile(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/files/process',
        port: ALGO_SERVER.port,
    });
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function recommendQuestions(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/questions',
        port: ALGO_SERVER.port,
    });
    console.log(url, JSON.stringify(params));
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function knowledgesPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/knowledges/paper',
        port: ALGO_SERVER.port,
    });
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法知识点组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function getRecoQuestions(questionId) {
    let url = URL.format({
        protocol: ai_ques_server.protocol,
        hostname: ai_ques_server.hostname,
        pathname: `/sim_ques/v1/recom_sim_ques`
    })
    const params = {
        sam_kb_ques_id: questionId
    }
    try {
        const result = await axios.get(url, { params: params }, options);
        if (result.status === 200) return result.data.data.reco_sim_questions;
        return [];
    } catch (e) {
        logger.error(e);
        return [];
    }
}

async function recommendLevelPaperByCommon(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/level_paper/by_common',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法推荐分层共性练习失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function recommendLevelPaperByKnowledge(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/level_paper/by_knowledge',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法推荐分层知识点练习失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function recommendLevelPaperByPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/level_paper/by_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法推荐分层平行练习失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}
/**
 * 教学计划推荐试卷
 * @param params
 * @return {Promise<void>}
 */
async function recommendSyncPaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/sync_paper',
        port: ALGO_SERVER.port,
    });
    try {
        let result = await axios.post(url, params, options);
        if (!result.data || result.data.code !== 0) {
            logger.error(`算法知识点组卷失败,url:[${url}], params：${JSON.stringify(params)}, result: ${JSON.stringify(result.data)}`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
    }
    return null;
}

/**
 * 根据试题列表信息，推荐分层练习
 * @param params
 * @return {Promise<void>}
 */
async function recommendLevelPaperByQues (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/level_paper/by_ques',
        port: ALGO_SERVER.port,
    });
    try {
        let result = await axios.post(url, params, options);
        if (!result.data || result.data.code !== 0) {
            logger.error(`算法题目组卷失败,url:[${url}], params：${JSON.stringify(params)}, result: ${JSON.stringify(result.data)}`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
    }
    return null;
}

