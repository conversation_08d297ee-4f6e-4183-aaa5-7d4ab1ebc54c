const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const mime = require('mime');
const axios = require('axios');
const xyz_model = require('./index');
// 默认文件格式
const default_type = 'docx';

module.exports = {
    getCatalogList,
    getResourceList,
    getResourceDetail,
    downloadResource,
}

async function getCatalogList(params) {
    const {book_id, chapter_id, type} = params;
    const query = {
        book_id,
        chapter_id,
        type,
        valid: enums.BOOL.YES
    }
    const list = await db.collection(schema.prep_catalog).find(query).sort({order: 1}).toArray();
    return list.map(e => {
        return {
            id: e._id.toString(),
            name: e.name
        };
    });
}

async function getResourceList(params) {
    const {book_id, chapter_id, type, type_id, catalog_id, sort_by} = params;
    const is_element = false;
    const collection = db.zyk_collection(schema.prep_resource);
    const query = {
        valid: enums.BOOL.YES
    };
    if (is_element) {
        query.catalog_id = catalog_id;
    } else {
        // const book = await client.kb.getBookDetail({book_id});
        if (book_id) query.book_id = book_id;
        if (chapter_id) {
            const book = await xyz_model.getBookDetail({book_id});
            const arr = [];
            const fun = (children, is_child = false) => {
                if (!_.size(children)) return;
                for (const child of children) {
                    if (child.id === chapter_id || is_child) {
                        if (child.key !== 'knowledge') {
                            arr.push(child.id);
                        }
                        if (child.key === 'chapter') {
                            fun(child.children, true);
                        }
                    } else {
                        if (child.key === 'chapter') {
                            fun(child.children, is_child);
                        }
                    }
                }
            }
            for (const child of book.book.children) {
                let is_child = false;
                if (child.id === chapter_id) {
                    arr.push(child.id);
                    is_child = true;
                }
                if (child.key === 'chapter') {
                    fun(child.children, is_child);
                }
            }
            query.chapter_id = {$in: arr};
        }
        if (type) query.type = type;
        if (type_id) query.type_id = type_id;
    }
    const result = {
        total: 0,
        list: []
    };
    result.total = await collection.find(query).count();
    if (!result.total) return result;
    const sort = {[sort_by]: -1};
    const list = await collection.find(query).sort(sort).skip(params.offset).limit(params.limit).toArray();
    for (const data of list) {
        data.id = data._id.toString();
        data.ctime = data.ctime.getTime();
        data.utime = data.utime.getTime();
        // if (!is_element) {
        //     delete data.children;
        // }
        delete data._id;
    }
    result.list = list;
    return result;
}

async function getResourceDetail(params) {
    const {user_id, id, type, from} = params;
    let resource = null;

    if (from === enums.UserPrepResourceFrom.USER) {
        resource = await db.collection(schema.user_xyz_prep_resource).findOne({_id: new ObjectId(id)});
        if (resource.user_id !== user_id) resource = null;
    } else {
        resource = await db.zyk_collection(schema.zyk.prep_resource).findOne({_id: new ObjectId(id)});
    }
    if (_.isEmpty(resource)) throw new BussError('资源不存在');
    // 处理字段
    resource.id = id;
    resource.ctime = resource.ctime.getTime();
    resource.utime = resource.utime.getTime();
    delete resource.valid;
    delete resource._id;
    // 子元素
    // const ids = resource.children.map(e => e.id);
    // const elements = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    // elements.forEach(e => e.id = e._id.toString());
    // if (_.size(resource.children)) {
    //     for (const index in resource.children) {
    //         const children = resource.children[index];
    //         const element = elements.find(e => e.id === children.id);
    //         const tmp = _.assign({}, element);
    //         delete tmp.valid;
    //         delete tmp._id;
    //         tmp.ctime = element.ctime.getTime();
    //         tmp.utime = element.utime.getTime();
    //         resource.children[index] = tmp;
    //     }
    //     // if (from === enums.UserPrepResourceFrom.USER && _.isEmpty(resource.file)) {
    //     //     const el = elements[0];
    //     //     if (el.mime === 'pptx') {
    //     //         resource.file = await genMergeFile(resource);
    //     //     }
    //     // }
    // }

    if (from === enums.UserPrepResourceFrom.SYS) {
        await addViewTimes(id);
        resource.view_times += 1;
    }
    return resource;
}

async function addViewTimes(id) {
    await db.zyk_collection(schema.prep_resource).updateOne({_id: new ObjectId(id)}, {$inc: {view_times: 1}});
}

async function addDownloadTimes(id, type) {
    if (type === enums.PrepResourceType.courseware || type === enums.PrepResourceType.learn) {
        await db.zyk_collection(schema.prep_resource).updateOne({_id: new ObjectId(id)}, {$inc: {download_times: 1}});
    }
}

async function downloadResource(req, res, params) {
    const {user_id, id, type, from, content} = params;

    let resource = {};
    if (from === enums.UserPrepResourceFrom.SYS) { // 系统资源
        resource = await db.zyk_collection(schema.prep_resource).findOne({_id: new ObjectId(id)});
    } else { // 用户资源
        resource = await db.collection(schema.user_xyz_prep_resource).findOne({_id: new ObjectId(id), user_id});
    }
    if (_.isEmpty(resource)) throw new BussError('资源不存在');
    let response = {};
    let fileMime = mime.getType(default_type);
    let fileName = resource.name;
    if (content) {
        const download_params = {
            html: content,
            filename: resource.name,
        }
        response = await client.utilbox.getDownloadInfo(download_params);
    } else {
        // if (from === enums.UserPrepResourceFrom.USER && _.isEmpty(resource.file)) {
        //     const ids = resource.children.map(e => e.id);
        //     const elements = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
        //     const el = elements[0];
        //     if (el.mime === 'pptx') {
        //         resource.file = await genMergeFile(resource);
        //     }
        // }
        const file_url = `${resource.file.host}${resource.file.url}`;
        response = await axios.get(file_url, { responseType: 'stream' });
        fileMime = mime.getType(resource.file.mime);
        if (!response || response.status !== 200 || !response.data) {
            throw new BussError('下载文件错误');
        }
    }
    if (_.isEmpty(response)) throw new BussError('下载文件错误');

    if (from === enums.UserPrepResourceFrom.SYS) { // 增加下载次数
        await addDownloadTimes(id, type);
    }
    // 响应数据
    res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${fileName}.${mime.getExtension(fileMime)}`)}`);
    // res.setHeader('Content-type', fileMime);
    res.setHeader('Content-type', 'application/octet-stream');
    return response.data;
}

// async function genMergeFile(resource) {
//     const ids = resource.children.map(e => e.id);
//     const list = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
//     if (_.size(list) === 1) return _.get(list[0], 'children.0.content.0.value', null);
//     const mergeParams = {
//         file_type: 'pptx',
//         file_urls: []
//     };
//     for (const doc of list) {
//         mergeParams.file_urls.push(`${doc.file.host}${doc.file.url}`);
//     }
//     const file = await client.algo.mergeFile(mergeParams);
//     if (!file) throw new BussError('获取文件失败');
//     const {origin, pathname} = new URL(file.url);
//     const result =  {
//         mime: mergeParams.file_type,
//         size: file.size,
//         host: origin,
//         url: pathname,
//         images: []
//     };
//     // 生成缩略图
//     await db.collection(schema.user_xyz_prep_resource).updateOne({_id: resource._id}, {$set: {file: result, utime: new Date()}});
//     return result;
// }
