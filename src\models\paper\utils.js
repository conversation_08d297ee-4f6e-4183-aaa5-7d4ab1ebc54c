


function _render_basket(basket) {
    // 试题篮分卷信息
    basket.score = 0;
    let block_num = 0;
    const volumes = basket.volumes;
    for (const v in volumes) {
        const volume = volumes[v];
        const blocks = volume.blocks;
        for (const b in blocks) {
            const block = blocks[b];
            const questions = block.questions;
            const n = questions.length;
            if (questions.length <= 0) {
                continue;
            }
            let s = Number(questions[0].score);
            let ts = Number(s);
            let tag = true;
            for (let i = 1; i < n; ++i) {
                if (questions[i].score !== questions[i - 1].score) {
                    tag = false;
                }
                ts += Number(questions[i].score);
            }
            const order = `${DIGIT_MAP_CHINESE[++block_num]}`;
            const ss = tag ? `，每题${s}分` : '';
            block.default_score = tag ? s : block.default_score;
            const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
            const note = (block.note.length > 0) ? `，${block.note}` : '';
            block.title = `${order}、${block.type}（${detail}${note}）`;
            // block.title = `${block.type}（${detail}${note}）`;
            basket.score += Number(ts);
        }
    }
    // 试卷名称， 2017年5月3日
    // var t = new Date();
    // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
    basket.name = basket.name || genExamPaperName(basket);
    // 副标题
    basket.subtitle = basket.subtitle || '';
    // 考试时间，单位分钟
    basket.duration = basket.duration || 120;
    // 试卷信息栏，考试总分：100分；考试时间：100分钟
    const paper_info = `考试总分：${basket.score}   考试时间：${basket.duration}`;
    basket.paper_info = basket.paper_info || paper_info;
    // 候选人信息栏
    const line = '__________ ';
    const cand_info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

    basket.cand_info = basket.cand_info || cand_info;
    // 注意事项
    basket.attentions = basket.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
    // 保密标记文字
    basket.secret_tag = basket.secret_tag || '绝密★启用前';
    // 装订线
    basket.gutter = basket.gutter || 0;
    return basket;
}

function traverse_questions(basket, ques_map) {
    const fileds = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
    for (const volume of basket.volumes || []) {
        for (const block of volume.blocks || []) {
            for (const i in block.questions) {
                const question = block.questions[i];
                const q = ques_map[question.id];
                if (!q) {
                    delete block.questions[i];
                    continue;
                }
                // 去除返回的试题电子化答案、解析、解答等信息
                if (q.blocks) {
                    q.blocks = {
                        stems: q.blocks.stems,
                        knowledges: q.blocks.knowledges,
                        types: q.blocks.types,
                    }
                }
                block.questions[i] = _.pick(q, fileds);
                block.questions[i]['score'] = question['score'];
            }
        }
    }
}


function genExamPaperName(basket) {
    const nowDate = new Date();
    const year = nowDate.getFullYear();

    // 学年
    const academicYearStartDate = new Date();
    academicYearStartDate.setMonth(0, 1);
    academicYearStartDate.setHours(0, 0, 0, 0);

    const academicYearEndDate = new Date();
    academicYearEndDate.setMonth(7, 15);
    academicYearEndDate.setHours(0, 0, 0, 0);
    let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
    if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
        academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
    }
    // 学期
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);

    let semester = "上";//学期
    if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
        semester = '下';
    }
    const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
    let info = `${academicYearLong}学年${basket.period}${basket.subject} (${semester}) ${basket.type || ''}试卷(${formatDateStr})`;
    return info;
}
