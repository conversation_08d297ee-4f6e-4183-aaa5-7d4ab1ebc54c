const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('yj_api_server');
const qs = require('querystring');
const jwt = require('jsonwebtoken');
const enums = require('../enums/enums');
const utils = require('../utils/utils');

module.exports = {
    getUserInfo,
    getTeachersByFilter,
    getGradeGroup,
    getTeachers,
    getTeacherAuthRoles,
    getSchoolSetting,
    getTeacherSelecteInfo,
    getSchoolAcademicInfo,
}

/**
 * 获取教师信息
 * @param userId
 * @returns {Promise<null|*>}
 */
async function getUserInfo(userId) {
    let tokenKey = Buffer.from(server.appCenterKey, 'base64');
    const token = jwt.sign({ 'removed': true },tokenKey,{
            algorithm: 'HS512',
            jwtid: userId.toString(),
            noTimestamp: false
    });
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v353/anno/user/profile/ForAppcenter',
        search: qs.stringify({
            token: token
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取学校信息异常: ${JSON.stringify(config)}`);
        return null;
    }
    if (response.data.code !== 0) {
        logger.error(`阅卷获取教师信息异常: ${JSON.stringify(response.data)}`);
        return null;
    }
    return response.data && response.data.data || null;
}

/**
 * 获取教师信息
 * @param params.userIds
 * @param params.phone
 * @returns {Promise<null|*>}
 */
async function getTeachersByFilter(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v1/anno/users/profile/ForAppcenter',
    });
    const response = await axios.post(url, params);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取教师信息异常: ${JSON.stringify(config)}`);
        return null;
    }
    return response.data && response.data.data || null;
}

async function getGradeGroup(schoolId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v1/ctb/school/gradeGroups',
        search: qs.stringify({
            schoolId: schoolId
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取学校信息异常: ${JSON.stringify(config)}`);
        return null;
    }
    return response.data && response.data.data || null;
}


async function getTeachers(schoolId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v1/teacherInfo/Tiku',
        search: qs.stringify({
            schoolId: schoolId
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取学校信息异常: ${JSON.stringify(config)}`);
        return null;
    }
    return response.data && response.data.data || null;
}

async function getTeacherSelecteInfo(userId, schoolId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v353/anno/teacher/selected/nianji/subject',
        search: qs.stringify({
            userId: +userId,
            schoolId: schoolId
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取教师选择年级科目信息异常: ${JSON.stringify(config)}`);
        return null;
    }
    return response.data && response.data.data || null;
}


async function getTeacherAuthRoles(id) {
    let result = [];
    const teacher = await getUserInfo(id);
    if (!teacher) return result;
    const roleMap = new Map();
    if (teacher.schoolId && teacher.phone) { // 超级管理员
        const admin = `${teacher.schoolId}admin`;
        if (teacher.phone === admin) {
            roleMap.set('admin', {title: '超级管理员'});
        }
    }
    const roles = teacher['[role]'] || [];
    for (const role of roles) {
        let r = roleMap.get(role.title);
        if (!r) {
            r = {
                key: 'role',
                name: role.title,
                children: []
            };
            roleMap.set(role.title, r);
        }
        const nianji = role['[nianji]'][0]; // 映射学段
        if (!nianji) continue;
        const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === nianji || e.grade === nianji);
        if (!gradeInfo) continue;
        let period = r.children.find(e => e.name === gradeInfo.period);
        if (!period) {
            period = {
                key: 'period',
                name: gradeInfo.period,
                children: []
            };
            r.children.push(period);
        }
        const xueke = role['[xueke]'][0];
        if (!xueke) continue;
        const standardSubject = utils.getStandardSubject(period.name, xueke);
        if (standardSubject) {
            let subject = period.children.find(e => e.name === standardSubject);
            if (!subject) {
                subject = {
                    key: 'subject',
                    name: standardSubject
                };
                period.children.push(subject);
            }
        }
    }
    if (roleMap.size > 0) result = Array.from(roleMap.values());
    return result
}

async function getSchoolSetting(schoolId) {
     const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v0/school/setting',
        search: qs.stringify({
            schoolId: schoolId
        })
    });
    const response = await axios.get(url);
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取学校信息异常: ${JSON.stringify(config)}`);return null;
    }
    return response.data && response.data.data || null;
}

async function getSchoolAcademicInfo(schoolId, unify_sid) {
     const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v1/user/getAcademicInfo',
        search: qs.stringify({
            schoolId: schoolId
        })
    });
    const response = await axios.get(url, { headers: { 'Cookie': `unify_sid=${unify_sid}` } });
    if (!response || response.status !== 200 || !response.data) {
        logger.error(`阅卷获取学校信息异常: ${JSON.stringify(config)}`);return null;
    }
    return response.data && response.data.data || null;
}