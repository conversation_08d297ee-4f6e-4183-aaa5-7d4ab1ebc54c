const config = require('config');
const axios = require('axios');
const server = config.get('mkp_server');
const logger = require('../lib/logger');

module.exports = {
    getTeacherReviewList,
}
async function request(path, { pathParams, params, data, method } = {}) {
    try{
        let url = path;
        if (pathParams) {
            Object.keys(pathParams).forEach(key => {
                let value = encodeURIComponent(pathParams[key]);
                url = url.replace(new RegExp(`:${key}`), value);
            });
        }
        const baseURL = `${server.protocol}://${server.hostname}:${server.port}`;
        const response = await axios.request({
            method: method || 'get',
            baseURL: baseURL,
            url: url,
            ...(data ? {data: data} : {}),
            params: {
                api_key: server.appKey,
                ...(params ? params : {}),
            }
        });
        if (response.data && (response.data.code !== 0 && response.data.msg)) {
            logger.error(`MKP请求失败: baseUrl: ${baseURL}, url: ${url}`);
            return null;
        }
        return response.data && response.data.data;
    } catch (e) {
        logger.error(e);
        throw new Error('MKP');
    }
}

async function getTeacherReviewList(params) {
    return await request('bzp_api/v3/school_teacher/review_list', {
        params: params
    });
}


