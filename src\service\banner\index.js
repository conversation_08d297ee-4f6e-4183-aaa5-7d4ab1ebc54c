const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/banner');
const enums = require('../../../common/enums/enums');

module.exports = {
    getList,
}

const JOI_GET_LIST = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
});

async function getList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    return await model.getList(params, req.user);
}
