const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/basket/prep_basket');

module.exports = {
    getBasketSimple,
    getBasketDetail,
    updateBasket,
}

const JOI_GET_SIMPLE = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).optional(),
    type_id: Joi.string().optional(),
});

async function getBasketSimple(req, res) {
    const params = await JOI_GET_SIMPLE.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getBasketSimple(params);
}

async function getBasketDetail(req, res) {
    const params = await JOI_GET_SIMPLE.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getBasketDetail(params);
}

const JOI_UPDATE = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    type_id: Joi.string().optional(),
    children: Joi.array().items(Joi.object({
        id: Joi.string().required()
    })).optional()
});

async function updateBasket(req, res) {
    const params = await JOI_UPDATE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.updateBasket(params);
}
