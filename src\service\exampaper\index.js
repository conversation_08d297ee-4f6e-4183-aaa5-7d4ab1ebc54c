const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/exampaper');
const enums = require('../../../common/enums/enums');

module.exports = {
    paperToPaper,
    knowledgePaper,
    recommendLevelPaper,
    paperByQuestions,
}

const JOI_PAPER_TO_PAPER = Joi.object({
    id: Joi.number().required(),
    filtered_ques: Joi.array().items(Joi.number()),
    preference: Joi.number().default(0), // 选题偏好：0:默认无偏好，1：优先使用组卷最多的试题，2:优先使用最近录入的试题，3:优先使用本地试题
    province: Joi.string().optional(),
});


async function paperToPaper(req, res) {
    const params = await JOI_PAPER_TO_PAPER.validateAsync(_.assign(req.params, req.body));
    params.user_id = req.user.id;
    return await model.paperToPaper(params);
}

const JOI_KNOWLEDGE_PAPER = Joi.object({
    difficulty: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    province: Joi.string().optional(),
    city: Joi.string().optional(),
    type: Joi.string().required(),
    knowledges_ids: Joi.array().items(Joi.number()).min(1),
    blocks: Joi.array().items(Joi.object({
        type: Joi.string().required(),
        num: Joi.number().required()
    })).required().min(1)
});

async function knowledgePaper(req, res) {
    const params = await JOI_KNOWLEDGE_PAPER.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.knowledgePaper(params);
}


const JOI_RECOMMEND_LEVEL_PAPER = Joi.object({
    period: Joi.string().required(),                // 必选，学段
    subject: Joi.string().required(),               // 必选，科目
    grade: Joi.string().optional(),                 // 年级
    school_id: Joi.number().optional(),             // 学校id
    school_name: Joi.string().optional(),           // 学校名字
    is_level: Joi.boolean().optional(),             // 是否分层

    students: Joi.object({
        foundation: Joi.array().items(Joi.string()).optional(),    // 基础层学生（成绩比较差）
        intermediate: Joi.array().items(Joi.string()).optional(),  // 中等层学生（中等水平学生）
        advance: Joi.array().items(Joi.string()).optional(),       // 优等生（成绩较好学生）
    }).optional(),

    // 资源、单场阅卷考试推题专用
    paper_id: Joi.alternatives().try(               // 试卷id
        // Joi.number(),
        Joi.string()
    ).optional(),
    paper_from: Joi.string().optional(),            // paper来源，用于去对应平台或库或接口查询试卷、作业、yj、tiku、xb、kb、zyl
    // paper_from === zyl
    user_id: Joi.string().optional(),               // 作业篮 user_id
    // 薄弱知识点推题专用
    know_list: Joi.array().items(Joi.object({
        id: Joi.number().required(),                // 知识点id
        name: Joi.string().required(),              // 知识点名称
    })).optional(),                                 // 薄弱知识点列表
    // 非资源推题专用
    paper_list: Joi.array().items(Joi.string()).optional(),  // 试卷id_list
    reco_type: Joi.string().optional(),             // origin：原始共性错题；sim_reco: 只要推荐题； origin_reco: 知识点下原题+推荐题
    reco_ques_num: Joi.object().pattern(
        Joi.string(),                               // 题型
        Joi.number()                                // 数量
    ).optional(),                                   // 推荐题不同题型对应的推荐题量
    // 共性错题专用
    common_score_rate: Joi.string().optional(),     // 学生得分率，根据此范围筛选共性题
    common_ques_num: Joi.object().pattern(
        Joi.string(),                               // 题型
        Joi.number()                                // 数量
    ).optional(),                                   // 原始试题
    // 薄弱知识点推题专用
    origin_ques_num: Joi.object().pattern(
        Joi.string(),                               // 题型
        Joi.number()                                // 数量
    ).optional(),                                   // 原始试题

    // 题目列表
    ques_list: Joi.array().items(Joi.object({
        qid: Joi.string().optional().allow(''),               // 题目id
        from_type: Joi.string().optional().allow(''),         // homework, yj_exam......
        exam_id: Joi.string().optional().allow(''),           // 考试id
        exam_name: Joi.string().optional().allow(''),         // 考试名称
        ques_name: Joi.string().optional().allow(''),         // 试题名称
        type: Joi.string().required(),                        // 题型
        description: Joi.string().allow('').required(),       // 试题描述
        blocks: Joi.object().required(),            // 试题结构化数据
    })).optional(),                                 // 题目列表
});

async function recommendLevelPaper(req, res) {
    const params = await JOI_RECOMMEND_LEVEL_PAPER.validateAsync(req.body);
    const type = req.params.type;
    if (params.grade) {
        const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === params.grade || e.grade === params.grade);
        params.grade = gradeInfo && gradeInfo.grade || params.grade;
    }
    return await model.recommendLevelPaper(params, type);
}

const JOI_PAPER_BY_QUESTIONS = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    questions: Joi.array().items(Joi.alternatives().try( // 答案
        Joi.string(),
        Joi.number(),
    )).min(1).required(), // 试题id列表
    grade: Joi.string().optional(), // 年级
    type: Joi.string().optional(), // 试卷类型
});
async function paperByQuestions(req, res) {
    const params = await JOI_PAPER_BY_QUESTIONS.validateAsync(req.body);

     if (params.grade) {
        const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === params.grade || e.grade === params.grade);
        params.grade = gradeInfo && gradeInfo.grade || params.grade;
    }
    return await model.paperByQuestions(params);
}