---
title: 内部调用接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 内部调用接口

内部调用接口用于系统间的数据交换和服务调用，所有接口都需要使用access-key进行认证。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## 认证方式

所有内部接口都需要在请求头中携带有效的access-key：

```shell
# 所有请求都需要携带access-key
curl -H "access-key: YOUR_ACCESS_KEY" ...
```

## GET 获取试卷详情

GET /v1/internal/paper/:id

获取指定试卷的详细信息，支持系统试卷和用户试卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|试卷ID，数字为系统试卷，字符串为用户试卷|
|from|query|number|否|来源类型，默认值1|
|access-key|header|string|是|内部调用认证密钥|

### 请求示例

```shell
# 获取系统试卷
curl -X GET "http://localhost:8055/v1/internal/paper/12345?from=1" \
  -H "access-key: your_access_key_here"

# 获取用户试卷
curl -X GET "http://localhost:8055/v1/internal/paper/user_paper_001" \
  -H "access-key: your_access_key_here"
```

### 返回结果

#### 成功响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "id": "12345",
    "name": "2024年中考数学模拟试卷",
    "period": "初中",
    "grade": "九年级", 
    "subject": "数学",
    "type": "模拟考试",
    "from_year": "2024",
    "to_year": "2024",
    "press_version": "人教版",
    "province": "北京市",
    "city": "海淀区",
    "year": "2024",
    "view_times": 1256,
    "download_times": 432,
    "subtitle": "第一次模拟考试",
    "score": 100,
    "duration": 120,
    "paper_info": "本试卷共3页，25题，满分100分，考试时间120分钟",
    "cand_info": "考生须知：1.本试卷分第Ⅰ卷和第Ⅱ卷两部分...",
    "attentions": "注意事项：请在答题卡上作答",
    "secret_tag": "普通",
    "gutter": "standard",
    "template": "exam",
    "source": "系统",
    "source_id": "12345",
    "status": "已发布",
    "exam_status": "可用",
    "volumes": [
      {
        "name": "第一卷",
        "blocks": [
          {
            "name": "选择题",
            "instruction": "本大题共10小题，每小题3分，共30分",
            "questions": [
              {
                "order": 1,
                "question_id": "q_001",
                "score": 3
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 试卷不存在

```json
{
  "code": 0,
  "msg": "成功",
  "data": {}
}
```

### 错误示例

#### 认证失败

```json
{
  "code": 401,
  "msg": "access-key验证失败",
  "data": null
}
```

---

## GET 获取试题篮内容

GET /v1/internal/basket

获取用户试题篮的详细内容，支持多种操作模式。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|user_id|query|string|是|用户ID|
|category|query|string|否|试题篮类型，默认"basket"|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|paper_id|query|string/number|否|试卷ID|
|op|query|string|否|操作类型：delete/merge，默认delete|
|source_type|query|string|否|来源类型，默认SYS|
|template|query|string|否|模板类型：standard/exam/homework|
|access-key|header|string|是|内部调用认证密钥|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/internal/basket?user_id=user123&period=初中&subject=数学&op=merge" \
  -H "access-key: your_access_key_here"
```

### 返回结果

#### 成功响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "id": "basket_123",
    "name": "我的试题篮",
    "period": "初中",
    "subject": "数学",
    "grade": "八年级",
    "type": "综合",
    "template": "standard",
    "volumes": [
      {
        "name": "主卷",
        "blocks": [
          {
            "name": "选择题",
            "instruction": "每题3分",
            "questions": [
              {
                "question_id": "q_001",
                "score": 3,
                "order": 1
              }
            ]
          }
        ]
      }
    ],
    "statistics": {
      "total_questions": 15,
      "total_score": 100
    }
  }
}
```

### 错误示例

#### 试题数量超限

```json
{
  "code": 1000,
  "msg": "试题篮试题数量超过上限",
  "data": null
}
```

---

## GET 获取试题详情

GET /v1/internal/question/:id

获取指定试题的详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|试题ID|
|paper_id|query|string|否|试卷ID，用于关联查询|
|access-key|header|string|是|内部调用认证密钥|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/internal/question/q_12345?paper_id=paper_001" \
  -H "access-key: your_access_key_here"
```

### 返回结果

#### 成功响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "id": "q_12345",
    "type": "single_choice",
    "period": "初中",
    "subject": "数学",
    "grade": "八年级",
    "difficulty": 3,
    "year": "2024",
    "blocks": {
      "stem": "下列说法正确的是（）",
      "options": [
        "A. 有理数都是正数",
        "B. 0是自然数",
        "C. 负数都是有理数",
        "D. 整数包括正整数和负整数"
      ],
      "answer": "B",
      "explanation": "根据数学定义，0是自然数...",
      "solution": "解题步骤：1. 分析各选项..."
    },
    "knowledges": [
      {
        "id": "k_001",
        "name": "有理数的概念",
        "level": 2
      }
    ],
    "audio": null,
    "description": "基础概念题",
    "comment": "考查对基本概念的理解"
  }
}
```

#### 试题不存在

```json
{
  "code": 0,
  "msg": "成功",
  "data": null
}
```

### 错误示例

#### 认证失败

```json
{
  "code": 401,
  "msg": "access-key无效或已禁用",
  "data": null
}
```

---

## 认证错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 401 | access-key验证失败 | 请求头中缺少access-key |
| 401 | access-key无效或已禁用 | access-key不存在或状态被禁用 |
| 401 | 内部接口认证失败 | 其他认证相关错误 |

## 使用说明

1. **认证机制**: 所有内部接口都需要有效的access-key进行认证
2. **数据来源**: 支持多种数据源（系统、用户、校本资源）
3. **ID类型**: 数字ID通常表示系统资源，字符串ID表示用户资源
4. **错误处理**: 统一的错误码和错误信息格式
5. **性能优化**: 内部接口优化了查询性能，适合高频调用

## 安全注意事项

- access-key需要妥善保管，不可泄露
- 定期更换access-key提高安全性
- 监控access-key的使用情况
- 及时禁用不再使用的access-key