/**
 * 好分数教师端
 * wiki:
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('hfs_teacher_v3_server');
const qs = require('querystring');
const utils = require('../utils/utils');
const options = { headers: {}, timeout: 50000 };
const subject_utils = require('../utils/subject_utils');


module.exports = {
    getExamList,
    getPaperQuestions,
    getClassPaperStatistics,
    getTeacherInfoById,
    getTeacherInfoByToken,
    getTeacherExamList,
    getExamPaperClasses,
    getExamPaperQuestionAnswerPictures,
    getExamPaperQuestionAnswerPictureUrl,
    getTeacherClasses,
    getTeacherClassStudents,
    getExamPaperClassComparison,
    getExamPaperQuestionDetail,
    getExamPaperStudentQuestionAnswer,
    getTeacherInfo,
    getExamPaperQuestionDetailV2,
};

/**
 * 考后巩固组卷
 * @param teacherId 教师ID
 * @returns {Promise<*>}
 */
async function getExamList (teacherId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/exams',
        port: server.port,
        search: qs.stringify({
            teacherId: teacherId,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || !result.data || result.data.code !== 0) {
        return null;
    }
    result = result.data.data;
    const new_result = [];
    if (_.size(result)) {
        for (const exam of result) {
            if (!_.size(exam.classes)) continue;
            const subject = subject_utils.regularSubject(exam.subject);
            if (!subject) continue;
            exam.regular_subject = subject;
            // for (const c of exam.classes) {
            //     if (!c.name.includes('班')) c.name = c.name + '班';
            // }
            new_result.push(exam);
        }
    }
    return new_result;
}

/**
 * 获取试题详细
 * @param userId 用户ID/教师ID
 * @param examId number 考试ID
 * @param paperId string 科目ID
 * @param classId string 班级ID
 * @returns {Promise<void>}
 */
async function getPaperQuestions(userId, examId, paperId, classId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/classes/question-detail',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || !result.data || result.data.code !== 0) {
        return null;
    }
    return result.data.data;
}

/**
 * 获取班级数据统计
 * @param userId 用户ID/教师ID
 * @param examId 考试ID
 * @param paperId 科目ID
 * @param classId 班级ID
 * @returns {Promise<unknown>}
 */
async function getClassPaperStatistics(userId, examId, paperId, classId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/classes/exams/papers/statistics',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            scoreType: 0,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || !result.data || result.data.code !== 0) {
        return null;
    }
    return result.data.data;
}

async function getTeacherInfoById(id) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/info/tiku',
        port: server.port,
        search: qs.stringify({
            userId: id,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || !result.data || result.data.code !== 0) {
        return null;
    }
    return result.data.data;
}


async function getTeacherInfoByToken(token) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/info/hfs-token',
        port: server.port,
        search: qs.stringify({
            hfsToken: token,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || !result.data || result.data.code !== 0) {
        return null;
    }
    return result.data.data;
}

async function getTeacherExamList(userId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/exam/list',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            // type: type,
            appKey: server.appKey,
        })
    });
    try {
        let result = await axios.get(url, options);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB获取试题信息失败: url: ${url}`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
        return null;
    }
}


async function getExamPaperClasses(userId, examId, paperId) {

    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/classes/exam/paper/classes',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            scoreType: 0,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    const list = _.get(result, 'data.data', null);
    if (_.size(list)) {
        _class_sort(list);
    }
    return list;
}

function _class_sort(list) {
    if (!_.size(list)) return;
    list.sort((a, b) => {
        const numA = utils.matchNumber(a.name || a.class_name);
        const numb = utils.matchNumber(b.name || b.class_name);
        return numA - numb;
    })
}

/**
 * 获取答题图片
 * @param userId
 * @param examId
 * @param paperId
 * @param questionId
 * @param classIds
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getExamPaperQuestionAnswerPictures(userId, examId, paperId, questionId, classIds) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/classes/exam/paper/question/answer/pictures',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            questionId: questionId,
            classIds: classIds,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 获取答题图片
 * @param userId
 * @param examId
 * @param paperId
 * @param questionId
 * @param classIds
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getExamPaperQuestionAnswerPictureUrl(examId, paperId, pictures) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/image/answer/picture/url',
        port: server.port,
        search: qs.stringify({
            appKey: server.appKey,
        })
    });
    const data = {
        examId,
        paperId,
        pictures
    }
    let result = await axios.post(url, data, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

/**
 * 获取教师班级
 * @param user_id
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getTeacherClasses(user_id) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/classes',
        port: server.port,
        search: qs.stringify({
            userId: user_id,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

/**
 * 获取教师班级学生
 * @param user_id 教师ID
 * @param class_id 班级ID
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getTeacherClassStudents(user_id, class_id) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/class-students',
        port: server.port,
        search: qs.stringify({
            userId: user_id,
            classId: class_id,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function getExamPaperClassComparison(userId, examId, paperId, classId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/classes/exams/papers/comparison',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            scoreType: 0,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function getExamPaperQuestionDetail(userId, examId, paperId) {

    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/exam/paper/question-detail',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    const question_info = _.get(result, 'data.data', null);
    // if (question_info) {
    //     if (_.size(question_info.class_info)) { // 班级排序
    //         _class_sort(question_info.class_info);
    //         for (const class_info of question_info.class_info) {
    //             class_info.question_score = _question_sort(class_info.question_score);
    //         }
    //     }
    //     // 试题排序
    //     question_info.questions = _question_sort(question_info.questions);
    // }
    return question_info;
}

function _question_sort(list) {
    if (!_.size(list)) return list;
    let level = 0;
    for (const q of list) {
        const key_arr = (q.key || '').split('.');
        if (_.size(key_arr) > level) {
            level = _.size(key_arr);
        }
    }
    if (level === 0) return list;
    for (const q of list) {
        const key_arr = (q.key || '').split('.');
        for (let i = 1; i <= level; i++) {
            try {
                q[`key_sort_${i}`] = key_arr.length > i ? Number(key_arr[i]) : 999;
            } catch (e) {
                q[`key_sort_${i}`] = 999;
            }
        }
    }
    const arr = Array.from({length: level}, (item, index) => index);
    list =  _.orderBy(list, arr.map(e => `key_sort_${e + 1}`), arr.map(e => 'asc'));
    for (const q of list) {
        arr.forEach(e => {
            delete q[`key_sort_${e + 1}`];
        });
    }
    return list;
}

function _class_sort(list) {
    if (!_.size(list)) return;
    list.sort((a, b) => {
        const numA = utils.matchNumber(a.name || a.class_name);
        const numb = utils.matchNumber(b.name || b.class_name);
        return numA - numb;
    })
}


async function getExamPaperStudentQuestionAnswer(userId, examId, paperId, classId, studentId, questionId) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/students/exams/papers/questions/brief',
        port: server.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            studentId: studentId,
            questionId: questionId,
            appKey: server.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}



async function getTeacherInfo(unify_sid) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/v3/teachers/info',
        port: server.port,
        search: qs.stringify({
            appKey: server.appKey,
        })
    });
    // {
    //     id: "000000003748837837881344",
    //     name: "韩校长",
    //     avatar: "",
    //     phone: "***********",
    //     account: "***********",
    //     schoolId15: 55553,
    //     schoolId: 55553,
    //     schoolName: "精准教学广州04",
    //     roles: [
    //       {
    //         title: "校管理员",
    //         from: 10,
    //       },
    //       {
    //         title: "任课老师",
    //         from: 10,
    //       },
    //     ],
    //     roleAll: [
    //       {
    //         title: "任课老师",
    //         from: 10,
    //       },
    //     ],
    //     classes: [
    //       {
    //         id: "000000000000000251043137",
    //         name: "演示10班",
    //         grade: "高二",
    //         roles: [
    //           "语文",
    //           "数学",
    //         ],
    //         year: "2025",
    //         isVisible: true,
    //         newest: 0,
    //       }
    //     ],
    //     isLiveTeacher: false,
    //     organizationType: 1,
    //     isOpenPractice: true,
    //     isOpenMessage: true,
    //     logoPath: "https://ypt-oss.yunxiao.com/v1/production/dir_portal-be-boss/images/logoSchool/upload_b997086d34f699e0d4126dbecdbbcd49.jpg",
    //     vipType: true,
    //     schoolVersion: {
    //       schoolType: "普通",
    //       vipType: 1,
    //       displayVersion: true,
    //       zdyVersion: {
    //         open: false,
    //         time: "2024-12-23T16:00:00.000Z",
    //       },
    //       endTime: "2025-05-30",
    //       startTime: "2024-12-01",
    //       useType: "旗舰版",
    //     },
    //     schoolId20: 55553,
    //     banner: "https://hfs-oss.yunxiao.com/production/000001/teacher_banner.jpg",
    //     user_tag: "{\"targetSchools\":55553,\"targetUserType\":1}",
    //   }
    const newOptions = _.assign({}, options);
    newOptions.headers['Cookie'] = unify_sid;
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function getExamPaperQuestionDetailV2(cls_id, exam_id, paper_id, unify_sid) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: `/v3/classes/${cls_id}/exams/${Number(exam_id)}/papers/${paper_id}/question-detail-v2`,
        port: server.port,
    });
    try {
        const opt = _.assign({}, options);
        opt.headers = {Cookie: `unify_sid=${unify_sid}`};
        let result = await axios.get(url, opt);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB获取试题信息失败: url: ${url}`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
        return null;
    }
}
