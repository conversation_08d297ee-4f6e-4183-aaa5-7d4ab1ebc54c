const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/edu_plan');

module.exports = {
    getByParams,
    getById,
    getLessonItems,
    save,
    getChapterPapers,
    getChapterLessonResource,
    getChapterResource,
}

const JOI_GET = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    from_year: Joi.number().optional(), // 学年
    to_year: Joi.number().optional(), // 学年
    semester: Joi.string().optional(), // 学期
});

async function getByParams(req, res) {
    const params = await JOI_GET.validateAsync(req.query);
    params.user = req.user;
    return await model.getByParams(params);
}

const JOI_ID = Joi.object({
    id: Joi.string().required(),
});

async function getById(req, res) {
    const params = await JOI_ID.validateAsync(req.params);
    params.user = req.user;
    return await model.getById(params);
}

async function getLessonItems(req, res) {
    const params = await JOI_ID.validateAsync(req.query);
    return await model.getLessonItems(params, req.user);
}

const JOI_SAVE = Joi.object({
    id: Joi.string().optional(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    from_year: Joi.number().optional(), // 学年
    to_year: Joi.number().required(), // 学年
    semester: Joi.string().required(), // 学期
    stime: Joi.string().required(),
    etime: Joi.string().required(),
    start_week: Joi.number().required(),
    weeks: Joi.array().items(Joi.object({ // 周计划
        index: Joi.number().required(), //
        stime: Joi.string().required(), // 开始时间
        etime: Joi.string().required(), // 结束时间
        children: Joi.array().items(Joi.object({
            id: Joi.string().optional(),
            name: Joi.string().required(),
            key: Joi.string().required(),
            // source_id: Joi.string().optional().default('').allow(''),
            book_id: Joi.number().optional().default(0),
            chapter_id: Joi.number().optional().default(0),
            children: Joi.array().items(Joi.object()).optional()
        })).optional()
    })).min(1).required(),
});

async function save(req, res) {
    const params = await JOI_SAVE.validateAsync(req.body);
    return await model.save(params, req.user);
}


const JOI_GET_CHAPTER_PAPERS = Joi.object({
    id: Joi.string().required(),
    team_prep_id: Joi.string().optional(),
    chapter_id: Joi.string().required(),
    lesson_id: Joi.string().optional(),
});

async function getChapterPapers(req, res) {
    const params = await JOI_GET_CHAPTER_PAPERS.validateAsync(req.query);
    return await model.getChapterPapers(params, req.user);
}

const JOI_GET_CHAPTER_RESOURCE = Joi.object({
    id: Joi.string().required(),
    chapter_id: Joi.string().required(),
    key: Joi.string().required(),
});

async function getChapterResource(req, res) {
    const params = await JOI_GET_CHAPTER_RESOURCE.validateAsync(req.query);
    return await model.getChapterResource(params, req.user);
}

const JOI_GET_CHAPTER_LESSON_RESOURCE = Joi.object({
    id: Joi.string().required(),
    chapter_id: Joi.string().optional(),
    lesson_id: Joi.string().required(),
    key: Joi.string().required()
});

async function getChapterLessonResource(req, res) {
    const params = await JOI_GET_CHAPTER_LESSON_RESOURCE.validateAsync(req.query);
    return await model.getChapterLessonResource(params, req.user);
}


