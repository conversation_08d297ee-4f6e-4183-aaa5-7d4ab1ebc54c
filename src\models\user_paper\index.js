const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const paperUtils = require('../../../common/utils/paper_utils');
const utils = require('../../../common/utils/utils');
const logger = require('../../../common/lib/logger');
const questionModel = require('../question');
const zykModel = require('../zyk');

module.exports = {
    get_list,
    get_detail,
    post_paper,
    batch_post_paper,
    delete_paper,
    getDtkGateway,
    update_paper_status,
    getUserPaperQuestions,
    upload_paper,
    parse_callback,
    get_upload_list,
    delete_upload_by_id,
    get_detail_by_ai_task_id,
    get_paper_public_by_content,
}

async function get_list(params) {
    const {user_id, period, subject, grade, type, offset, limit, status, exam_status, source, year, sort_by} = params;
    const result = {
        total: 0,
        list: []
    };
    const cond = {
        user_id: user_id,
        // source: enums.PaperSourceType.ASSEMBLE,
        valid: enums.BOOL.YES
    }
    if (period) cond.period = period;
    if (subject) cond.subject = subject;
    if (grade) cond.grade = grade;
    if (type) cond.type = type;
    if (status) cond.status = status;
    if (exam_status) cond.exam_status = exam_status;
    if (source) {
        cond.source = source;
        if (source !== enums.PaperSourceType.UPLOAD) cond.status = enums.PaperStatus.DONE;
    }
    if (year) {
        if (year > 0) cond.to_year = year;
        else cond.to_year = {$lt: 2020};
    }
    const total = await db.collection(schema.user_paper).find(cond).count();
    if (!total) return result;
    result.total = total;
    let proj = { _id: 1, name: 1, download_time: 1, volumes: 1, period: 1, subject: 1, grade: 1, press_version: 1, share_sort: 1, expired_time: 1, invalid_time: 1, dtk_id: 1, last_sync_time: 1, type: 1, status: 1, ctime:1, utime: 1, source: 1, error: 1, from_year: 1, to_year: 1 };
    const list = await db.collection(schema.user_paper).find(cond).sort({ctime: -1}).skip(offset).limit(limit).project(proj).toArray();
    for (const item of list) {
        result.list.push({
            id: item._id.toString(),
            name: item.name,
            question_num: get_question_num(item),
            grade: item.grade || '',
            period: item.period,
            subject: item.subject,
            source: item.source,
            type: item.type,
            from_year: item.from_year,
            to_year: item.to_year,
            status: item.status,
            exam_status: item.exam_status,
            ctime: item.ctime.getTime(),
            utime: item.utime.getTime(),
            error: item.error || '',
            from_enum: enums.PaperFrom.GROUP
        });
    }
    return result;
}

function get_question_num(paper) {
    let question_num = 0;
    if (paper.volumes) {
        for (let a = 0; a < paper.volumes.length; a++) {
            let volume = paper.volumes[a];
            let blocks = volume.blocks;
            if (!blocks) {
                continue;
            }
            for (let b = 0; b < blocks.length; b++) {
                let block = blocks[b];
                if (!block) {
                    continue;
                }
                let questions = block.questions;
                if (!questions) {
                    continue;
                }
                for (let c = 0; c < questions.length; c++) {
                    question_num++;
                }
            }
        }
    }
    return question_num;
}

async function get_detail_by_ai_task_id(params) {
    const {id} = params;
    const cond = {
        ai_task_id: id,
    }
    const task = await db.jzl_collection(schema.parse_task).findOne(cond);
    if (_.isEmpty(task)) throw new BussError('任务不存在');
    if (!task.tiku_paper_id) throw new BussError('试卷不存在');
    return await get_detail({id: task.tiku_paper_id});
}

async function get_detail(params) {
    const {user_id, id} = params;
    const cond = {
        _id: new ObjectId(id),
        // user_id: user_id, // 去掉用户、试卷改变使用
        valid: enums.BOOL.YES
    }
    const paper = await db.collection(schema.user_paper).findOne(cond);
    if (_.isEmpty(paper)) return {};
    if (paper.status === enums.PaperStatus.DONE) {
        // paperUtils.
        await extend_ques(paper);
        // 处理试题题号和去除题内题号
        paperUtils.ques_process(paper);
    }
    // 字段转换
    paper.id = paper._id.toString();
    paper.ctime = paper.ctime.getTime();
    paper.utime = paper.utime.getTime();
    // 删除_id
    delete paper._id;
    delete paper.valid;
    return paper;
}


async function extend_ques(paper) {
    const kb_ids = [];
    // let up_ids = [];
    const zx_ids = [];
    const zyk_ids = [];
    const ques_map = {};
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            for (const ques of block.questions) {
                if (ques.source === enums.QuestionSource.UPLOAD) {
                    // up_ids.push(ques.id);
                    ques_map[ques.id] = ques;
                } else if (ques.source === enums.QuestionSource.ZX) {
                    zx_ids.push(ques.id);
                } else if (ques.source === enums.QuestionSource.ZYK) {
                    zyk_ids.push(ques.id);
                } else {
                    kb_ids.push(ques.id);
                }
            }
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestionByIds(kb_ids);
        questions.forEach(e => ques_map[e['id']] = e);
    }
    if (_.size(zx_ids)) {
        const questions = await questionModel.getByIds(zx_ids);
        questions.forEach(e => ques_map[e['id']] = e);
    }
    if (_.size(zyk_ids)) {
        const questions = await zykModel.getQuestionByIds(zyk_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    paperUtils.traverse_questions(paper, ques_map);
}

async function post_paper(params) {
    const { user_id, id } = params;
    let paper = {};
    if (id) {
        const cond = {
            _id: new ObjectId(id),
            user_id: user_id,
            valid: enums.BOOL.YES
        }
        paper = await db.collection(schema.user_paper).findOne(cond);
        if (_.isEmpty(paper)) throw new BussError('试卷不存在');
        // 完成后也可以编辑
        // if (paper.source === enums.PaperSourceType.UPLOAD && paper.status === enums.PaperStatus.DONE) throw new BussError('不可编辑');
    }

    const now = new Date();
    if (id) {
        delete params.id;
        delete params._id;
        delete params.ctime;
        // delete params.from;
        params.utime = now;
        if (params.status === enums.PaperStatus.DONE) { // 根据状态处理试题
            await questionModel.syncPaperQuestions(user_id, params);
            // 同步任务状态
            await db.jzl_collection(schema.parse_task).updateOne(
                {user_id: Number(user_id).toString(), tiku_paper_id: id},
                {$set: {status: enums.PaperStatus.DONE}});
        }
        await db.collection(schema.user_paper).updateOne({_id: new ObjectId(id)}, {$set: params});
    } else {
        const {from_year, to_year} = utils.getAcademicYear();
        // if (!params.from) params.from = enums.PaperFrom.GROUP; // 默认用户组卷
        params.ctime = now;
        params.utime = now;
        params.valid = enums.BOOL.YES;
        params.source_type = enums.PaperSourceType.ASSEMBLE;
        params.source = enums.PaperSourceType.ASSEMBLE;
        params.source_id = enums.PaperSourceType.ASSEMBLE;
        params.status = enums.PaperStatus.DONE;
        params.exam_status = enums.ExamStatus.EDITABLE;
        if (!params.from_year) params.from_year = from_year;
        if (!params.to_year) params.to_year = to_year;
        const insert = await db.collection(schema.user_paper).insertOne(params);
        return {id: insert.insertedId.toString()};
    }
    return { id };
}

async function batch_post_paper(array) {
    let paperList = [];
    for (const params of array) {
        const paper = await post_paper(params);
        paperList.push({
            id: paper.id,
            name: params.name
        })
    }
    return paperList
}
async function update_paper_status(params) {
    const {user_id, id, status} = params;
    const cond = {
        _id: new ObjectId(id),
        user_id: user_id,
        valid: enums.BOOL.YES
    }
    const paper = await db.collection(schema.user_paper).findOne(cond);
    if (_.isEmpty(paper)) throw new BussError('试卷不存在');
    await db.collection(schema.user_paper).updateOne({_id: paper._id}, {$set: {utime: new Date(), exam_status: status}});
    return { id };
}

async function delete_paper(params) {
    const {user_id, id} = params;
    const cond = {
        _id: new ObjectId(id),
        user_id: user_id,
        valid: enums.BOOL.YES
    }
    const paper = await db.collection(schema.user_paper).findOne(cond);
    if (_.isEmpty(paper)) throw new BussError('试卷不存在');
    if (paper.exam_status === enums.ExamStatus.NOTEDITABLE) throw new BussError('试卷已关联作业，如需删除，请先去布置作业列表撤销当前作业');
    await db.collection(schema.user_paper).updateOne({_id: paper._id}, {$set: {utime: new Date(), valid: enums.BOOL.NO}});
    return { id };
}

async function getDtkGateway(params) {
    const { user, host } = params;
    const teacher = await client.yj.getUserInfo(user.id);
    return await client.dtk.getGateWay(teacher, host);
}

async function getUserPaperQuestions(user_paper_id) {
    const paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(user_paper_id)});
    if (_.isEmpty(paper)) return [];
    const ids = [];
    for (const v of paper.volumes){
        for (const b of v.blocks){
            ids.push(...(b['questions'].map(e => e.id)));
        }
    }
    const questions = await client.kb.getQuestionByIds(ids);
    const result = [];
    for (const id of ids) {
        result.push(questions.find(e => e.id === id));
    }
    return result;
}

async function upload_paper(params) {
    const {user_id} = params;
    // 保存试卷
    const now = new Date();
    params.ctime = now;
    params.utime = now;
    params.valid = enums.BOOL.YES;
    params.source = enums.PaperSourceType.UPLOAD;
    params.source_type = enums.PaperSourceType.UPLOAD;
    params.status = enums.PaperStatus.INIT;
    params.exam_status = enums.ExamStatus.EDITABLE;
    const insert = await db.collection(schema.user_paper).insertOne(params);
    params.id = insert.insertedId.toString();
    params._id = insert.insertedId;
    wordParsePaper(user_id, params);
    return { id: insert.insertedId.toString() };
}

async function parse_callback(params) {
    const {task_id, data, user_id} = params;
    const task = await get_task_by_id(task_id);
    // 处理解析后的试卷
    const basket = _.assign(paperUtils.init(), _.pick(task, ['name', 'period', 'subject', 'grade', 'type', 'from_year', 'to_year']));
    // if (basket.name.endsWith('.docx') || basket.name.endsWith('.doc')) { // 处理后缀名
    //     basket.name = basket.name.split('.').slice(0, -1).join('');
    // }
    const date = new Date();
    let index = Date.now();
    const quesMap = {};
    const fun = async (content) => {
        if (!_.isEmpty(content) && content.type === 'question') {
            const ques = {
                id: `upload_${index}`,
                source: enums.QuestionSource.UPLOAD,
                source_id: enums.QuestionSource.UPLOAD,
                period: task.period,
                subject: task.subject,
                grade: task.grade,
                ...(_.pick(content.content, ['blocks', 'description', 'type', 'comment']))
            }
            //
            ques.knowledges = [];
            const knowledges = _.get(ques, 'blocks.knowledges', []);
            const knowledgeMap = {};
            for (const stem of knowledges) {
                for (const k of stem) {
                    if (!_.isEmpty(knowledgeMap[k.id])) continue;
                    ques.knowledges.push(k);
                    knowledgeMap[k.id] = k;
                }
            }
            quesMap[ques.id] = ques;
            index++;
            paperUtils.insert_questions(basket, ques);
        }
        for (const c of content.children || []) {
            await fun(c);
        }
    }
    for (const cont of data.content) {
        await fun(cont);
    }
    const result = {
        status: enums.PaperStatus.EDIT,
        error: '',
        paper_id: ''
    }
    // 移除题目数量检查
    // const ques_num = paperUtils.get_question_num(basket);
    // if (!ques_num) {
    //     result.status = enums.PaperStatus.ERROR;
    //     result.error = '未获取到试题';
    //     return result;
    // }
    paperUtils.traverse_questions(basket, quesMap);
    paperUtils.render_basket(basket);
    basket.user_id = user_id;
    basket.ctime = date;
    basket.utime = date;
    basket.valid = enums.BOOL.YES;
    basket.source = enums.PaperSourceType.UPLOAD;
    basket.source_type = enums.PaperSourceType.UPLOAD;
    basket.source_id = task_id;
    basket.exam_status = enums.ExamStatus.EDITABLE;
    basket.status = enums.PaperStatus.EDIT;
    const insert = await db.collection(schema.user_paper).insertOne(basket);
    result.paper_id = insert.insertedId.toString();
    return result;
}

async function get_task_by_id(id) {
    return await db.jzl_collection(schema.parse_task).findOne({_id: new ObjectId(id)});
}

async function get_paper_public_by_content(params) {
    const { data } = params;

    // 直接使用 paperUtils.init() 初始化试卷结构，不依赖 task
    const basket = paperUtils.init();

    let index = Date.now();
    const quesMap = {};

    // 递归处理内容的函数，仿照 parse_callback 的逻辑
    const fun = async (content) => {
        if (!_.isEmpty(content) && content.type === 'question') {
            const ques = {
                id: `upload_${index}`,
                source: enums.QuestionSource.UPLOAD,
                source_id: enums.QuestionSource.UPLOAD,
                // 由于没有 task，这些字段使用默认值或从 content 中提取
                period: content.content?.period || '',
                subject: content.content?.subject || '',
                grade: content.content?.grade || '',
                ...(_.pick(content.content, ['blocks', 'description', 'type', 'comment']))
            }

            // 处理知识点信息
            ques.knowledges = [];
            const knowledges = _.get(ques, 'blocks.knowledges', []);
            const knowledgeMap = {};
            for (const stem of knowledges) {
                for (const k of stem) {
                    if (!_.isEmpty(knowledgeMap[k.id])) continue;
                    ques.knowledges.push(k);
                    knowledgeMap[k.id] = k;
                }
            }

            quesMap[ques.id] = ques;
            index++;
            paperUtils.insert_questions(basket, ques);
        }

        // 递归处理子内容
        for (const c of content.children || []) {
            await fun(c);
        }
    }

    // 处理所有内容
    for (const cont of data.content) {
        await fun(cont);
    }

    // 使用 paperUtils 处理试卷结构
    paperUtils.traverse_questions(basket, quesMap);
    paperUtils.render_basket(basket);

    // 直接返回 paper 结构，不插入数据库
    return basket;
}

async function get_upload_list(params) {
    const { period, subject, type, grade, year, limit, offset, status, user_id } = params;
    const result = {
        total: 0,
        list: []
    };
    const cond = {
        user_id: Number(user_id).toString(),
        // source: enums.PaperSourceType.ASSEMBLE,
        deleted: enums.BOOL.NO
    }
    if (period) cond.period = period;
    if (subject) cond.subject = subject;
    if (grade) cond.grade = grade;
    if (type) cond.type = type;
    if (status) cond.status = status;
    if (year) {
        if (year > 0) cond.to_year = year;
        else cond.to_year = {$lt: 2020};
    }
    const total = await db.jzl_collection(schema.parse_task).find(cond).count();
    if (!total) return result;
    result.total = total;
    const list = await db.jzl_collection(schema.parse_task).find(cond).sort({createdAt: -1}).skip(offset).limit(limit).toArray();
    const paper_ids = [];
    const files = [];
    for (const item of list) {
        if (item.tiku_paper_id) paper_ids.push(item.tiku_paper_id);
        files.push(...(item.disk_files || []));
        result.list.push({
            id: item._id.toString(),
            name: item.name,
            question_num: 0,
            grade: item.grade || '',
            period: item.period,
            subject: item.subject,
            // source: item.source,
            type: item.type,
            from_year: item.from_year,
            to_year: item.to_year,
            status: item.status,
            tiku_paper_id: item.tiku_paper_id,
            // exam_status: enums.ExamStatus.EDITABLE,
            ctime: item.createdAt.getTime(),
            utime: item.updatedAt.getTime(),
            error: item.error || '',
            from_enum: enums.PaperFrom.GROUP,
            task_type: item.task_type,
            files: item.disk_files || []
        });
    }
    if (_.size(paper_ids)) {
        const papers = await db.collection(schema.user_paper).find({_id: {$in: paper_ids.map(e => new ObjectId(e))}}).toArray();
        for (const data of result.list) {
            const paper = papers.find(e => data.tiku_paper_id === e._id.toString());
            if (paper) {
                data.question_num = get_question_num(paper);
                data.exam_status = paper.exam_status;
                data.source = paper.source;
            }
        }
    }
    if (_.size(files)) {
        const fileMap = {};
        const fileList = await db.jzl_collection('disk-file').find({_id: {$in: files.map(e => new ObjectId(e))}}).toArray();
        for (const file of fileList) {
            fileMap[file._id.toString()] = file;
        }
        for (const data of result.list) {
            data.files = data.files.map(e => {
                return  {
                    id: e,
                    url: fileMap[e].url,
                    name: fileMap[e].name,
                    size: fileMap[e].size,
                    type: fileMap[e].type,
                    hash: fileMap[e].hash,
                    period: fileMap[e].period,
                    subject: fileMap[e].subject,
                    category: fileMap[e].category,
                    suffix: fileMap[e].suffix,
                    source: fileMap[e].source,
                    createdAt: fileMap[e].createdAt.getTime(),
                    updatedAt: fileMap[e].updatedAt.getTime(),
                }
            });
        }
    }
    return result;
}


async function wordParsePaper(user_id, doc) {
    try {
        const result = await client.wordParser.wordParse(doc.period, doc.subject, doc.type, doc.name, doc.source_url);
        const {code, msg, data} = result;
        if (code !== 0) { // 解析失败
            await db.collection(schema.user_paper).updateOne({_id: doc._id}, {$set: {status: enums.PaperStatus.ERROR, error: msg} } );
            return;
        }
        // 处理解析后的试卷
        const basket = paperUtils.init();
        let index = Date.now();
        const quesMap = {};
        const fun = async (content) => {
            if (!_.isEmpty(content) && content.type === 'question') {
                const ques = {
                    id: `upload_${index}`,
                    source: enums.QuestionSource.UPLOAD,
                    source_id: enums.QuestionSource.UPLOAD,
                    period: doc.period,
                    subject: doc.subject,
                    grade: doc.grade,
                    ...(_.pick(content.content, ['blocks', 'description', 'type', 'comment']))
                }
                quesMap[ques.id] = ques;
                index++;
                paperUtils.insert_questions(basket, ques);
            }
            for (const c of content.children || []) {
                await fun(c);
            }
        }
        for (const cont of data.content) {
            await fun(cont);
        }
        const ques_num = paperUtils.get_question_num(basket);
        if (!ques_num) { //
            await db.collection(schema.user_paper).updateOne({_id: doc._id}, {$set: {status: enums.PaperStatus.ERROR, error: '未获取到试题'}} );
            return;
        }
        paperUtils.traverse_questions(basket, quesMap);
        paperUtils.render_basket(basket);
        const upDoc = _.pick(basket, [
            'subtitle', 'score', 'duration', 'paper_info', 'cand_info', 'attentions', 'secret_tag', 'score_info', 'gutter', 'partsList', 'template', 'volumes'
        ]);
        upDoc.status = enums.PaperStatus.EDIT;
        await db.collection(schema.user_paper).updateOne({_id: doc._id}, {$set: upDoc});
    } catch (e) {
        logger.error('试卷解析失败', e);
        await db.collection(schema.user_paper).updateOne({_id: doc._id}, {$set: {status: enums.PaperStatus.ERROR, error: e.message}});
    }
}

async function delete_upload_by_id(params, user) {
    const {id} = params;
    const task = await db.jzl_collection(schema.parse_task).findOne({_id: new ObjectId(id), user_id: Number(user.id).toString()});
    if (_.isEmpty(task)) throw new BussError('数据不存在');
    await db.jzl_collection(schema.parse_task).updateOne({_id: new ObjectId(id)}, {$set: {deleted: enums.BOOL.YES, updatedAt: new Date()}});
    if (task.tiku_paper_id) {
        await db.collection(schema.user_paper).updateOne({_id: new ObjectId(task.tiku_paper_id)}, {$set: {valid: enums.BOOL.NO, utime: new Date()}});
    }
    return {id};
}
