const client = require('../../../common/client');
const logger = require('../../../common/lib/logger');
const enums = require('../../../common/enums/enums');
const utils = require('../../../common/utils/utils');
const _ = require('lodash');


module.exports = {
    getBooks,
    getBookDetail,
    chapterRecommendPapers
}

async function getBooks(params) {
    logger.info('获取教材列表：', Date.now());
    return await client.kb.getBooks(params);
}

async function getBookDetail(params) {
    return await client.kb.getBookDetail(params);
}


async function chapterRecommendPapers(params, user) {
    const {book_id, chapter_id, period, subject, grade, term, type} = params;
    const result = {
        total: 0,
        list: []
    };
    const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === grade || e.grade === grade);
    if (!gradeInfo) throw new BussError('年级错误');
    const semesterInfo = utils.getSemesterAndYear();
    const algo_params = {
        // period: period,
        // subject: subject,
        type: type || '同步练习',
        grade: gradeInfo.grade,
        term: term || semesterInfo.semester,
        trees: [],
        ai_paper: 0,
        real_paper: 10,
        school_id: user.school_id
    };
    if (book_id) {
        const book = await client.kb.getBookDetail({book_id, fields_type: 'knowledge'});
        if (!_.isEmpty(book)) {
            algo_params.period = book.period;
            algo_params.subject = book.subject;
            algo_params.trees.push({
                type: 'book',
                id: book.book_id,
                name: book.grade,
                version: book.press_version,
                children: [findNodePath(book.book.children, chapter_id)]
            })
        }
    }
    if (period) algo_params.period = period;
    if (subject) algo_params.subject = subject;
    if (!algo_params.period || !algo_params.subject) throw new BussError('学段科目错误');
    const algo_papers = await client.algo.recommendSyncPaper(algo_params);
    if (_.isEmpty(algo_papers)) return result;
    const real_papers = algo_papers.real_papers || [];
    result.total = _.size(real_papers);
    if (!result.total) return result;
    result.list = await client.kb.getPaperByIds(real_papers.map(e => e.id));
    return result;
}


function findNodePath(tree, targetId) {
    for (const node of tree) {
        if (node.id === targetId) {
            // 找到目标节点，返回精简后的节点
            let chapters = [];
            if (node.children) chapters = utils.flattenTree(node.children);
            for (const c of chapters) {
                delete c.importance;
                delete c.chance;
                delete c.score;
                delete c.ques_num;
            }
            return {
                id: node.id,
                name: node.name,
                key: node.key,
                children: node.children
            };
        }

        if (node.children && node.children.length > 0) {
            const childResult = findNodePath(node.children, targetId);
            if (childResult) {
                // 如果子节点中找到目标，构建精简后的父节点
                return {
                    id: node.id,
                    name: node.name,
                    key: node.key,
                    children: [childResult]
                };
            }
        }
    }
    return null;
}
