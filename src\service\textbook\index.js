const Joi = require('@hapi/joi');
const textbookModel = require('../../models/textbook');
const logger = require('../../../common/lib/logger');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    createTextbook,
    getTextbookList,
    updateTextbook,
    deleteTextbook,
    getTextbookById
};

// Joi 校验规则
const JOI_CREATE_TEXTBOOK = Joi.object({
    name: Joi.string().required().max(200).label('教辅名称'),
    description: Joi.string().optional().allow('').label('教辅描述'),
    grade: Joi.string().required().label('年级'),
    period: Joi.string().required().valid('小学', '初中', '高中').label('学段'),
    subject: Joi.string().required().valid(
        '语文', '数学', '英语', '物理', '化学', '生物', 
        '地理', '历史', '政治', '道德与法治', '科学', 
        '信息技术', '音乐', '美术', '体育'
    ).label('科目'),
    cover_url: Joi.string().required().allow('').label('封面URL'),
    cover_file: Joi.string().optional().allow('').label('封面文件ID'),
    remark: Joi.string().optional().allow('').label('备注'),
    disk_file: Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().optional(),
        size: Joi.number().optional(),
        type: Joi.string().optional().default(''),
        path: Joi.string().optional().default('').allow(''),
        url: Joi.string().optional().default(''),
        hash: Joi.string().optional().default(''),
        parent_id: Joi.string().optional().default('0'),
        is_folder: Joi.number().valid(0, 1).optional(),
    }).required().label('网盘文件信息')
});

const JOI_GET_TEXTBOOK_LIST = Joi.object({
    offset: Joi.number().integer().min(0).optional().default(0).label('偏移量'),
    limit: Joi.number().integer().min(1).max(100).optional().default(20).label('每页数量'),
    period: Joi.string().optional().valid('小学', '初中', '高中').label('学段'),
    grade: Joi.string().optional().label('年级'),
    subject: Joi.string().optional().valid(
        '语文', '数学', '英语', '物理', '化学', '生物', 
        '地理', '历史', '政治', '道德与法治', '科学', 
        '信息技术', '音乐', '美术', '体育'
    ).label('科目'),
    keyword: Joi.string().optional().allow('').label('关键词'),
    my_only: Joi.boolean().optional().default(false).label('只显示我创建的教辅')
});

const JOI_UPDATE_TEXTBOOK = Joi.object({
    name: Joi.string().optional().max(200).label('教辅名称'),
    description: Joi.string().optional().allow('').label('教辅描述'),
    grade: Joi.string().optional().label('年级'),
    period: Joi.string().optional().valid('小学', '初中', '高中').label('学段'),
    subject: Joi.string().optional().valid(
        '语文', '数学', '英语', '物理', '化学', '生物', 
        '地理', '历史', '政治', '道德与法治', '科学', 
        '信息技术', '音乐', '美术', '体育'
    ).label('科目'),
    cover_url: Joi.string().optional().allow('').label('封面URL'),
    cover_file: Joi.string().optional().allow('').label('封面文件ID'),
    disk_file: Joi.string().optional().allow('').label('网盘文件ID'),
    remark: Joi.string().optional().allow('').label('备注')
});

const JOI_TEXTBOOK_ID = Joi.object({
    id: Joi.string().required().label('教辅ID')
});

/**
 * @swagger
 * /v1/textbook:
 *   post:
 *     summary: 创建教辅
 *     description: 创建新的校本教辅资源
 *     tags:
 *       - 教辅管理
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - grade
 *               - period
 *               - subject
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 200
 *                 description: 教辅名称
 *                 example: "数学练习册"
 *               description:
 *                 type: string
 *                 description: 教辅描述
 *                 example: "七年级上册数学练习册"
 *               grade:
 *                 type: string
 *                 description: 年级
 *                 example: "七年级"
 *               period:
 *                 type: string
 *                 enum: [小学, 初中, 高中]
 *                 description: 学段
 *                 example: "初中"
 *               subject:
 *                 type: string
 *                 enum: [语文, 数学, 英语, 物理, 化学, 生物, 地理, 历史, 政治, 道德与法治, 科学, 信息技术, 音乐, 美术, 体育]
 *                 description: 科目
 *                 example: "数学"
 *               cover_url:
 *                 type: string
 *                 description: 封面URL
 *                 example: "https://example.com/cover.jpg"
 *               cover_file:
 *                 type: string
 *                 description: 封面文件ID
 *                 example: "cover_file_id"
 *               disk_file:
 *                 type: string
 *                 description: 网盘文件ID
 *                 example: "disk_file_id"
 *               remark:
 *                 type: string
 *                 description: 备注
 *                 example: "适用于课后练习"
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/Textbook'
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 认证失败
 *       500:
 *         description: 服务器错误
 */
async function createTextbook(req, res) {
    try {
        const params = await JOI_CREATE_TEXTBOOK.validateAsync(req.body);
        
        const textbookData = {
            ...params,
            user_id: req.user.user_id,
            school_id: req.user.school_id,
            deleted: 0
        };
        textbookData.unify_sid = req.cookies['unify_sid'];
        const result = await textbookModel.createTextbook(textbookData);
        logger.info('创建教辅成功:', result._id);
        return result;
    } catch (error) {
        logger.error('创建教辅失败:', error);
        throw error;
    }
}

/**
 * @swagger
 * /v1/textbook:
 *   get:
 *     summary: 获取教辅列表
 *     description: 分页获取校本教辅资源列表
 *     tags:
 *       - 教辅管理
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: offset
 *         in: query
 *         description: 偏移量
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *       - name: limit
 *         in: query
 *         description: 每页数量
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - name: period
 *         in: query
 *         description: 学段筛选
 *         required: false
 *         schema:
 *           type: string
 *           enum: [小学, 初中, 高中]
 *       - name: grade
 *         in: query
 *         description: 年级筛选
 *         required: false
 *         schema:
 *           type: string
 *       - name: subject
 *         in: query
 *         description: 科目筛选
 *         required: false
 *         schema:
 *           type: string
 *           enum: [语文, 数学, 英语, 物理, 化学, 生物, 地理, 历史, 政治, 道德与法治, 科学, 信息技术, 音乐, 美术, 体育]
 *       - name: keyword
 *         in: query
 *         description: 关键词搜索
 *         required: false
 *         schema:
 *           type: string
 *       - name: my_only
 *         in: query
 *         description: 只显示我创建的教辅
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Textbook'
 *                     total:
 *                       type: integer
 *                       example: 100
 *                     offset:
 *                       type: integer
 *                       example: 0
 *                     limit:
 *                       type: integer
 *                       example: 20
 *       401:
 *         description: 认证失败
 *       500:
 *         description: 服务器错误
 */
async function getTextbookList(req, res) {
    try {
        const params = await JOI_GET_TEXTBOOK_LIST.validateAsync(req.query);
        const queryParams = {
            ...params,
            school_id: req.user.school_id
        };
        
        // 如果需要只显示当前用户创建的教辅
        if (params.my_only) {
            queryParams.user_id = req.user.user_id;
        }
        
        // 从查询参数中移除 my_only，避免传递给模型层
        delete queryParams.my_only;
        
        const result = await textbookModel.getTextbookList(queryParams);
        return result;
    } catch (error) {
        logger.error('获取教辅列表失败:', error);
        throw error;
    }
}

/**
 * @swagger
 * /v1/textbook/{id}:
 *   get:
 *     summary: 获取教辅详情
 *     description: 获取指定教辅的详细信息，会自动增加浏览次数
 *     tags:
 *       - 教辅管理
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 教辅ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/Textbook'
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 认证失败
 *       403:
 *         description: 无权限访问
 *       404:
 *         description: 教辅不存在
 *       500:
 *         description: 服务器错误
 */
async function getTextbookById(req, res) {
    try {
        const params = await JOI_TEXTBOOK_ID.validateAsync(req.params);
        const textbook = await textbookModel.getTextbookById(params.id);
        
        if (!textbook) {
            throw new BussError('教辅不存在');
        }
        
        if (textbook.school_id !== req.user.school_id) {
            throw new BussError('无权限查看此教辅');
        }
        
        // 增加浏览次数
        await textbookModel.updateTextbook(params.id, {
            view_times: textbook.view_times + 1
        });
        
        textbook.view_times += 1;
        return textbook;
    } catch (error) {
        logger.error('获取教辅详情失败:', error);
        throw error;
    }
}

/**
 * @swagger
 * /v1/textbook/{id}:
 *   put:
 *     summary: 更新教辅
 *     description: 更新指定教辅的信息
 *     tags:
 *       - 教辅管理
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 教辅ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 200
 *                 description: 教辅名称
 *               description:
 *                 type: string
 *                 description: 教辅描述
 *               grade:
 *                 type: string
 *                 description: 年级
 *               period:
 *                 type: string
 *                 enum: [小学, 初中, 高中]
 *                 description: 学段
 *               subject:
 *                 type: string
 *                 enum: [语文, 数学, 英语, 物理, 化学, 生物, 地理, 历史, 政治, 道德与法治, 科学, 信息技术, 音乐, 美术, 体育]
 *                 description: 科目
 *               cover_url:
 *                 type: string
 *                 description: 封面URL
 *               cover_file:
 *                 type: string
 *                 description: 封面文件ID
 *               disk_file:
 *                 type: string
 *                 description: 网盘文件ID
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/Textbook'
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 认证失败
 *       404:
 *         description: 教辅不存在
 *       500:
 *         description: 服务器错误
 */
async function updateTextbook(req, res) {
    try {
        const { id } = await JOI_TEXTBOOK_ID.validateAsync(req.params);
        const updateData = await JOI_UPDATE_TEXTBOOK.validateAsync(req.body);

        const result = await textbookModel.updateTextbook(id, updateData);
        logger.info('更新教辅成功:', id);
        return result;
    } catch (error) {
        logger.error('更新教辅失败:', error);
        throw error;
    }
}

/**
 * @swagger
 * /v1/textbook/{id}:
 *   delete:
 *     summary: 删除教辅
 *     description: 删除指定教辅（软删除）
 *     tags:
 *       - 教辅管理
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 教辅ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 0
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 认证失败
 *       404:
 *         description: 教辅不存在
 *       500:
 *         description: 服务器错误
 */
async function deleteTextbook(req, res) {
    try {
        const { id } = await JOI_TEXTBOOK_ID.validateAsync(req.params);
        const result = await textbookModel.deleteTextbook(id);
        logger.info('删除教辅成功:', id);
        return result;
    } catch (error) {
        logger.error('删除教辅失败:', error);
        throw error;
    }
}

/**
 * @swagger
 * components:
 *   schemas:
 *     Textbook:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 教辅ID
 *           example: "64a1b2c3d4e5f6789012345"
 *         name:
 *           type: string
 *           description: 教辅名称
 *           example: "数学练习册"
 *         description:
 *           type: string
 *           description: 教辅描述
 *           example: "七年级上册数学练习册"
 *         grade:
 *           type: string
 *           description: 年级
 *           example: "七年级"
 *         period:
 *           type: string
 *           enum: [小学, 初中, 高中]
 *           description: 学段
 *           example: "初中"
 *         subject:
 *           type: string
 *           enum: [语文, 数学, 英语, 物理, 化学, 生物, 地理, 历史, 政治, 道德与法治, 科学, 信息技术, 音乐, 美术, 体育]
 *           description: 科目
 *           example: "数学"
 *         cover_url:
 *           type: string
 *           description: 封面URL
 *           example: "https://example.com/cover.jpg"
 *         cover_file:
 *           type: string
 *           description: 封面文件ID
 *           example: "cover_file_id"
 *         disk_file:
 *           type: string
 *           description: 网盘文件ID
 *           example: "disk_file_id"
 *         view_times:
 *           type: integer
 *           description: 浏览次数
 *           example: 15
 *         download_times:
 *           type: integer
 *           description: 下载次数
 *           example: 8
 *         remark:
 *           type: string
 *           description: 备注
 *           example: "适用于课后练习"
 *         user_id:
 *           type: string
 *           description: 创建用户ID
 *           example: "teacher123"
 *         school_id:
 *           type: integer
 *           description: 学校ID
 *           example: 1001
 *         deleted:
 *           type: integer
 *           enum: [0, 1]
 *           description: 删除标识
 *           example: 0
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *           example: "2024-01-01T00:00:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *           example: "2024-01-01T00:00:00.000Z"
 */