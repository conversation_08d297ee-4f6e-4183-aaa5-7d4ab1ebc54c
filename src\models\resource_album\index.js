const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const path = require('path');
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const client = require('../../../common/client');

module.exports = {
    getAlbumList,
    getAlbumDetail,
    getAlbumDataDetail,
    download,
}

async function getAlbumList(params, user) {
    const result = {
        total: 0,
        list: []
    };
    const data = await client.kb.getAlbumList(params);
    if (!_.isEmpty(data)) {
        result.total = data.total;
        if (!result.total) return result;
        for (const item of data.list) {
            result.list.push(item);
        }
    }
    return result;
}

async function getAlbumDetail(params, user) {
    const {id} = params;
    const result = await client.kb.getAlbum(id);
    if (!_.isEmpty(result)) {
        result.id = result._id;
        delete result._id;
        await client.kb.updateAlbumTimes({id, action: 'view_times'});
    }
    return result;
}

async function getAlbumDataDetail(params, user) {
    const {id, data_id} = params;
    const album = await client.kb.getAlbum(id);
    if (_.isEmpty(album)) throw new BussError('专辑不存在');
    // 校验节点是否可以查询数据
    const chapter_ids = getQueryChapter(album, data_id);
    if (!_.size(chapter_ids)) throw new BussError('专辑节点不可查询');
    const list = await client.kb.getAlbumDataByIds(chapter_ids);
    const result = [];
    if (_.size(list)) { // 更新查看次数
        for (const chapter_id of chapter_ids) {
            const data = list.find(e => e.id === chapter_id);
            if (!data) continue;
            // data.ctime = data.ctime.getTime();
            // data.utime = data.utime.getTime();
            delete data.valid;
            // 系统资源统一加载
            await buildSystemResource(data);
            data.view_times = (data.view_times || 0) + 1;
            result.push(data);
        }
    }
    return result;
}

async function download(params, user) {
    const {id, data_id} = params;
    await client.kb.updateAlbumTimes({id, data_id, action: 'download_times'});
    return '';
}


function getQueryChapter(album, data_id) {
    const result = [];
    const getChild = (list, data_id, child = false) => {
        if (!_.size(list)) return;
        for (const chapter of list) {
            if (chapter.id === data_id || child) {
                if (chapter.type === 'album') result.push(chapter.id);
                if (chapter.is_query) getChild(chapter.children, data_id, true);
            } else {
                getChild(chapter.children, data_id);
            }
        }
    }
    getChild(album.children, data_id, false);
    return result;
}

async function buildSystemResource(data) {
    const id_map = {
        knowledge_ids: [],
        question_ids: [],
        exampaper_ids: [],
        edu_file_ids: [],
        edu_tool_ids: [],
        text_question_ids: []
    };

    getIds = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.knowledge_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.question_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.exampaper_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_file_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_tool_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.text_question_ids.push(content.id);
                }
            }
            getIds(chapter.children);
        }
    };
    getIds(data.children);
    // 获取系统资源
    const value_map = {};
    if (_.size(id_map.knowledge_ids)) {
        value_map.knowledges = await client.kb.getKnowledgeByIds(id_map.knowledge_ids);
    }
    if (_.size(id_map.question_ids)) {
        value_map.questions = await client.kb.getQuestionByIds(id_map.question_ids);
        const fileds = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
        for (const index in value_map.questions) {
            const q = value_map.questions[index];
            if (q.blocks) {
                q.blocks = {
                    stems: q.blocks.stems,
                    knowledges: q.blocks.knowledges,
                    types: q.blocks.types,
                }
            }
            value_map.questions[index] = _.pick(q, fileds);
        }
    }
    if (_.size(id_map.exampaper_ids)) {
        value_map.exampapers = await client.kb.getPaperByIds(id_map.exampaper_ids);
    }
    if (_.size(id_map.edu_file_ids)) {
        value_map.edu_files = await client.kb.getEduFileByIds(id_map.edu_file_ids);
        for (const data of value_map.edu_files) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.edu_tool_ids)) {
        value_map.edu_tools = await client.kb.getEduToolByIds(id_map.edu_tool_ids);
        for (const data of value_map.edu_tools) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.text_question_ids)) {
        value_map.text_questions = await client.kb.getQuestionByIds(id_map.text_question_ids);
    }
    const handler = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.knowledges || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.questions || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.exampapers || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_files || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_tools || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.text_questions || []).find(e => e.id === content.id);
                }
            }
            handler(chapter.children);
        }
    }
    handler(data.children);
}
