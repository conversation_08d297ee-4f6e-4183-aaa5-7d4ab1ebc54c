const config = require('config');
const express = require('express');
const app = express();
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const cors = config.get('cors');
const gzip = require('compression');
const _ = require('lodash');
const proxyMiddleware = require('http-proxy-middleware');
const url = require('url');
const routers = require('../src/routers');
const logger = require('../common/lib/logger');
const markdownMiddleware = require('../common/middlewares/markdown');

app.use(gzip());
app.use(bodyParser.urlencoded({ extended: true, limit: '5mb' }));
app.use(bodyParser.json({ limit: '5mb' }));
app.use(bodyParser.raw({ limit: '5mb' }));
app.use(cookieParser());
app.use('/download/xlsx', express.static('data/xlsx'));

// 支持通过域名+路径访问/public下的markdown文件，删除/public前缀，直接使用下级路径
// 支持通过参数切换查看模式：?view=html (默认) 或 ?view=raw
app.use(markdownMiddleware);

app.all('*', function (req, res, next) {
    req.realIP = req.header('X-Real-IP') || req.ip;
    // req['localAddress'] = localAddress;
    next();
});

// 支持跨域访问控制CORS配置
app.all('*', function (req, res, next) {
    const origin = req.headers.origin;
    if (origin && cors.find(e => origin.includes(e))){
        res.header('Access-Control-Allow-Origin', origin);
    }
    res.header('Access-Control-Allow-Credentials', true);
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, user-id, user_id');
    res.header('Access-Control-Allow-Methods', 'PUT,POST,GET,DELETE');
    res.header('X-Powered-By', ' 3.2.1');
    res.header('Content-Type', 'application/json;charset=utf-8');
    next();
});

app.all('*', function (req, res, next) {
    // Log down
    req.reqSeq = Math.random() * 1000 | 0; // to identify log line
    const sensitiveProperties = ['password', 'oldPassword', 'newPassword'];
    const logBody = _.isObject(req.body) ? _.omit(req.body, sensitiveProperties) : req.body;
    const bodySize = logBody && JSON.stringify(logBody).length || 0;
    logger.info(`[${req.reqSeq}]${req.method} ${req.url} from ${req.realIP} with query ${JSON.stringify(req.query)} and body ${JSON.stringify(bodySize > 2000 ? null : logBody)}`);

    next();
});

app.use(proxyMiddleware('/utilbox_api', {
    target: url.format({
        protocol: config.get('util_server').protocol,
        hostname: config.get('util_server').hostname,
        port: config.get('util_server').port,
    }),
    changeOrigin: true,
    onProxyReq: (proxyReq, req, res) => {
        const query = req.url.includes('?') ? `&appid=${config.get('util_server.appid')}` : `?appid=${config.get('util_server.appid')}`;
        proxyReq.path += query;
    }
}));

// init router
routers.init(app);

module.exports = app;

