
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const prepUtils = require('../../../common/utils/prep_utils');
const mime = require('mime');
const axios = require('axios');
// 默认文件格式
const default_type = 'docx';

module.exports = {
    getList,
    getDetail,
    download,
    updateTimes,
}

async function getList(params) {
    // params.page_img_status = enums.BooleanNumber.YES;
    // params.preview_status = enums.BooleanNumber.YES;
    // 参数转换
    if (params.type_id) {
        params.category = enums.TypeIdCategoryMap[params.type_id];
        delete params.type_id;
    }
    if (params.chapter_id) {
        params.parent_chapter_id = params.chapter_id;
        delete params.chapter_id;
    }
    const kb_res = await client.kb.getEduFileList(params);
    const result = {
        total: _.get(kb_res, 'total_num', 0),
        list: []
    };
    if (!result.total) return result;
    result.list = kb_res.records.map(d => {
        return {
            id: d.id,
            name: d.name,
            host: d.host,
            url: d.url,
            // page_img_url: d.page_img_url,
            // page_num: d.page_num,
            download_times: d.download_times,
            view_times: d.view_times,
            ctime: d.ctime,
            period: d.period,                           // 学段
            subject: d.subject,                         // 学科
            press_version: d.press_version,             // 教材版本
            book_id: d.book_id,                         // 教材ID
            book_name: d.book_name,                     // 教材名称
            chapter_id: d.chapter_id,                   // 章节id
            type_id: enums.KbFileCategoryMap[d.category],
            // video_url: d.video_url,
            // file_preview_url: d.preview_url || '', // 预览地址
            file: getFileInfo(d)
        }

    });
    return result;
}

async function getDetail(params) {
    const {user_id, id, from} = params;
    let resource = null;
    if (from === enums.UserPrepResourceFrom.USER) {
        resource = await db.collection(schema.user_prep_resource).findOne({_id: new ObjectId(id)});
        if (resource.user_id !== user_id) resource = null;
        if (!_.isEmpty(resource)) {
            resource.id = resource._id.toString();
            resource.ctime = resource.ctime.getTime();
            resource.utime = resource.utime.getTime();
            delete resource.valid;
            delete resource._id;
        }
    } else if (from === enums.UserPrepResourceFrom.ZYK) {
        resource = await db.zyk_collection(schema.zyk.prep_resource).findOne({_id: new ObjectId(id)});
        if (!_.isNumber(resource)) {
            resource.id = resource._id.toString();
            resource.ctime = resource.ctime.getTime();
            resource.utime = resource.utime.getTime();
            delete resource.valid;
            delete resource._id;
            await addViewTimes(id);
        }
    } else {
        resource = await client.kb.getEduFileById(id);
        if (!_.isEmpty(resource)) {
            resource.type_id = enums.KbFileCategoryMap[resource.category];
            delete resource.user_id;
            delete resource.user_name;
            delete resource.from;
            delete resource.category;
            delete resource.preview_status;
            delete resource.preview_url;
            delete resource.page_num;
            delete resource.page_img_status;
            resource.file = getFileInfo(resource);
            await client.kb.updateEduFileTimes(id, enums.FileAction.view_times);
        }
    }
    if (_.isEmpty(resource)) throw new BussError('资源不存在');
    return resource;
}

function getFileInfo(resource) {
    return {
        host: resource.host,
        url: resource.url,
        mime: resource.suffix || resource.url.split('.')[1],
        size: resource.size || 0
    };
}

async function addViewTimes(id) {
    await db.zyk_collection(schema.zyk.prep_resource).updateOne({_id: new ObjectId(id)}, {$inc: {view_times: 1}});
}

async function addDownloadTimes(id, type_id) {
    if (type_id === enums.ResourceType.lesson_ware.type_id || type_id === enums.ResourceType.study_plan.type_id) {
        await db.zyk_collection(schema.zyk.prep_resource).updateOne({_id: new ObjectId(id)}, {$inc: {download_times: 1}});
    }
}

async function download(req, res, params) {
    const {user_id, id, type_id, from, content} = params;
    let resource = null;
    if (from === enums.UserPrepResourceFrom.SYS) { // 系统资源
        if (type_id === enums.ResourceType.homework.type_id
            || type_id === enums.ResourceType.sys_paper.type_id
        ) {
            resource = await client.kb.getPaperById(id);
        } else {
            resource = await client.kb.getEduFileById(id);
            if (!_.isEmpty(resource)) {
                resource.file = getFileInfo(resource);
                await client.kb.updateEduFileTimes(id, enums.FileAction.download_times);
            }
        }
    } else if (from === enums.UserPrepResourceFrom.ZYK) { // 校本
        resource = await db.zyk_collection(schema.zyk.prep_resource).findOne({_id: new ObjectId(id)});
        if (!_.isEmpty(resource)) await addDownloadTimes(id, resource.type_id);
    } else { // 用户资源
        if (type_id === enums.ResourceType.user_paper.type_id) {
            resource = await db.collection(schema.user_paper).findOne({_id: new ObjectId(id)});
        } else {
            resource = await db.collection(schema.user_prep_resource).findOne({_id: new ObjectId(id), user_id});
        }
    }
    if (!resource) throw new BussError('资源不存在');
    let response = {};
    let fileMime = mime.getType(default_type);
    let fileName = resource.name;
    if (content) {
        const download_params = {
            html: content,
            filename: resource.name,
        }
        response = await client.utilbox.getDownloadInfo(download_params);
    } else {
        let file_url = '';
        if (resource.hasOwnProperty('file')) {
            file_url = `${resource.file.host}${resource.file.url}`;
        } else {
            file_url = `${resource.host}${resource.url}`;
        }
        if (!file_url) throw new BussError('文件地址错误');
        response = await axios.get(file_url, { responseType: 'stream' });
        fileMime = mime.getType(resource.file.mime);
        if (!response || response.status !== 200 || !response.data) {
            throw new BussError('下载文件错误');
        }
    }
    if (_.isEmpty(response)) throw new BussError('下载文件错误');
    // 响应数据
    res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${fileName}.${mime.getExtension(fileMime)}`)}`);
    // res.setHeader('Content-type', fileMime);
    res.setHeader('Content-type', 'application/octet-stream');
    return response.data;
}

async function updateTimes(params) {
    const {id, action} = params;
    await client.kb.updateEduFileTimes(id, action);
}
