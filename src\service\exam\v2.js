const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/exam/v2');


module.exports = {
    getExamPaperClasses,
    getExamPaperQuestions,
    putExamPaperComment,
    putExamPaperQuestionSim,
    searchExamPaperQuestion,
    putExamPaperQuestionAnswerMark,
    putExamPaperStatus,
    getCommentList,
    getClassCommentDetail,
    getExamPaperCategoryPaper,
}


const JOI_GET_EXAM_PAPER_CLASSES = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperClasses(req, res) {
    const params = await JOI_GET_EXAM_PAPER_CLASSES.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getExamPaperClasses(params, req.user);
}

const JOI_GET_EXAM_PAPER_QUESTIONS = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required()
});

async function getExamPaperQuestions(req, res) {
    const params = await JOI_GET_EXAM_PAPER_QUESTIONS.validateAsync(_.assign(req.params, req.query));
    return await model.getExamPaperQuestions(params, req.user);
}


const JOI_PUT_EXAM_PAPER_COMMENT = Joi.array().items(Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    courseware_name: Joi.string().required(),
    questions: Joi.array().items(Joi.string()).optional(),
}));

async function putExamPaperComment(req, res) {
    const examParams = await JOI_GET_EXAM_PAPER_QUESTIONS.validateAsync(req.params);
    const params = await JOI_PUT_EXAM_PAPER_COMMENT.validateAsync(req.body);
    return await model.putExamPaperComment(examParams, params, req.user);
}

const JOI_PUT_EXAM_PAPER_QUESTION_SIM = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    key: Joi.string().required(),
    ids: Joi.array().items(Joi.number())
});

async function putExamPaperQuestionSim(req, res) {
    const params = await JOI_PUT_EXAM_PAPER_QUESTION_SIM.validateAsync(_.assign(req.params, req.body));
    return await model.putExamPaperQuestionSim(params, req.user);
}

const JOI_SEARCH_EXAM_PAPER_QUESTION = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    knowledges: Joi.array().items(Joi.object({
        id: Joi.number().required(),
        name: Joi.string().required(),
    })),
    question_id: Joi.number().optional()
});

async function searchExamPaperQuestion(req, res) {
    const params = await JOI_SEARCH_EXAM_PAPER_QUESTION.validateAsync(_.assign(req.params, req.body));
    return await model.searchExamPaperQuestion(params, req.user);
}

const JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_MARK = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    key: Joi.string().required(),
    type: Joi.string().required().valid('excellent', 'mediocre'),
    student_id: Joi.number().required()
});

async function putExamPaperQuestionAnswerMark(req, res) {
    const params = await JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_MARK.validateAsync(_.assign(req.params, req.body));
    return await model.putExamPaperQuestionAnswerMark(params, req.user);
}

const JOI_PUT_EXAM_PAPER_STATUS = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    status: Joi.string().required(),
});

async function putExamPaperStatus(req, res) {
    const params = await JOI_PUT_EXAM_PAPER_STATUS.validateAsync(req.params);
    return await model.putExamPaperStatus(params, req.user);
}

const JOI_GET_COMMENT_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
});

async function getCommentList(req, res) {
    const params = await JOI_GET_COMMENT_LIST.validateAsync(req.query);
    return await model.getCommentList(params, req.user);
}

const JOI_GET_CLASS_COMMENT_DETAIL = Joi.object({
    id: Joi.string().required(),
    class_id: Joi.string().required(),
    type: Joi.string().required().valid('draft', 'formal')
});

async function getClassCommentDetail(req, res) {
    const params = await JOI_GET_CLASS_COMMENT_DETAIL.validateAsync(req.params);
    return await model.getClassCommentDetail(params, req.user);
}

const JOI_GET_EXAM_PAPER_CATEGORY_PAPER = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    category: Joi.number().required(),
});

async function getExamPaperCategoryPaper(req, res) {
    const params = await JOI_GET_EXAM_PAPER_CATEGORY_PAPER.validateAsync(_.assign(req.params, req.query));
    return await model.getExamPaperCategoryPaper(params, req.user);
}

