const express = require('express')
const router = express.Router()
const { handler } = require('../../../common/middlewares/request')
const code = require('../../../common/enums/code')
const db = require('../../../common/db');
const response = require('../../../common/lib/response');

const paper_service = require('../../service/paper');
const basket_service = require('../../service/basket');
const question_service = require('../../service/question');



// 时期筛选项
// 验证access-key的中间件
async function verifyAccessKey (req, res, next) {
  const accessKey = req.headers['access-key']
  if (!accessKey) {
    return res.json(response.error(code.AUTH_ERROR, 'access-key is required'))
  }
  let matchKey = await db.jzl_collection('access-key').findOne({ key: accessKey })
  if (!matchKey) {
    // 不存在或禁用。
    return res.json(response.error(code.AUTH_ERROR, 'invalid access-key'))
  }

  if (!matchKey.status) {
    return res.json(response.error(code.AUTH_ERROR, 'access-key is forbidden'))
  }
  next()
}

// 为所有internal接口添加access-key验证
router.use(verifyAccessKey)

// 试卷相关
// 获取试卷详细
router.get('/paper/:id', handler(1, paper_service.getDetail));

// 试题篮相关
// 获取试题篮试题
router.get('/basket', handler(1, basket_service.get_basket_internal));

// 获取试题详细
router.get('/question/:id', handler(1, question_service.getQuestionDetail));

module.exports = router
