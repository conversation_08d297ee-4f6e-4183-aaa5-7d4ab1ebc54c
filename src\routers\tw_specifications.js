const express = require('express');
const router = express.Router();
const service = require('../service/table');
const { handler } = require('../../common/middlewares/request');

router.get('/hot', handler(1, service.getHotTable));
// 获取用户细目表列表
router.get('/list', handler(1, service.getUserTableList));
// 获取细目表详细
router.get('/:table_id/detail', handler(1, service.getDetail));
// 细目表详细
router.get('/:table_id/info', handler(1, service.getDetail));
// 知识点匹配
router.post('/knowledge', handler(1, service.postKnowledge));
// 细目表知识点匹配
// router.post('/:table_id/knowledge', handler(1, service.postKnowledge));
router.post('', handler(1, service.createTable));
// 修改细目表
router.put('/:table_id', handler(1, service.updateTable));
// 删除细目表
router.delete('/:table_id', handler(1, service.deleteTable));
// 细目表组卷
router.post('/exampapers', handler(1, service.createPaper));
// 下载细目表
router.post('/xlsx', handler(1, service.downloadTable));
// 查看试卷的细目表
router.get('/exampapers/:id', handler(1, service.getTableByPaper));


module.exports = router;
