/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const SERVER = config.get('tiku_server');
const qs = require('querystring');

module.exports = {
    getPaperById,
};

/**
 * 考后巩固组卷
 * @returns {Promise<*>}
 * @param paperId
 */
async function getPaperById(user_paper_id) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/yj_api/v1/assemble_exampapers/${user_paper_id}`,
        port: SERVER.port
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.get(url, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`题库获取试卷信息失败,url:[${url}]`);
        return null;
    }
    return result.data && result.data.data;
}
