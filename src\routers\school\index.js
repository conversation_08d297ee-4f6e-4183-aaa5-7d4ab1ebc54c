const express = require('express');
const router = express.Router();
const service = require('../../service/school');
const service_zyk = require('../../service/school/school_zyk');
const { handler } = require('../../../common/middlewares/request');

// 获取年级组
router.get('/grade/groups', handler(1, service.getGradeGroup));
// 获取学校教师
router.get('/teachers', handler(1, service.getTeachers));

// 教辅列表
router.get('/zyk/reference_book/list', handler(1, service_zyk.getReferenceBooks));
// 试卷列表
router.get('/zyk/exampaper/list', handler(1, service_zyk.getExampaperList));
// 校本资源统计
router.get('/resource/statistics', handler(1, service.getResourceStatistics));

module.exports = router;
