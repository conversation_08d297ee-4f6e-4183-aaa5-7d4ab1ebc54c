const error_code = require('../enums/code');

/**
 * 自定义业务异常
 * @param message
 * @constructor
 */
function BussError(message) {
    this.name = 'BussError'; // 设置自定义的name属性
    this.message = message || '业务异常'; // 设置默认的message属性
    this.code = error_code.BUSS_ERR;
    // Error.captureStackTrace是Node.js中的一个方法，用于捕获堆栈跟踪
    // 如果在浏览器环境中，你可能需要使用其他方法来捕获堆栈信息
    if (Error.captureStackTrace) {
        Error.captureStackTrace(this, BussError);
    } else {
        this.stack = (new Error()).stack; // 尝试获取堆栈信息
    }
}

// 继承自Error
// BussError.prototype = new Error();
BussError.prototype = Object.create(Error.prototype);
BussError.prototype.constructor = BussError;

module.exports = BussError;
