'use strict';
const express = require('express');
const router = express.Router();
const service = require('../service/basket');
const { handler } = require('../../common/middlewares/request');

// 获取试题篮试题
router.get('/questions', handler(1, service.get_questions));
// 向试题篮中提交试题
router.post('/questions', handler(1, service.post_questions));
// 从试题篮中删除试题
router.delete('/questions', handler(1, service.delete_questions));

// 取试题篮
router.get('', handler(1, service.get_basket));
// 整体更新试题篮
// router.put('', handler(1, service.get_questions));
// 删除试题篮
router.delete('', handler(1, service.delete_basket));

module.exports = router;
