---
title: 学科网公开接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 学科网公开接口

学科网公开接口提供与学科网平台的OAuth2授权集成功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET OAuth2授权回调

GET /v1/xkw/auth/callback

处理学科网OAuth2授权回调，完成用户授权流程。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|code|query|string|是|学科网OAuth2授权码|
|state|query|string|是|状态参数，用于关联用户|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xkw/auth/callback?code=auth_code_123&state=user_state_456"
```

### 返回结果

此接口不返回JSON数据，而是执行重定向操作。

#### 成功重定向

```
HTTP/1.1 302 Found
Location: https://xkw.service.url/success
```

#### 授权拒绝重定向

```
HTTP/1.1 302 Found  
Location: https://xkw.service.url/denied
```

### 错误示例

#### 用户不存在

**错误码**: 1000  
**错误信息**: "用户不存在"

```json
{
  "code": 1000,
  "msg": "用户不存在",
  "data": null
}
```

#### 非法请求

**错误码**: 1000  
**错误信息**: "非法请求"

```json
{
  "code": 1000,
  "msg": "非法请求", 
  "data": null
}
```

### 业务逻辑说明

1. **验证参数**: 检查code和state参数的有效性
2. **用户验证**: 根据state参数查找对应的用户记录
3. **获取令牌**: 使用授权码向学科网交换访问令牌
4. **获取用户信息**: 使用访问令牌获取用户在学科网的资料
5. **更新关系**: 更新用户与学科网的绑定关系
6. **重定向**: 根据授权结果重定向到相应页面

### 注意事项

- 此接口通常由学科网平台自动调用，不需要前端直接调用
- state参数必须与之前发起授权时的参数一致
- 授权成功后会自动建立用户与学科网的关联关系
- 如果用户拒绝授权，系统会记录拒绝原因但不报错