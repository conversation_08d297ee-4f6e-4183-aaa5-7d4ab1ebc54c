---
title: 地区管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 地区管理接口

地区管理接口提供地区信息查询功能，为系统提供地区选择的基础数据。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取地区简单列表

GET /v1/region/simple

获取简化的地区列表数据，用于地区选择器等组件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/region/simple" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "110000",
      "name": "北京市",
      "level": 1,
      "parent_id": "0",
      "children": [
        {
          "id": "110100",
          "name": "北京市",
          "level": 2,
          "parent_id": "110000",
          "children": [
            {
              "id": "110101",
              "name": "东城区",
              "level": 3,
              "parent_id": "110100"
            },
            {
              "id": "110102",
              "name": "西城区",
              "level": 3,
              "parent_id": "110100"
            }
          ]
        }
      ]
    },
    {
      "id": "120000",
      "name": "天津市",
      "level": 1,
      "parent_id": "0",
      "children": [
        {
          "id": "120100",
          "name": "天津市",
          "level": 2,
          "parent_id": "120000",
          "children": [
            {
              "id": "120101",
              "name": "和平区",
              "level": 3,
              "parent_id": "120100"
            },
            {
              "id": "120102",
              "name": "河东区",
              "level": 3,
              "parent_id": "120100"
            }
          ]
        }
      ]
    }
  ]
}
```

### 返回数据结构

#### 地区信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|地区代码|行政区划代码|
|name|string|true|none|地区名称|行政区划名称|
|level|number|true|none|行政级别|1-省级 2-地级 3-县级|
|parent_id|string|true|none|父级ID|上级行政区划代码|
|children|array|false|none|下级地区|子级行政区划列表|

#### 成功响应格式

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态码|0表示成功|
|» msg|string|true|none|响应消息|成功时为空字符串|
|» data|array|true|none|地区数据|地区列表数组|

### 错误示例

#### 服务异常

```json
{
  "code": 14,
  "msg": "获取地区数据失败",
  "data": ""
}
```

#### 认证失败

```json
{
  "code": 11,
  "msg": "用户未登录或登录已过期",
  "data": ""
}
```

---

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|11|认证失败|用户未登录或登录已过期|
|12|参数错误|请求参数格式错误|
|14|业务错误|服务处理异常|

## 业务逻辑说明

### 数据来源

地区数据来自知识库服务（KB），通过调用外部API获取：
- **知识库API**: `/kb_api/v2/regions/simple/`
- **认证方式**: 通过api_key进行服务间认证
- **数据格式**: 返回层级结构的地区树形数据

### 数据结构

#### 行政级别
- **一级**: 省、直辖市、自治区、特别行政区
- **二级**: 地级市、地区、自治州、盟
- **三级**: 县、县级市、市辖区

#### 树形结构
- 采用嵌套的树形结构组织地区数据
- 每个地区包含其所有下级行政区划
- 支持任意层级的地区查询和选择

### 使用场景

1. **地区选择器**: 用户注册时选择所在地区
2. **数据筛选**: 按地区筛选学校、教师、学生数据
3. **统计分析**: 地区维度的教育数据统计
4. **权限控制**: 基于地区的数据访问权限控制

### 性能特点

- **静态数据**: 地区数据相对稳定，变更频率低
- **缓存友好**: 适合在客户端或CDN层面进行缓存
- **一次获取**: 返回完整的地区树，减少网络请求次数

### 错误处理

#### 服务降级
- 当知识库服务不可用时，系统可能返回默认的地区数据
- 建议客户端实现本地缓存机制，提高用户体验

#### 数据一致性
- 地区数据由国家统计局标准维护
- 系统定期从权威数据源更新地区信息
- 确保地区代码与国家标准保持一致

### 集成说明

#### 依赖服务
- **知识库服务**: 提供地区数据的核心服务
- **认证服务**: 确保API调用的安全性

#### 响应时间
- 正常情况下响应时间 < 200ms
- 包含完整地区树的数据量约为数百KB
- 建议客户端实现适当的加载状态提示

## 注意事项

1. **数据更新**: 地区数据会根据国家行政区划调整进行更新
2. **编码标准**: 地区代码遵循GB/T 2260-2007标准
3. **缓存策略**: 建议客户端缓存地区数据，减少服务器压力
4. **容错处理**: 建议实现网络异常时的降级方案