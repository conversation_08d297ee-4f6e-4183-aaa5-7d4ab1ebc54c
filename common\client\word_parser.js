/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('word_parser_server');

module.exports = {
    wordParse,
};

const options  = {
    timeout: 10 * 60 * 1000
}

/**
 * word试卷解析
 * @param period 学段
 * @param subject 科目
 * @param type 试卷类型
 * @param name 名称
 * @param word_url 文件地址
 * @returns {Promise<any>}
 */
async function wordParse(period, subject, type, name, word_url) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/word/parse2',
        port: server.port,
    });
    logger.info('试卷解析开始：', name);
    const result = await axios.post(url, {
        period,
        subject,
        type,
        name,
        word_url,
        knowledge_predict: true,
        inner_html: false
    }, options);
    logger.info('试卷解析完成：', name);
    if (result.data.code !== 0) {
        logger.error(`试卷解析失败: ${JSON.stringify(result.data)}`);
    }
    return result.data;
}
