---
title: 学校管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 学校管理接口

学校管理接口提供年级组管理、教师信息查询和资源库管理功能，支持学校级别的数据管理和查询。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取年级组列表

GET /v1/school/grade/groups

获取学校的年级组信息，包括年级划分和组织结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/school/grade/groups" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "gg_001",
      "name": "七年级组",
      "grades": ["七年级"],
      "period": "初中",
      "head_teacher": {
        "id": "teacher_001",
        "name": "张组长"
      },
      "teachers": [
        {
          "id": "teacher_002",
          "name": "李老师",
          "subject": "数学"
        }
      ]
    },
    {
      "id": "gg_002",
      "name": "八年级组",
      "grades": ["八年级"],
      "period": "初中",
      "head_teacher": {
        "id": "teacher_003",
        "name": "王组长"
      },
      "teachers": [
        {
          "id": "teacher_004",
          "name": "赵老师",
          "subject": "语文"
        }
      ]
    }
  ]
}
```

---

## GET 获取学校教师列表

GET /v1/school/teachers

获取学校所有教师的基本信息，包括任教科目和年级。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/school/teachers" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "teacher_001",
      "name": "张老师",
      "phone": "138****1234",
      "title": [],
      "xueke": ["数学"],
      "nianji": ["七年级", "八年级"]
    },
    {
      "id": "teacher_002", 
      "name": "李老师",
      "phone": "139****5678",
      "title": [],
      "xueke": ["语文"],
      "nianji": ["七年级"]
    }
  ]
}
```

---

## GET 获取学校教辅列表

GET /v1/school/zyk/reference_book/list

获取学校资源库中的教辅书籍列表，支持按学段、科目和年级筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|school_id|query|number|是|学校ID|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|科目名称|
|grade|query|string|否|年级，可为空|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/school/zyk/reference_book/list?school_id=1001&period=初中&subject=数学&grade=七年级" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "ref_book_001",
      "book_name": "七年级数学同步练习册"
    },
    {
      "id": "ref_book_002",
      "book_name": "初中数学知识点归纳"
    },
    {
      "id": "ref_book_003",
      "book_name": "七年级数学测试卷"
    }
  ]
}
```

---

## GET 获取学校试卷列表

GET /v1/school/zyk/exampaper/list

获取学校资源库中的试卷列表，支持多维度筛选和分页查询。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|school_id|query|number|是|学校ID|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|科目名称|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|press_version|query|string|否|出版社版本|
|grade|query|string|否|年级|
|reference_book_id|query|string|否|教辅ID|
|type|query|string|否|试卷类型|
|sort_by|query|string|否|排序字段，默认ctime|
|sort_order|query|number|否|排序方向，默认-1（降序）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/school/zyk/exampaper/list?school_id=1001&period=初中&subject=数学&offset=0&limit=20&grade=七年级&sort_by=ctime&sort_order=-1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 156,
    "list": [
      {
        "id": "exam_paper_001",
        "name": "七年级数学期中测试卷",
        "origin_kb_id": "kb_001",
        "period": "初中",
        "subject": "数学",
        "type": "期中测试",
        "user_id": "teacher_001",
        "user_name": "张老师",
        "ctime": 1698825600000,
        "utime": 1698912000000,
        "view_times": 35,
        "download_times": 12,
        "from_enum": 4
      },
      {
        "id": "exam_paper_002",
        "name": "七年级数学单元测试卷（有理数）",
        "origin_kb_id": "kb_002",
        "period": "初中",
        "subject": "数学",
        "type": "单元测试",
        "user_id": "teacher_002",
        "user_name": "李老师",
        "ctime": 1698739200000,
        "utime": 1698825600000,
        "view_times": 28,
        "download_times": 8,
        "from_enum": 4
      }
    ]
  }
}
```

---

## 返回数据结构

### 年级组信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|年级组ID|唯一标识|
|name|string|true|none|年级组名称|如"七年级组"|
|grades|array|true|none|年级列表|包含的年级|
|period|string|true|none|学段|所属学段|
|head_teacher|object|false|none|年级组长|组长信息|
|teachers|array|false|none|教师列表|组内教师|

### 教师信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|教师ID|用户唯一标识|
|name|string|true|none|教师姓名|真实姓名|
|phone|string|true|none|手机号|脱敏显示|
|title|array|true|none|职称|教师职称列表|
|xueke|array|true|none|任教科目|科目名称数组|
|nianji|array|true|none|任教年级|年级名称数组|

### 教辅信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|教辅ID|唯一标识|
|book_name|string|true|none|教辅名称|书籍名称|

### 试卷信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|试卷ID|唯一标识|
|name|string|true|none|试卷名称|试卷标题|
|origin_kb_id|string|true|none|原始知识库ID|来源标识|
|period|string|true|none|学段|所属学段|
|subject|string|true|none|科目|试卷科目|
|type|string|true|none|试卷类型|如期中测试、单元测试|
|user_id|string|true|none|创建者ID|上传教师ID|
|user_name|string|true|none|创建者姓名|上传教师姓名|
|ctime|number|true|none|创建时间|时间戳|
|utime|number|true|none|更新时间|时间戳|
|view_times|number|true|none|查看次数|浏览统计|
|download_times|number|true|none|下载次数|下载统计|
|from_enum|number|true|none|来源枚举|数据来源类型|

### 分页响应结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|number|true|none|总数量|符合条件的记录总数|
|list|array|true|none|数据列表|当前页数据|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|12|参数错误|请求参数格式错误或缺失必要参数|
|14|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 数据来源

- **年级组和教师数据**: 来自阅卷系统(YJ)的外部接口
- **教辅和试卷数据**: 存储在资源库MongoDB数据库(kb_zyk)中

### 筛选规则

#### 教辅列表筛选
- 必须匹配学校ID、学段和科目
- 年级参数可选，为空时返回该科目下所有年级的教辅

#### 试卷列表筛选
- 支持多维度筛选：学校、学段、科目、年级、教辅ID、试卷类型、出版社版本
- 支持按创建时间、更新时间等字段排序
- 支持分页查询，提高大数据量查询性能

### 数据处理

- **教师信息**: 对原始数据进行格式化，提取科目和年级信息
- **敏感信息**: 手机号进行脱敏处理
- **统计信息**: 试卷包含查看和下载次数统计

### 权限控制

- 所有接口需要用户认证
- 基于学校上下文限制数据访问范围
- 教师只能查看所属学校的数据

### 性能优化

- 试卷查询支持索引优化
- 分页查询避免大数据量返回
- 教辅列表返回简化信息提高响应速度