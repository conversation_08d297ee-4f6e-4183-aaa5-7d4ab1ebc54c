
const _ = require('lodash');
// const ObjectID = require("mongodb").ObjectID;
// const mongodber = require('../utils/mongodber');
const db = require('../../../common/db');
// const ResponseWrapper = require('../middlewares/response_wrapper');
const logger = require('../../../common/lib/logger');
// const enums = require('../../bin/enum');
// const Joi = require('@hapi/joi');
const client = require('../../../common/client');
// const constants = require('../utils/constants');
const template = require('./../../../common/enums/template');
const TYPES = template.TYPES;
// const DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;
// const subject_utils = require('../../../common/utils/subject_utils');
// const config = require('config');
// const Fontmin = require('fontmin');
// const moment = require('moment');
const paper_utils = require('../../../common/utils/paper_utils');
// const question_utils = require('../utils/question_utils');
// const utils = require('../utils/utils');
//
const schema = require('../../../common/enums/schema');

const collection_exam = db.collection(schema.user_exam);
// 用户组卷表
const collection_exampaper= db.collection(schema.user_paper);


module.exports = {
    buildExamPapers,
    build_level_paper_params,
    build_detail_paper_params,
}


async function buildExamPapers(exam) {
    let params = [1, 2, 3];
    const exam_papers = exam.papers || [];
    for (const ep of exam_papers) {
        params = params.filter(e => e !== ep.category);
    }
    if (!_.size(params)) return;
    try {
        const promises = params.map(category => buildExampaperByCategory(exam, category));
        let papers = await Promise.all(promises);
        if (!_.size(papers)) return;
        for (const p of papers) {
            exam.papers.push({
                id: p._id.toString(),
                type: '考后巩固',
                category: p.category,
                time: new Date()
            });
        }
        await collection_exam.updateOne({_id: exam._id}, {$set: {papers: exam.papers, utime: new Date()}});
        return papers;
    } catch (e) {
        logger.error(e);
    }
}

async function buildExampaperByCategory(exam, category) {
    const yp_data = await client.yuanpei.getPaperQuestions(exam.paper_id);
    const params = _build_level_paper_params(exam, category);
    const algo_paper = await client.algo.levelPaper(params);
    const paper = await paper_utils.build_algo_paper(exam.user_id, algo_paper);
    paper.type = '考后巩固';
    paper.period = exam.period;
    paper.grade = exam.grade;
    paper.subject = exam.subject;
    paper.category = category;
    paper_utils.set_display(paper, 0);
    paper.name = _get_exampaper_name(category, yp_data);
    const insert_result = await collection_exampaper.insertOne(paper);
    paper._id = insert_result.insertedId;
    return paper;
}

function build_level_paper_params(exam, category) {
    return _build_level_paper_params(exam, category);
}

function _build_level_paper_params(exam, category) {
    const difficulty_text = {
        1: '简单卷',
        2: '中等卷',
        3: '培优卷',
    }
    const set = new Set();
    for (const q of exam.questions || []) {
        (q.same || []).forEach(id => set.add(id));
    }
    const params = {
        period: exam.period,                        // 必填，学段
        grade: exam.grade,                          // 必填，学段
        subject: exam.subject,                      // 必填，科目
        paper_id: exam.paper_id.split('-')[0],      // 必填，试卷ID
        difficulty: difficulty_text[category],      // 必填，简单卷，中等卷，培优卷
        filtered_ques: [...set],               // 必填，过滤试题ID，默认为[]
        // school_id: +(exam.paper_id.split('-')[1]),  // 选填，学校ID
        // user_id: exam.user_id.toString(),                           // 选填，用户ID
        // class_name: str,                         // 选填，班级名
        // min_year: int,                              // 选填，最小试题年份限制
    }
    return params;
}

const CATEGORY_PAPER_NAME = {
    1: '简单练习卷',
    2: '中等练习卷',
    3: '培优练习卷',
    4: '薄弱点巩固卷',
    5: '共性练习卷',
}

function _get_exampaper_name(category, yp_paper) {
    return `${_.get(yp_paper, 'paper.name', '')} ${CATEGORY_PAPER_NAME[category]}`;
}

// function findYpQuestion(hfs_question, yp_paper) {
//     let result = null;
//     if (_.isEmpty(yp_paper)) return result;
//     for (const yp_question of yp_paper.questions || []) {
//         const point = (yp_question.points || []).find(e => e.id.split('-')[1] === hfs_question.questionId.toString());
//         if (!_.isEmpty(point)) {
//             result = yp_question;
//             break;
//         }
//     }
//     return result;
// }

//
// function getRecomendQuestionsParam(exam, hfs_question, yp_question) {
//     const params = {
//         period: exam.period,
//         subject: exam.subject,
//         grade: exam.grade,
//         ques_id: yp_question.id,
//         ques_from: 'yuanpei',
//         ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
//         recom_num: 2
//     };
//     return params;
// }

const category_difficulty = {
    1: '容易',
    2: '中等',
    3: '困难',
    4: '容易',
}


function build_detail_paper_params(exam, category, table, body) {
    const default_difficulty = category_difficulty[category];
    const param_table = _.assign({}, table);
    const blocks = [];
    for (const b of param_table.blocks) {
        const block = {
            type: b.questions[0].type,
            questions: b.questions.map(e => {
                const type_t = TYPES[e.type] || TYPES['default'];
                const obj = {
                    type: e.type,
                    score: e.score || type_t.default_score,
                    knowledges: e.knowledges,
                    difficulty: category === 4 ? e.difficulty : default_difficulty
                };
                if (category === 4 || obj.difficulty === '不限') {
                    obj.difficulty = '中等';
                }
                return obj;
            })
        }
        blocks.push(block);
    }
    const set = new Set();
    for (const q of exam.questions || []) {
        (q.same || []).forEach(id => set.add(id));
    }
    const params = {
        period: exam.period,                // 必选，学段
        subject: exam.subject,              // 必选，科目
        grade: exam.grade,                  // 选填，年级
        type: '平行组卷',                           // 必填，平行组卷
        // school_id: user.schoolId,                 // 选填，学校ID
        user_id: exam.user_id.toString(),              // 选填，用户ID
        paper_id: body.paper_id.split('-')[0],    // 选填，试卷ID
        class_name: body.class_name || '',        // 选填，班级名
        filtered_ques: [...set],                  // 必填，过滤试题ID，默认为[]
        // min_year: int,             // 选填，最小试题年份限制
        blocks,
    }
    return params;
}

/**
 * 获取好分数试题类型
 * @param subject
 * @param hfs_question
 * @param yp_question
 * @returns {Promise<*>}
 * @private
 */
function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}


