const log4js = require('log4js');
const config = require('config');
// const logger = log4js.getLogger();

class Logger {

    constructor(options) {
        log4js.configure(options);
        // log4js.configure(config.get('log4js'));
        this.logger = log4js.getLogger();
    }
    info(message, ...args) {
        this.logger.info(message, ...args);
    }

    error(message, ...args) {
        this.logger.error(message, ...args);
    }

    warn(message, ...args) {
        this.logger.warn(message, ...args);
    }
}

module.exports = new Logger(config.get('log4js'));

// function init() {
//     log4js.configure(config.get('log4js'));
// }


//
// module.exports = {
//     init,
//     info,
//     error,
//     warn,
// }
