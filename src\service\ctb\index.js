const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/ctb');


module.exports = {
    getExamList,
    getExamPaperClasses,
    getExamPaperQuestionDetail,
    questionSearch,
    putExamPaperQuestionSame,
    getCtbConfigStudents,
    addCtbConfig,
    getCtbConfigList,
    getCtbConfigDetail,
    getCtbContent,
    download,
    // getExamPaperQuestionSame,
    // getExamPaperQuestionAnswerImage,
    // getExamPaperStudentQuestionAnswer,
    // putExamPaperQuestionAnswerTag,
}

const JOI_EXAM_LIST = Joi.object({
    offset: Joi.string().required(),
    limit: Joi.string().required(),
    type: Joi.number().optional(),
    event_time_start: Joi.number().optional(),
    event_time_end: Joi.number().optional(),
    exam_name: Joi.string().optional().allow(''),

});

async function getExamList(req, res) {
    const params = await JOI_EXAM_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getExamList(params);
}

const JOI_GET_EXAM_PAPER_CLASSES = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperClasses(req, res) {
    const params = await JOI_GET_EXAM_PAPER_CLASSES.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getExamPaperClasses(params);
}

const JOI_GET_EXAM_PAPER_QUESTION_DETAIL = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    zujuanId: Joi.string().optional().allow(''),
});

async function getExamPaperQuestionDetail(req, res) {
    const params = await JOI_GET_EXAM_PAPER_QUESTION_DETAIL.validateAsync(_.assign(req.params, req.query));
    params.user_id = req.user.id;
    return await model.getExamPaperQuestionDetail(params);
}

const JOI_POST_EXAM_QUESTION_SEARCH = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(), // 分页参数
    type: Joi.string().optional().allow('', null), // 类型-非必填
    difficulty: Joi.string().optional().allow('', null), // 难度-非必填
    exam_type: Joi.string().optional().allow('', null), // 题源-非必填
    province: Joi.string().optional().allow('', null), // 省份-非必填
    knowledges: Joi.array().items(Joi.object({
        id: Joi.number().required(),
        name: Joi.string().required(),
    })).optional(), // 知识点-非必填
    question_id: Joi.number().optional().allow('', null), // 试题ID-换一题的时候必填
});

async function questionSearch(req, res) {
    const params = await JOI_POST_EXAM_QUESTION_SEARCH.validateAsync(_.assign(req.params, req.body));
    params.user_id = req.user.id;
    return await model.questionSearch(params);
}

const JOI_PUT_EXAM_PAPER_QUESTION_SAME = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    questions: Joi.array().items(Joi.object({
        id: Joi.number().required()
    })).required()
});

async function putExamPaperQuestionSame(req, res) {
    const params = await JOI_PUT_EXAM_PAPER_QUESTION_SAME.validateAsync(_.assign(req.params, req.body));
    params.user_id = req.user.id;
    return await model.putExamPaperQuestionSame(params);
}

const JOI_POST_CTB_CONFIG = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    questions: Joi.array().items(Joi.object({
        id: Joi.string().required()
    })).required().min(1),
    student_config: Joi.object({
        origin_question: Joi.number().required().min(0).max(1),
        same_question: Joi.number().required().min(0).max(1),
    }).required(),
    class_config: Joi.object({
        origin_question: Joi.number().required().min(0).max(1),
        same_question: Joi.number().required().min(0).max(1),
    }).required(),
});

async function addCtbConfig(req, res) {
    const params = await JOI_POST_CTB_CONFIG.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.addCtbConfig(params);
}

const JOI_GET_CTB_CONFIG_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    exam_name: Joi.string().optional().allow(''),
    ctime_start: Joi.number().optional(),
    ctime_end: Joi.number().optional(),
});

async function getCtbConfigList(req, res) {
    const params = await JOI_GET_CTB_CONFIG_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getCtbConfigList(params);
}

const JOI_GET_CTB_CONFIG_DETAIL = Joi.object({
    id: Joi.string().required(),
});

async function getCtbConfigDetail(req, res) {
    const params = await JOI_GET_CTB_CONFIG_DETAIL.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getCtbConfigDetail(params);
}

const JOI_GET_CTB_CONFIG_STUDENTS = Joi.object({
    config_id: Joi.string().required(),
});

async function getCtbConfigStudents(req, res) {
    const params = await JOI_GET_CTB_CONFIG_STUDENTS.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getCtbConfigStudents(params);
}

const JOI_GET_CTB_CONTENT = Joi.object({
    id: Joi.string().required(),
    class_id: Joi.string().required(),
    student_id: Joi.string().optional().allow('')
});

async function getCtbContent(req, res) {
    const params = await JOI_GET_CTB_CONTENT.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getCtbContent(params);
}

const JOI_DOWNLOAD = Joi.object({
    filename: Joi.string().required(), // 来源
    content: Joi.string().required(), // 内容
});

async function download(req, res) {
    const params = await JOI_DOWNLOAD.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.download(req, res, params);
}
