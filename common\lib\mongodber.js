const config = require('config');
const MongoClient = require("mongodb").MongoClient;

const logger = require('./logger');

const database = {};

module.exports = {
    init,
    collection,
}

async function init() {
    const client = new MongoClient(config.get('db.tiku'), config.get('db.options'));
    await client.connect();
    database.db = client.db();
    logger.info(` db tiku`)
}

async function collection(name) {
    return database.db.collection(name);
}

