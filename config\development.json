{"app": {"name": "open_tiku_serv", "port": 8055, "url": "https://open-tiku-serv-wan.yunxiao.com", "client_domain": "https://grayopentiku.yxzhixue.com", "env": "development"}, "db": {"opentiku": "mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testopentiku?replicaSet=ReplsetTest&readPreference=primaryPreferred", "kb_zyk": "mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testopentiku?replicaSet=ReplsetTest&readPreference=secondaryPreferred", "jzl_jiaoyan": "mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testjzl_jiaoyan?replicaSet=ReplsetTest&readPreference=primaryPreferred", "tiku": "mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testtiku?replicaSet=ReplsetTest&readPreference=secondaryPreferred", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "ignoreUndefined": true}}, "redis": {"host": "**********", "port": 6000, "db": 0, "password": ""}, "kb_api_server": {"protocol": "http", "hostname": "************", "port": 8500, "appKey": "iyunxiao_tiku20200421"}, "util_server": {"protocol": "http", "hostname": "devdnr-kb-util-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421", "appid": "yxzx", "tmpAppid": "tmp"}, "yuanpei_api_server": {"protocol": "http", "hostname": "dnr-kb-yuanpei-lan.yunxiao.com", "port": 80, "appkey": "iyunxiao_tiku20200421"}, "algo_server": {"protocol": "http", "hostname": "graynew-tiku-algo-lan.yunxiao.com", "port": 80}, "hfs_teacher_v3_server": {"protocol": "http", "hostname": "grayhfs-teacher-v3.yunxiao.com", "port": 80, "appKey": "7kQ5GNumrJNXXkWo"}, "tiku_server": {"protocol": "http", "hostname": "devdnr-tiku-serv-wan.yunxiao.com", "port": 80}, "scantron": {"sk": "IHGGghjklBNJiuytg1567ytfghjuy", "protocol": "https", "hostname": "graydtk.haofenshu.com", "port": 443, "yunxiao": "graydtk.yunxiao.com", "yxzhixue": "graydtk.yxzhixue.com"}, "hfs_server": {"protocol": "http", "hostname": "hfs-support-lan.yunxiao.com", "port": 80, "apiKey": "Ktaoo9CbUep4ElAQKLTSinNiMmPfy0qw"}, "rank_server": {"protocol": "http", "hostname": "rank-serv.yunxiao.com", "port": 80}, "yj_api_server": {"protocol": "http", "hostname": "gray-yj-api.haofenshu.com", "port": 80, "appCenterKey": "462fd506cf7c463caa4bdfa94fad5ea3", "key": "db34b4cdfb3e98a7828714d31886c9b9"}, "file_server": {"protocol": "http", "hostname": "************", "port": 9993}, "boss_server": {"protocol": "http", "hostname": "boss-api.iyunxiao.com", "port": 80, "apikey": "9c0ef6da3b7a07bc311a0ebb092ca23a"}, "xkw_server": {"protocol": "https", "hostname": "sso.zxxk.com", "port": 443, "app_key": "108331740043373600", "service": "https://zjse.xkw.com", "app_secret": "cHIjrwUgtc6Z0119Mk6UQoFOPCmoTd6q"}, "xkw_api_server": {"protocol": "https", "hostname": "openapi.xkw.com", "port": 443, "app_key": "108331740043373600", "app_secret": "cHIjrwUgtc6Z0119Mk6UQoFOPCmoTd6q"}, "ai_ques_server": {"protocol": "http", "hostname": "ai-ques-serv.yunxiao.com", "port": 80}, "word_parser_server": {"protocol": "http", "hostname": "dnr-wordparser-lan.yunxiao.com", "port": 80}, "se_kb_server": {"protocol": "http", "hostname": "kb-se-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421"}, "ai_kb_server": {"protocol": "http", "hostname": "ai-kb-serv-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421"}, "jiaoyan_server": {"protocol": "http", "hostname": "testjiaoyan-api-lan.yunxiao.com", "port": 80}, "mkp_server": {"protocol": "https", "hostname": "mkp-serv-wan.yunxiao.com", "port": 443, "appKey": "iyunxiao_tk250704"}, "english_tw_list": {"高中": [1719033028, 81814468, 1803508932, 1950178500, 1724538052, 1719033028, 81814468, 1803508932, 1950178500, 1724538052], "初中": [940400836, 72377284, 194928068, 1743477956, 1720278212, 940400836, 72377284, 194928068, 1743477956, 1720278212]}, "cors": ["http://devopentiku.yunxiao.com", "https://devopentiku.yunxiao.com"], "unify": {"app_id": "11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2", "public_key": "7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D"}, "log4js": {"appenders": {"console": {"type": "console"}, "normal": {"type": "dateFile", "filename": "logs/log.log", "pattern": "_yyyy-MM-dd", "alwaysIncludePattern": false}, "error": {"type": "dateFile", "filename": "logs/error.log", "pattern": "_yyyy-MM-dd", "alwaysIncludePattern": false}}, "categories": {"error": {"appenders": ["error", "normal"], "level": "error"}, "console": {"appenders": ["console"], "level": "debug"}, "default": {"appenders": ["normal", "console"], "level": "info"}}}}