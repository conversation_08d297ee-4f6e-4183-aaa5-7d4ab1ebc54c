'use strict';
const express = require('express');
const router = express.Router();
const service = require('../../service/resource_album');
const { handler } = require('../../../common/middlewares/request');

// 获取专辑列表-公共
router.get('/list', handler(1, service.getAlbumList));
// 获取专辑列表-学校
router.get('/school/list', handler(1, service.getSchoolAlbumList));
// 获取专辑详细
router.get('/:id', handler(1, service.getAlbumDetail));
// 获取专辑数据详细
router.get('/:id/data/:data_id', handler(1, service.getAlbumDataDetail));
// 下载
router.post('/download', handler(1, service.download));

module.exports = router;
