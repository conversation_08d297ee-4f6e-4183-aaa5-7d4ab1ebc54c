const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/zyk/resource');
const enums = require('../../../common/enums/enums');

module.exports = {
    getCatalogList,
    getResourceList,
    getResourceDetail,
    downloadResource,
}

const JOI_GET_CATALOG_LIST = Joi.object({
    book_id: Joi.number().required(), // 用户ID
    chapter_id: Joi.number().required(), //  学段
    type: Joi.number().required(), // 科目
});

async function getCatalogList(req, res) {
    const params = await JOI_GET_CATALOG_LIST.validateAsync(req.query);
    params.school_id = req.user.school_id;
    return await model.getCatalogList(params);
}

const JOI_GET_RESOURCE_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    book_id: Joi.string().optional(), // 教材ID
    chapter_id: Joi.string().optional(), // 章节ID
    type: Joi.string().optional().allow(''), // 类型
    type_id: Joi.string().optional().allow(''), // 类型
    // catalog_id: Joi.string().optional().allow(''),
    sort_by: Joi.string().optional().default('utime')
});

async function getResourceList(req, res) {
    const params = await JOI_GET_RESOURCE_LIST.validateAsync(req.query);
    params.school_id = req.user.school_id;
    return await model.getResourceList(params);
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.string().required(),
    // type: Joi.string().required(), // 类型
    from: Joi.number().optional().default(1), // 来源
});

async function getResourceDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.query);
    params.user_id = req.user.id;
    params.school_id = req.user.school_id;
    return await model.getResourceDetail(params);
}

const JOI_DOWNLOAD = Joi.object({
    id: Joi.string().required(),
    // type: Joi.number().required(), // 类型
    from: Joi.number().required(), // 来源
    content: Joi.string().optional().allow(''), // 内容
});

async function downloadResource(req, res) {
    const params = await JOI_DOWNLOAD.validateAsync(req.body);
    params.user_id = req.user.id;
    params.school_id = req.user.school_id;
    return await model.downloadResource(req, res, params);
}
