const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/zyk/user_prep_catalog');

module.exports = {
    getUserPrepCatalog,
    updateUserPrepCatalog,
}

const JOI_GET_USER_CATALOG = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    category: Joi.number().required(),
});

async function getUserPrepCatalog(req, res) {
    const params = await JOI_GET_USER_CATALOG.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getUserCatalog(params);
}

const JOI_UPDATE_USER_CATALOG = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    category: Joi.number().required(),
    children: Joi.array().items(Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().required(),
        children: Joi.any()
    })).optional().default([])
});

async function updateUserPrepCatalog(req, res) {
    const params = await JOI_UPDATE_USER_CATALOG.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.updateUserPrepCatalog(params);
}

