const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getUserCatalog,
    updateUserPrepCatalog,
}

async function getUserCatalog(params) {
    const {user_id, period, subject, category} = params;
    let catalog = await db.collection(schema.user_prep_catalog).findOne({user_id: user_id, period, subject, category});
    if (_.isEmpty(catalog)) {
        catalog = {
            user_id,
            period,
            subject,
            category,
            children: [{
                id: new ObjectId().toString(),
                name: '我的备课'
            }],
            valid: enums.BOOL.YES,
            ctime: new Date(),
            utime: new Date()
        };
        await db.collection(schema.user_prep_catalog).insertOne(catalog);
    }
    return catalog.children;
}

async function updateUserPrepCatalog(params) {
    const {user_id, period, subject, category} = params;
    const catalog = await db.collection(schema.user_prep_catalog).findOne({user_id: user_id, period, subject, category});
    const children = params.children || [];
    if (_.size(children)) {
        const handler = (list) => {
            if (!_.size(list)) return;
            for (const data of list) {
                if (!data.id) data.id = new ObjectId().toString();
                handler(data.children);
            }
        }
        handler(children);
    }
    const date = new Date();
    if (_.isEmpty(catalog)) {
        const ds = {
            user_id,
            period,
            subject,
            category,
            children,
            valid: enums.BOOL.YES,
            ctime: date,
            utime: date,
        }
        await db.collection(schema.user_prep_catalog).insertOne(ds);
    } else {
        const ds = {
            children,
            utime: date
        }
        await db.collection(schema.user_prep_catalog).updateOne({_id: catalog._id}, {$set: ds});
    }
    return children;
}


