const Joi = require('@hapi/joi');
const _ = require('lodash');

const enums = require('../../../common/enums/enums');
const model = require('../../models/edu_plan/team_prep');

module.exports = {
    getList,
    getDetail,
    save,
    updateStatus,
    deleteById,
    getLessonResource,
    saveLessonResource
}

const JOI_GET_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional().default(''),
    from_year: Joi.number().optional(), // 学年
    to_year: Joi.number().optional(), // 学年
    semester: Joi.string().optional(), // 学期
    name: Joi.string().optional().default(''),
    status: Joi.string().optional().default(''),
});

async function getList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    return await model.getList(params, req.user);
}

const JOI_ID = Joi.object({
    id: Joi.string().required(),
});

async function getDetail(req, res) {
    const params = await JOI_ID.validateAsync(req.params);
    return await model.getDetail(params, req.user);
}

const JOI_SAVE = Joi.object({
    id: Joi.string().optional(),
    edu_plan_id: Joi.string().required(),
    name: Joi.string().required(),
    end_time: Joi.string().required(),
    remark: Joi.string().optional().default(''),
    status: Joi.string().optional().default(enums.TeamPrepStatus.INIT),
    teachers: Joi.array().items(Joi.string()).optional().default([]),
    weeks: Joi.array().items(Joi.object({
        index: Joi.number().required(), // 周索引
        stime: Joi.string().required(), // 开始时间
        etime: Joi.string().required(), // 结束时间
        children: Joi.array().items(Joi.object({
            id: Joi.string().required(),
            name: Joi.string().required(),
            key: Joi.string().required(),
            // source_id: Joi.string().optional(),
            book_id: Joi.number().optional().default(0),
            chapter_id: Joi.number().optional().default(0),
            children: Joi.array().items(Joi.object()).optional()
        })).optional()
    })).min(1).required()
});

async function save(req, res) {
    const params = await JOI_SAVE.validateAsync(req.body);
    return await model.save(params, req.user);
}

const JOI_UPDATE_STATUS = Joi.object({
    id: Joi.string().required(),
    status: Joi.string().valid(...Object.values(enums.TeamPrepStatus)).required()
});

async function updateStatus(req, res) {
    const params = await JOI_UPDATE_STATUS.validateAsync(req.params);
    return await model.updateStatus(params, req.user);
}

async function deleteById(req, res) {
    const params = await JOI_ID.validateAsync(req.params);
    return await model.deleteById(params, req.user);
}

const JOI_GET_LESSON_RESOURCE = Joi.object({
    id: Joi.string().required(),
    lesson_id: Joi.string().required(),
    key: Joi.string().required()
});

async function getLessonResource(req, res) {
    const params = await JOI_GET_LESSON_RESOURCE.validateAsync(req.query);
    return await model.getLessonResource(params, req.user);
}

const JOI_SAVE_LESSON_RESOURCE = Joi.object({
    id: Joi.string().required(),
    lesson_id: Joi.string().required(),
    key: Joi.string().required(),
    children: Joi.array().items(Joi.object({
        id: Joi.alternatives().try(
            Joi.number(),
            Joi.string(),
        ).required(),
        score: Joi.number().optional().allow(0),
        source: Joi.string().optional().default('sys'),
        source_id: Joi.alternatives().try(
            Joi.number(),
            Joi.string(),
        ).optional().allow('', null) // 来源ID
    })).required()
});

async function saveLessonResource(req, res) {
    const params = await JOI_SAVE_LESSON_RESOURCE.validateAsync(req.body);
    return await model.saveLessonResource(params, req.user);
}




