const axios = require('axios');

module.exports = {
  getImageProxy,
}

// 处理图片流的接口(处理图片跨域)
async function getImageProxy (req, res) {
  const { url } = req.query
  if (!url) {
    throw new Error('参数错误')
  }

  // 使用 axios 获取图片流（设置 responseType 为 'stream'）
  const response = await axios({
    method: 'get',
    url: url,
    responseType: 'stream'
  })

  // 设置响应头
  res.set({
    'Content-Type': response.headers['content-type'], // 保持与源相同的 Content-Type
    'Content-Length': response.headers['content-length'], // 可选
    'Cache-Control': 'public, max-age=31536000' // 根据需求设置缓存

  })
  return response.data
}
