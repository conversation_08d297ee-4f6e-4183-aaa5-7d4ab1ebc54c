{"app": {"name": "open_tiku_serv", "port": 8055, "url": "https://grayopen-tiku-serv-wan.yunxiao.com", "client_domain": "https://grayzujuan.yxzhixue.com", "env": "gray"}, "db": {"opentiku": "**************************************************************************************************************************************", "kb_zyk": "mongodb://chuban_write:g9LuwYwvg7gSONyO@************:6010,************:6010/kb_zyk?replicaSet=Replset07&readPreference=secondaryPreferred", "jzl_jiaoyan": "mongodb://tiku_write:<EMAIL>:6010,n01.rs07.yunxiao.io:6010/jzl_jiaoyan?replicaSet=Replset07&readPreference=primaryPreferred&slaveOk=true", "options": {"useNewUrlParser": true, "useUnifiedTopology": true, "ignoreUndefined": true}}, "redis": {"host": "**********", "port": 6000, "db": 0, "password": "<EMAIL>"}, "kb_api_server": {"protocol": "http", "hostname": "dnr-kb-api-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421"}, "util_server": {"protocol": "http", "hostname": "dnr-kb-util-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421", "appid": "yxzx", "tmpAppid": "tmp"}, "yuanpei_api_server": {"protocol": "http", "hostname": "dnr-kb-yuanpei-lan.yunxiao.com", "port": 80, "appkey": "iyunxiao_tiku20200421"}, "algo_server": {"protocol": "http", "hostname": "graynew-tiku-algo-lan.yunxiao.com", "port": 80}, "hfs_teacher_v3_server": {"protocol": "http", "hostname": "grayhfs-teacher-v3.yunxiao.com", "port": 80, "appKey": "7kQ5GNumrJNXXkWo"}, "tiku_server": {"protocol": "http", "hostname": "graytiku-serv-wan.yunxiao.com", "port": 80}, "scantron": {"sk": "IHGGghjklBNJiuytg1567ytfghjuy", "protocol": "https", "hostname": "graydtk.haofenshu.com", "port": 443, "yunxiao": "graydtk.yunxiao.com", "yxzhixue": "graydtk.yxzhixue.com"}, "hfs_server": {"protocol": "http", "hostname": "hfs-support-lan.yunxiao.com", "port": 80, "apiKey": "Ktaoo9CbUep4ElAQKLTSinNiMmPfy0qw"}, "rank_server": {"protocol": "http", "hostname": "rank-serv.yunxiao.com", "port": 80}, "yj_api_server": {"protocol": "http", "hostname": "gray-yj-api.haofenshu.com", "port": 80, "appCenterKey": "462fd506cf7c463caa4bdfa94fad5ea3", "key": "db34b4cdfb3e98a7828714d31886c9b9"}, "file_server": {"protocol": "http", "hostname": "************", "port": 9993}, "boss_server": {"protocol": "http", "hostname": "boss-api.iyunxiao.com", "port": 80, "apikey": "9c0ef6da3b7a07bc311a0ebb092ca23a"}, "xkw_server": {"protocol": "https", "hostname": "sso.zxxk.com", "port": 443, "app_key": "108331740043373600", "service": "https://zjse.xkw.com", "app_secret": "cHIjrwUgtc6Z0119Mk6UQoFOPCmoTd6q"}, "xkw_api_server": {"protocol": "https", "hostname": "openapi.xkw.com", "port": 443, "app_key": "108331740043373600", "app_secret": "cHIjrwUgtc6Z0119Mk6UQoFOPCmoTd6q"}, "ai_ques_server": {"protocol": "http", "hostname": "ai-ques-serv.yunxiao.com", "port": 80}, "word_parser_server": {"protocol": "http", "hostname": "dnr-wordparser-lan.yunxiao.com", "port": 80}, "se_kb_server": {"protocol": "http", "hostname": "kb-se-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421"}, "ai_kb_server": {"protocol": "http", "hostname": "ai-kb-serv-lan.yunxiao.com", "port": 80, "appKey": "iyunxiao_tiku20200421"}, "jiaoyan_server": {"protocol": "http", "hostname": "grayjiaoyan-api-lan.yunxiao.com", "port": 80}, "english_tw_list": {"高中": [4097728196, 4089536196, 956718788, 952524484, 949903044, 81814468, 533159620, 885808836, 1190880196, 1217094596], "初中": [4127022788, 4100873924, 4091633348, 4114177732, 4080557764, 681991876, 681402052, 864378564, 879713988, 878862020]}, "cors": [".yunxiao.com", ".yxzhixue.com"], "unify": {"app_id": "11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2", "public_key": "7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D"}, "log4js": {"appenders": {"console": {"type": "console"}, "normal": {"type": "dateFile", "filename": "logs/log.log", "pattern": "_yyyy-MM-dd", "alwaysIncludePattern": false}, "error": {"type": "dateFile", "filename": "logs/error.log", "pattern": "_yyyy-MM-dd", "alwaysIncludePattern": false}}, "categories": {"error": {"appenders": ["error", "normal"], "level": "error"}, "console": {"appenders": ["console"], "level": "debug"}, "default": {"appenders": ["normal", "console"], "level": "info"}}}}