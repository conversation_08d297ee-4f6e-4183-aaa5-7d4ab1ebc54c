'use strict';

const enums = require('../enums');
const logger = require('../lib/logger');
const BussError = require('../exceptions/BussError');

// const utils = require('./utils');
// const errCodes = require('./error');
// const BusError = require('./BusError');

const stream = require('stream');
const response = require('../lib/response');

/**
 * API 异常/返回值统一处理
 * @param apiCode lib/api.js中的代码
 * @param fun api逻辑 async function (req, res)
 */
function handler (code, fun) {
    return async (req, res) => {
        try {
            const result = await fun(req, res);
            if (result && (Buffer.isBuffer(result) || result instanceof stream)) {
                if (Buffer.isBuffer(result)) {
                    res.send(result);
                } else {
                    result.pipe(res);
                }
            } else {
                return res.json(response.success(result));
            }
        } catch (err) {
            logger.error(err);
            if (err.isJoi) {
                return res.json(response.error(enums.code.PARAM_ERR, err.message));
            }
            if (err instanceof BussError) {
                return res.json(response.error(err.code, err.message));
            }
            // if (err.isJoi) { // 参数验证错误
            //     code = enums.code.PARAM_ERR;
            //     msg = `参数错误：${err.message}`;
            //     return res.json(utils.resJSON(errCodes.PARAM_ERR, '参数错误: ' + err.message, {}, errMsg));
            // }
            // 可增加其余自定义异常处
            // return res.json(utils.resJSON(errCodes.INTERNAL_ERR, err.message || err || '内部错误(oms)', {}, errMsg));
            return res.json(response.error(code, err.message || ''));
        }
    };
}

module.exports = {
    handler
};
