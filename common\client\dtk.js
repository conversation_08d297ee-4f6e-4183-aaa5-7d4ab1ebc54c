
const crypto = require('crypto');
const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('scantron');
const qs = require('querystring');

module.exports = {
    getGateWay,
};

/**
 * 考后巩固组卷
 * @returns {Promise<*>}
 * @param paperId
 */
async function getGateWay(user, host = '') {
    const object = {
        protocol: server.protocol,
        hostname: server.yunxiao,
        port: server.port
    };
    if (host && host.includes('yxzhixue')) object.hostname = server.yxzhixue;
    const url = URL.format(object);
    const query = {
        id: user.userId,
        name: user.name,
        schoolId: user.schoolId,
        schoolName: user.schoolName,
        timestamp: Date.now()
    }

    let token = '';
    token += user.userId;
    token += query.timestamp;
    token += server.sk;
    const md5 = crypto.createHash('md5');
    const dtk = md5.update(token).digest('hex');
    query.dtk = dtk;
    const result = {
        url: url,
        query: qs.stringify(query),
        input: query
    }
    return result;
}
