const config = require('config');
const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const server = config.get('util_server');

module.exports = {
    getQuestionAnswerImage,
    getDownloadInfo,
    getPaperByHtml,
    downloadExampaper,
}

async function getQuestionAnswerImage(questionId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/utilbox_api/v2/questions/${questionId}/image2`,
        search: qs.stringify({
            api_key: server.appKey,
            appid: server.tmpAppid
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function getDownloadInfo(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/utilbox_api/v1/paper/download`,
        search: qs.stringify({
            api_key: server.appKey,
            // appid: server.tmpAppid
        })
    });
    const result = await axios.post(url, {
        typeset: true,
        math: false,
        size: params.size || 'A4',
        seal: params.seal,
        html: params.html,
        name: params.filename,
        source_plat: 'tiku'
    }, {
        responseType: 'arraybuffer'
    });
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
        throw new Error('获取试题信息失败');
    }
    return result;
}


async function getPaperByHtml(params) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/utilbox_api/v1/exampapers/documents`,
        search: qs.stringify({
            api_key: server.appKey,
            // appid: server.tmpAppid
        })
    });
    const result = await axios.post(url, {
        target: 'exampaper', // 'exampaper', 'answer', 'all'
        format: 'docx', // 'docx'
        size: 'A4',   // 'A3', 'A4', 'A3_simple', 'A3_double', 'B4_double'
        filename: params.filename,
        content_type: 'html', // html
        content: params.html, // 内容
        // typeset: true,
        // math: false,
        // size: params.size || 'A4',
        // seal: params.seal,
        // html: params.html,
        // name: params.filename,
        source_plat: 'tiku'
    }, {
        responseType: 'arraybuffer'
    });
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
        throw new Error('获取试题信息失败');
    }
    return result;
}

async function downloadExampaper(doc) {
    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/utilbox_api/v1/exampapers/documents',
        search: qs.stringify({
            type: 'json',
            fields_type: 'full',
            // api_key: server.appKey
        })
    });
    const options = {
        timeout: 20000,
        responseType: 'arraybuffer'
    }
    const result = await axios.post(url, doc, options);
    return result;
}



