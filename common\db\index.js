const config = require('config');
const MongoClient = require("mongodb").MongoClient;

const logger = require('../lib/logger');

const database = {
    db: null,
    db_zyk: null,
    db_jzl: null,
    db_tiku: null,
};

module.exports = {
    init,
    collection,
    zyk_collection,
    jzl_collection,
    tiku_collection,
}

async function init() {
    const client = new MongoClient(config.get('db.opentiku'), config.get('db.options'));
    await client.connect();
    database.db = client.db();
    logger.info(`db opentiku init success`);
    const zyk_client = new MongoClient(config.get('db.kb_zyk'), config.get('db.options'));
    await zyk_client.connect();
    database.db_zyk = zyk_client.db();
    logger.info(`db zyk init success`);
    const jzl_client = new MongoClient(config.get('db.jzl_jiaoyan'), config.get('db.options'));
    await jzl_client.connect();
    database.db_jzl = jzl_client.db();
    logger.info(`db jzl_jiaoyan init success`);
    const tiku_client = new MongoClient(config.get('db.tiku'), config.get('db.options'));
    await tiku_client.connect();
    database.db_tiku = tiku_client.db();
    logger.info(`db tiku init success`);
}

function collection(name) {
    return database.db.collection(name);
}

function zyk_collection(name) {
    return database.db_zyk.collection(name);
}

function jzl_collection(name) {
    return database.db_jzl.collection(name);
}

function tiku_collection(name) {
    return database.db_tiku.collection(name);
}

