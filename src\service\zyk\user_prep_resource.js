const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/zyk/user_prep_resource');

module.exports = {
    getResourceList,
    getResourceKeys,
    addResource,
    saveOrUpdateCustomResource,
    updateResourceBaseInfo,
    deleteResource,
}

const JOI_GET_LIST = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    category: Joi.number().required(),
    // type: Joi.string().optional(),
    type_id: Joi.string().optional(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    catalog_id: Joi.string().optional().allow(''), // 目录ID-非必填
});

async function getResourceList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getResourceList(params);
}


const JOI_GET_KEYS = Joi.object({
    type: Joi.number().required(),
});

async function getResourceKeys(req, res) {
    // const params = await JOI_GET_KEYS.validateAsync(req.query);
    // params.user_id = req.user.id;
    return await model.getResourceKeys({user_id: req.user.id});
}



const JOI_ADD_RESOURCE = Joi.object({
    // type: Joi.number().required(),
    catalog_id: Joi.string().optional().default('').allow(''),
    resource_id: Joi.string().required(),
});

async function addResource(req, res) {
    const params = await JOI_ADD_RESOURCE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.addResource(params);
}

const JOI_SAVE_OR_UPDATE_RESOURCE = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type_id: Joi.string().required(),
    catalog_id: Joi.string().optional().default('').allow(''),
    id: Joi.string().optional(),
    name: Joi.string().required(),
    children: Joi.array().items(Joi.object({
        id: Joi.string().required()
    }))
});
async function saveOrUpdateCustomResource(req, res) {
    const params = await JOI_SAVE_OR_UPDATE_RESOURCE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.saveOrUpdateCustomResource(params);
}


const JOI_UPDATE_RESOURCE = Joi.object({
    id: Joi.string().required(),
    // type: Joi.number().required(),
    fields: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        value: Joi.string().optional().default('').allow('')
    })).min(1)
});

async function updateResourceBaseInfo(req, res) {
    const params = await JOI_UPDATE_RESOURCE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.updateResourceBaseInfo(params);
}

const JOI_DELETE_RESOURCE = Joi.object({
    id: Joi.string().required(),
    // type: Joi.number().required(),
});

async function deleteResource(req, res) {
    const params = await JOI_DELETE_RESOURCE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.deleteResource(params);
}

