const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const prepUtils = require('../../../common/utils/prep_utils');
const mime = require('mime');

module.exports = {
    getResourceList,
    getResourceKeys,
    addResource,
    saveOrUpdateCustomResource,
    updateResourceBaseInfo,
    deleteResource,
}

async function getResourceList(params) {
    const {user_id, category, catalog_id, type, period, subject} = params;
    let {type_id} = params;
    if (!type_id && type) {
        if (_.isNumber(type)) {
            type_id = prepUtils.getTypeIdByType(type);
        } else {
            type_id = type;
        }
    }
    const query = {
        user_id,
        period,
        subject,
        valid: enums.BOOL.YES
    }
    if (type_id) query.type_id = type_id;
    if (catalog_id) {
        const user_catalog = await db.collection(schema.user_prep_catalog).findOne({user_id, category, period, subject});
        const arr = [];
        const fun = (children, is_child = false) => {
            if (!_.size(children)) return;
            for (const child of children) {
                if (child.id === catalog_id || is_child) {
                    arr.push(child.id);
                    fun(child.children, true);
                } else {
                    fun(child.children, is_child);
                }
            }
        }
        for (const child of user_catalog.children) {
            let is_child = false;
            if (child.id === catalog_id) {
                is_child = true;
                arr.push(child.id);
            }
            fun(child.children, is_child);
        }
        if (_.size(arr)) {
            query.catalog_id = {$in: arr};
        } else {
            query.catalog_id = catalog_id;
        }
    }
    const result = {
        total: 0,
        list: []
    };
    result.total = await db.collection(schema.user_prep_resource).find(query).count();
    if (!result.total) return result;
    const list = await db.collection(schema.user_prep_resource).find(query).sort({ctime: -1}).skip(params.offset).limit(params.limit).toArray();
    for (const data of list) {
        data.id = data._id.toString();
        data.ctime = data.ctime.getTime();
        data.utime = data.utime.getTime();
        delete data._id;
        delete data.valid;
    }
    result.list = list;
    return result;
}

async function getResourceKeys(params) {
    const {user_id, type} = params;
    let {type_id} = params;
    if (!type_id && type) {
        if (_.isNumber(type)) {
            type_id = prepUtils.getTypeIdByType(type);
        } else {
            type_id = type;
        }
    }
    // if (!type_id) throw new BussError('资源类型不存在');
    const query = {user_id: user_id, valid: enums.BOOL.YES };
    if (type_id) query.type_id = type_id;
    const list = await db.collection(schema.user_prep_resource).find(query).toArray();
    return _.chain(list).filter(e => e.from_id).map(e => e.from_id).value();
}

async function addResource(params) {
    const {user_id, type, resource_id, catalog_id, from} = params;
    let {type_id} = params;
    if (!type_id && type) {
        if (_.isNumber(type)) {
            type_id = prepUtils.getTypeIdByType(type);
        } else {
            type_id = type;
        }
    }
    if (!type_id) throw new BussError('资源类型不存在');
    let resource = await db.collection(schema.user_prep_resource).findOne({user_id, type_id, from_id: resource_id});
    if (!_.isEmpty(resource)) {
        throw new BussError('不可以重复添加');
    }

    if (type_id === enums.ResourceType.homework.type_id) { // 作业
        resource = await client.kb.getPaperById(resource_id);
        resource.type_id = type_id;
    }  else if (type_id === enums.ResourceType.user_paper.type_id) { // 我的组卷
        resource = await db.collection(schema.user_paper).findOne({_id: new ObjectId(resource_id)});
        resource.type_id = type_id;
    } else {
        if (from === enums.UserPrepResourceFrom.SYS) {
            resource = await client.kb.getEduFileById(resource_id);
            if (!_.isEmpty(resource)) {
                resource.type_id = enums.KbFileCategoryMap[resource.category];
                resource.file = {
                    host: resource.host,
                    url: resource.url,
                    mime: resource.suffix || resource.url.split('.')[1],
                    size: resource.size || 0
                }
            }
        } else {
            resource = await db.zyk_collection(schema.zyk.prep_resource).findOne({_id: new ObjectId(resource_id)});
        }
    }
    if (_.isEmpty(resource)) {
        throw new BussError('系统无法找到该文件');
    }
    const date = new Date();
    const doc = {
        user_id,
        catalog_id: catalog_id || '',        // 用户目录ID
        from: from, // 来源 1-系统，2-用户创建
        from_id: resource_id,            // 来源ID
        period: resource.period,
        grade: resource.grade,
        subject: resource.subject,
        type: resource.type,
        type_id: resource.type_id,
        name: resource.name,
        // children: resource.children, // 23/04/25 去掉了这个本分
        file: resource.file,
        // mime: resource.mime || '', // 文件扩展类型
        valid: enums.BOOL.YES,
        ctime: date,
        utime: date
    }
    await db.collection(schema.user_prep_resource).insertOne(doc);
}

async function saveOrUpdateCustomResource(params) {
    let {user_id, id, children, period, subject, type} = params;
    if (!_.size(children)) throw new BussError('内容不能为空');
    let {type_id} = params;
    if (!type_id && type) {
        if (_.isNumber(type)) {
            type_id = prepUtils.getTypeIdByType(type);
        } else {
            type_id = type;
        }
    }
    const ids = children.map(e => e.id);
    const elements = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    const new_children = [];
    for (const id of ids) {
        const data = elements.find(e => e._id.toString() === id);
        if (_.isEmpty(data)) continue;
        new_children.push({
            id,
            name: data.name
        });
    }
    params.children = new_children;
    let resource = null;
    const date = new Date();
    if (id) { // 编辑
        resource = await db.collection(schema.user_prep_resource).findOne({_id: new ObjectId(id)});
        if (_.isEmpty(resource) || resource.user_id !== user_id) {
            throw new BussError('不可编辑');
        }
        delete params.user_id;
        delete params.id;
        params.utime = date;
        if (!_.isEqual(resource.children, params.children)) {
            await setFile(params, elements);
        }
        await db.collection(schema.user_prep_resource).updateOne({_id: new ObjectId(id)}, {$set: params});
    } else {
        params.from = enums.UserPrepResourceFrom.USER;
        params.from_id = '';
        params.valid = enums.BOOL.YES;
        params.ctime = date;
        params.utime = date;
        params.type_id = type_id;
        await setFile(params, elements);
        const result = await db.collection(schema.user_prep_resource).insertOne(params);
        id = result.insertedId.toString();
        // 清空资源篮
        await db.collection(schema.user_prep_basket).updateOne({user_id, period, subject, type_id}, {$set: {children: []}});
    }
    return {id};
}

async function updateResourceBaseInfo(params) {
    const {user_id, id, fields} = params;
    let resource = await db.collection(schema.user_prep_resource).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(resource) || resource.user_id !== user_id) {
        throw new BussError('资源不存在');
    }
    const ds = {
        utime: new Date()
    }
    for (const field of fields) {
        ds[field.name] = field.value;
    }
    await db.collection(schema.user_prep_resource).updateOne({_id: new ObjectId(id)}, {$set: ds});
}

async function deleteResource (params) {
    const {user_id, id, type} = params;
    await db.collection(schema.user_prep_resource).deleteOne({_id: new ObjectId(id), user_id: user_id});
}

async function setFile(resource, elements) {
    const el = elements[0];
    const el_file = _.get(el, 'children.0.content.0.value', null);
    if (el_file.hasOwnProperty('mime')) {
        const mime = el_file.mime;
        if (_.size(elements) === 1) {
            resource.file = el_file;
        }else if (mime === 'pptx') {
            resource.file = await genMergeFile(resource);
        } else {
            resource.file = null;
        }
    }
}


async function genMergeFile(resource) {
    const ids = resource.children.map(e => e.id);
    const elements = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    if (_.size(elements) === 1) return _.get(elements[0], 'children.0.content.0.value', null);
    const mergeParams = {
        file_type: 'pptx',
        file_urls: []
    };
    const images = [];
    for (const id of ids) {
        const el = elements.find(e => e._id.toString() === id);
        if (_.isEmpty(el)) continue;
        for (const child of el.children) {
            for (const content of child.content) {
                mergeParams.file_urls.push(`${content.value.host}${content.value.url}`);
            }
        }
        images.push(..._.get(el, 'children.0.content.0.value.images', []))
    }
    // for (const el of elements) {
    //     for (const child of el.children) {
    //         for (const content of child.content) {
    //             mergeParams.file_urls.push(`${content.value.host}${content.value.url}`);
    //         }
    //     }
    // }
    const file = await client.algo.mergeFile(mergeParams);
    if (!file) throw new BussError('获取文件失败');
    const {origin, pathname} = new URL(file.url);
    const result =  {
        mime: mergeParams.file_type,
        size: file.size,
        host: origin,
        url: pathname,
        images: images
    };
    // // 生成缩略图
    // const params = {
    //     period: resource.period || '',
    //     subject: resource.subject || '',
    //     file_name: resource.name || '',
    //     file_url: `${result.host}${result.url}`
    // }
    // const images = await client.file.pptxToImages(params);
    // result.images = images;
    return result;
}







