const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../../common/enums/template');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getSchoolAppUsageInfo,
    getSchoolAllAppUsageInfo,
}

async function getSchoolAppUsageInfo(params) {
    const {schoolId, productCategory} = params;
    return await client.boss.getSchoolInfo(schoolId, productCategory);
}

async function getSchoolAllAppUsageInfo(user) {
    return await client.boss.getSchoolAllAppUsageInfo(user.school_id);
}
