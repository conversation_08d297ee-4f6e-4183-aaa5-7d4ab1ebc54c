const express = require('express');
const router = express.Router();
const service = require('../service/ctb');
const { handler } = require('../../common/middlewares/request');

// 获取考试列表
router.get('/exam/list', handler(1, service.getExamList));
// 获取关联考试班级列表
router.get('/exam/:exam_id/paper/:paper_id/classes', handler(1, service.getExamPaperClasses));
// 获取考试详细
router.get('/exam/:exam_id/paper/:paper_id/questions', handler(1, service.getExamPaperQuestionDetail));
// 相似题搜索
router.post('/exam/:exam_id/paper/:paper_id/question/search', handler(1, service.questionSearch));
// 替换相似题
router.put('/exam/:exam_id/paper/:paper_id/question/:question_id/same', handler(1, service.putExamPaperQuestionSame));
// 保存错题本导出配置
router.post('/config', handler(1, service.addCtbConfig));
// 获取配置列表
router.get('/config/list', handler(1, service.getCtbConfigList));
// 获取配置详细
router.get('/config/:id', handler(1, service.getCtbConfigDetail));
// 获取考试学生
router.get('/config/:config_id/students', handler(1, service.getCtbConfigStudents));
// 预览像错题本
router.get('/content', handler(1, service.getCtbContent));
// 下载
router.post('/download', handler(1, service.download));

module.exports = router;
