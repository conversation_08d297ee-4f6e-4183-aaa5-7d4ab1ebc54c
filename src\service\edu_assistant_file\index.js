const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/edu_assistant_file');
const enums = require('../../../common/enums/enums');

module.exports = {
    getList,
    getDetail,
    download,
    updateTimes,
}

const JOI_GET_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    book_id: Joi.number().optional(),
    chapter_id: Joi.number().optional(),
    type_id: Joi.string().required(), // 分类ID
    sort_by: Joi.string().optional()
}).unknown(true);

async function getList(req, res) {
    const params = await JOI_GET_LIST.validateAsync(req.query);
    return await model.getList(params);
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    from: Joi.number().optional().valid(...Object.values(enums.UserPrepResourceFrom)).default(enums.UserPrepResourceFrom.SYS), // 来源
});
async function getDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getDetail(params);
}

const JOI_DOWNLOAD = Joi.object({
    id: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    type_id: Joi.string().required(),
    from: Joi.number().required(), // 来源
    content: Joi.string().optional().allow(''), // 内容
});

async function download(req, res) {
    const params = await JOI_DOWNLOAD.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.download(req, res, params);
}

const JOI_UPDATE_TIMES = Joi.object({
    id: Joi.number().required(),
    action: Joi.string().valid(...Object.keys(enums.FileAction)).required()
});

async function updateTimes(req, res) {
    const params = await JOI_UPDATE_TIMES.validateAsync(req.body);
    await model.updateTimes(req, res, params);
}
