---
title: 考试管理V2接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 考试管理V2接口

考试管理V2接口提供增强的考试讲评功能，支持教师标注、相似题推荐、讲评课件生成和平行试卷创建。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取考试试卷班级列表

GET /v2/exam/{exam_id}/paper/{paper_id}/classes

获取指定考试试卷对应的班级列表，仅返回教师有任课关系的班级。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v2/exam/exam_001/paper/paper_001/classes" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "class_001",
      "name": "七年级1班",
      "relation": 1,
      "student_count": 45,
      "submitted_count": 44,
      "average_score": 87.5
    }
  ]
}
```

---

## GET 获取考试试卷题目详情

GET /v2/exam/{exam_id}/paper/{paper_id}/questions

获取考试试卷的详细题目信息，包括相似题推荐和教师标注数据。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v2/exam/exam_001/paper/paper_001/questions" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "64f8a9b1c2d3e4f5a6b7c8d9",
    "class_info": [
      {
        "id": "class_001",
        "name": "七年级1班",
        "courseware_name": "七年级1班期中考试讲评课件",
        "questions": ["q_001", "q_003", "q_005"]
      }
    ],
    "questions": [
      {
        "key": "q_001",
        "content": "下列数中，是有理数的是（）",
        "type": 1,
        "difficulty": "容易",
        "score": 4,
        "accuracy": 0.85,
        "sim": [
          {
            "id": 12345,
            "content": "关于有理数的分类，下列说法正确的是（）",
            "type": "选择题",
            "difficulty": "容易"
          }
        ],
        "mark_info": {
          "excellent": [
            {
              "id": "stu_001",
              "name": "张三",
              "score": 4,
              "image_url": "https://example.com/answer/stu_001.jpg"
            }
          ],
          "mediocre": [
            {
              "id": "stu_002",
              "name": "李四",
              "score": 2,
              "image_url": "https://example.com/answer/stu_002.jpg"
            }
          ]
        }
      }
    ]
  }
}
```

### 错误示例

#### 考试不存在

```json
{
  "code": 4,
  "msg": "考试不存在或者未同步!",
  "data": ""
}
```

#### 未上传原卷

```json
{
  "code": 4,
  "msg": "未上传原卷",
  "data": ""
}
```

#### 不存在任课班级

```json
{
  "code": 4,
  "msg": "不存在任课班级",
  "data": ""
}
```

---

## PUT 更新讲评信息

PUT /v2/exam/{exam_id}/paper/{paper_id}/comment

更新考试试卷的班级讲评信息，包括选择的题目和课件名称。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|class_info|body|array|是|班级讲评信息数组|
|» id|body|string|是|班级ID|
|» name|body|string|是|班级名称|
|» courseware_name|body|string|是|课件名称|
|» questions|body|array|否|选中的题目key数组|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v2/exam/exam_001/paper/paper_001/comment" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "id": "class_001",
      "name": "七年级1班",
      "courseware_name": "七年级1班期中考试讲评课件",
      "questions": ["q_001", "q_003", "q_005"]
    }
  ]'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "64f8a9b1c2d3e4f5a6b7c8d9"
  }
}
```

---

## PUT 设置题目相似题

PUT /v2/exam/{exam_id}/paper/{paper_id}/question/{key}/sim

为指定题目设置相似题列表，用于讲评时的变式练习。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|key|path|string|是|题目唯一标识|
|ids|body|array|是|相似题ID数组|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v2/exam/exam_001/paper/paper_001/question/q_001/sim" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [12345, 12346, 12347]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 12345,
      "content": "关于有理数的分类，下列说法正确的是（）",
      "type": "选择题",
      "difficulty": "容易",
      "knowledges": ["有理数"],
      "subject": "数学",
      "period": "初中"
    }
  ]
}
```

---

## POST 搜索相似题

POST /v2/exam/{exam_id}/paper/{paper_id}/question/search

根据知识点或题目特征搜索相似题目，用于丰富讲评内容。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|offset|body|number|是|分页偏移量|
|limit|body|number|是|每页数量|
|knowledges|body|array|否|知识点筛选条件|
|» id|body|number|是|知识点ID|
|» name|body|string|是|知识点名称|
|question_id|body|number|否|参考题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v2/exam/exam_001/paper/paper_001/question/search" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "offset": 0,
    "limit": 20,
    "knowledges": [
      {
        "id": 1001,
        "name": "有理数"
      }
    ],
    "question_id": 12345
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 156,
    "list": [
      {
        "id": 12346,
        "content": "有理数的加法运算规律是（）",
        "type": "选择题",
        "difficulty": "中等",
        "score": 4,
        "knowledges": ["有理数", "加法运算"],
        "subject": "数学",
        "period": "初中"
      }
    ]
  }
}
```

---

## PUT 标记学生作答

PUT /v2/exam/{exam_id}/paper/{paper_id}/question/{key}/answer/mark

为题目的学生作答添加优秀或一般标记，用于讲评时展示典型答案。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|key|path|string|是|题目唯一标识|
|type|body|string|是|标记类型：excellent(优秀)、mediocre(一般)|
|student_id|body|number|是|学生ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v2/exam/exam_001/paper/paper_001/question/q_001/answer/mark" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "excellent",
    "student_id": 123456
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "64f8a9b1c2d3e4f5a6b7c8d9"
  }
}
```

---

## PUT 更新考试状态

PUT /v2/exam/{exam_id}/paper/{paper_id}/status/{status}

将考试讲评从草稿状态转为正式状态，完成讲评准备。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|status|path|string|是|目标状态|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v2/exam/exam_001/paper/paper_001/status/done" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "64f8a9b1c2d3e4f5a6b7c8d9"
  }
}
```

---

## GET 获取讲评列表

GET /v2/exam/comment/list

获取已完成的考试讲评列表，支持按学段和科目筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|period|query|string|否|学段筛选|
|subject|query|string|否|科目筛选|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v2/exam/comment/list?offset=0&limit=20&period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 12,
    "list": [
      {
        "id": "64f8a9b1c2d3e4f5a6b7c8d9",
        "exam_id": "exam_001",
        "exam_name": "2024年春季期中考试",
        "paper_id": "paper_001",
        "period": "初中",
        "subject": "数学",
        "grade": "七年级",
        "class_info": [
          {
            "id": "class_001",
            "name": "七年级1班",
            "courseware_name": "七年级1班期中考试讲评课件",
            "question_num": 5
          }
        ],
        "ctime": 1698825600000,
        "utime": 1698912000000
      }
    ]
  }
}
```

---

## GET 获取班级讲评详情

GET /v2/exam/comment/{id}/class/{class_id}/type/{type}

获取指定班级的详细讲评信息，包括选中的题目和相似题。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|考试记录ID|
|class_id|path|string|是|班级ID|
|type|path|string|是|数据类型：draft(草稿) 或 formal(正式)|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v2/exam/comment/64f8a9b1c2d3e4f5a6b7c8d9/class/class_001/type/formal" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "class_info": {
      "id": "class_001",
      "name": "七年级1班",
      "courseware_name": "七年级1班期中考试讲评课件",
      "questions": ["q_001", "q_003", "q_005"]
    },
    "questions": [
      {
        "key": "q_001",
        "content": "下列数中，是有理数的是（）",
        "type": 1,
        "difficulty": "容易",
        "score": 4,
        "accuracy": 0.85,
        "sim": [
          {
            "id": 12345,
            "content": "关于有理数的分类，下列说法正确的是（）",
            "type": "选择题",
            "difficulty": "容易"
          }
        ],
        "mark_info": {
          "excellent": [
            {
              "id": "stu_001",
              "name": "张三",
              "score": 4,
              "image_url": "https://example.com/answer/stu_001.jpg"
            }
          ],
          "mediocre": []
        }
      }
    ]
  }
}
```

---

## GET 获取平行追踪试卷

GET /v2/exam/{exam_id}/paper/{paper_id}/category/{category}/paper

根据考试结果生成平行追踪试卷，用于考后巩固练习。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|category|path|number|是|试卷分类（1:简单卷 2:中等卷 3:培优卷）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v2/exam/exam_001/paper/paper_001/category/2/paper" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "64f8a9b1c2d3e4f5a6b7c8d9",
      "name": "平行追踪试卷",
      "question_num": 25,
      "grade": "七年级",
      "period": "初中",
      "subject": "数学",
      "source": "EXAM",
      "type": "考后巩固",
      "from_year": 2024,
      "to_year": 2024,
      "status": "DONE",
      "exam_status": "EDITABLE",
      "ctime": 1698825600000,
      "utime": 1698912000000,
      "error": "",
      "from_enum": "GROUP"
    }
  ]
}
```

### 错误示例

#### 平行组卷失败

```json
{
  "code": 4,
  "msg": "平行组卷失败",
  "data": ""
}
```

---

## 返回数据结构

### 班级信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|班级ID|唯一标识|
|name|string|true|none|班级名称|如"七年级1班"|
|courseware_name|string|true|none|课件名称|讲评课件名称|
|questions|array|false|none|题目列表|选中的题目key数组|
|question_num|integer|false|none|题目数量|统计信息|

### 题目信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|true|none|题目标识|题目唯一key|
|content|string|true|none|题目内容|题目文本|
|type|integer|true|none|题目类型|1-主观题 2-客观题|
|difficulty|string|true|none|难度|容易/中等/困难|
|score|number|true|none|分值|题目分数|
|accuracy|number|true|none|正确率|0-1之间的小数|
|sim|array|false|none|相似题|相似题目数组|
|mark_info|object|false|none|标记信息|教师标注的优秀/一般作答|

### 标记信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|excellent|array|true|none|优秀作答|优秀学生作答列表|
|mediocre|array|true|none|一般作答|一般学生作答列表|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 讲评流程

1. **数据准备**: 首次访问时自动初始化考试数据，包括班级信息和题目结构
2. **题目分析**: 自动推荐相似题目，教师可手动调整相似题列表
3. **标注答案**: 教师标记优秀和一般的学生作答，用于讲评展示
4. **生成课件**: 选择需要讲评的题目，为每个班级生成个性化课件
5. **巩固练习**: 生成平行追踪试卷，供学生课后练习

### 数据来源

- **考试信息**: 来自HFS教务系统和元培阅卷系统
- **题目推荐**: 基于知识库算法服务的相似题推荐
- **学生作答**: 来自答题卡扫描和阅卷系统
- **平行试卷**: 通过算法服务生成的同难度题目

### 权限控制

- 教师只能操作自己任教班级的考试数据
- 草稿状态支持反复编辑，正式状态不可修改
- 相似题搜索基于当前考试的学段、科目限制