const qs = require('querystring');
const config = require('config');
const crypto = require('crypto');
const _ = require('lodash');
const aesUtils = require('./aesUtils');


module.exports = {
    generateSignature,
    generateApiSignature,
}

/**
 * 获取参数签名
 * @param {object} params
 * @param secret
 */
function generateSignature(params, secret) {
    if (!params) {
        return '';
    }
    const arr = [];
    for (const key in params) {
        if (key === 'state') continue;
        arr.push(params[key].toString())
    }
    arr.push(secret);
    return aesUtils.md5(arr.join(''));
}

// const app_secret = 'ed4545f513444725bd811e909d3ac79f';
//
// const signParams = {
//     client_id: 'key808',
//     extra: aesUtils.aesEncrypt('111', app_secret),
//     open_id: '',
//     redirect_uri: `http://baidu.com`,
//     service: 'http://t.zxxk.com/user/uc',
//     timespan: aesUtils.aesEncrypt('111', app_secret),
// }
// console.log('sign:', generateSignature(signParams, app_secret));
//
// const map = new Map();
// map.set('name', 11);
// map.set('age', 22);
// map.set('sex', 33);
// console.log(generateSignature(map, 'ed4545f513444725bd811e909d3ac79f'));


function generateApiSignature(path, header, query, body) {
    let params = _.assign({},
        _.pick(header, ['Xop-App-Id', 'Xop-Nonce', 'Xop-Timestamp']),
        {xop_url: path});
    if (query) params = _.assign(params, query);
    if (body) params['xop_body'] = body;
    // const keys = Object.keys(params).sort();
    let paramsStr = '';
    for (const key of Object.keys(params).sort()) {
        if (!paramsStr) paramsStr += `${key}=${params[key] || ''}`;
        else paramsStr += `&${key}=${params[key] || ''}`;
    }
    paramsStr += `&secret=${config.get('xkw_api_server.app_secret')}`;
    // paramsStr += `&secret=test`;
    // console.log('paramsStr', paramsStr);
    // base64
    const base64String = Buffer.from(paramsStr).toString('base64');
    // console.log('base64String', base64String);
    // 签名
    const result = sha1Base64ToHex(base64String);
    // console.log('sign', result);
    return result;
}

function sha1Base64ToHex(base64Str) {
    // 1. 创建SHA1哈希
    const hash = crypto.createHash('sha1');
    // 2. 更新哈希内容
    hash.update(base64Str);
    // 3. 生成16进制格式的摘要
    return hash.digest('hex');
}

// const header = {
//     'Xop-App-Id': 'test',
//     'Xop-Nonce': '01e7bd52ee7b45328630fe39d7f295ad',
//     'Xop-Timestamp': 1645151617
// }
// const path = '/xopqbm/textbooks';
//
// const query = {
//     course_id:27,
//     grade_id: '',
//     page_index: 1,
//     page_size: 10,
//     version_id: ''
// }
//
// const body = {"course_id":1,"area_ids":[],"year":2018,"kpoint_ids":[],"type_ids":[],"page_index":2,"paper_type_ids":[],"catalog_ids":[],"difficulty_levels":[]};
//
// generateApiSignature(path, header, query);
