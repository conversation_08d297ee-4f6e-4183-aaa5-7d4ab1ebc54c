const express = require('express');
const router = express.Router();
const service = require('../../service/user/user_prep_resource');
const { handler } = require('../../../common/middlewares/request');

//
router.get('/list', handler(1, service.getResourceList));
// 获取ID集合
router.get('/keys', handler(1, service.getResourceKeys));
// 修改基本信息
router.put('/base', handler(1, service.updateResourceBaseInfo));
// 编辑或添加自定义备课资源
router.post('/custom', handler(1, service.saveOrUpdateCustomResource));
// 添加系统备课资源
router.post('', handler(1, service.addResource));
// 删除资源
router.delete('', handler(1, service.deleteResource));

module.exports = router;
