const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');

const school_id = 110934; // 小语种学校
// const school_id = 55553; // 测试学校

const ResourceTypes = ['question', 'exampaper', 'repository','intelligent', 'homework'];



module.exports = {
    getStudyInfo,
    getCatalogInfo,
    getBookDetail,
    getCustomResource,
}

async function getStudyInfo() {
    const docs = await db.zyk_collection(schema.zyk.rbac_property).find({ school_id }).toArray();

    // 找到父节点，学段对应的数据无 parents 字段
    let record = _.find(docs, e => !e.parents);

    if (_.isEmpty(record)) return [];

    // {高中: {key, name, children}, ...}
    let periodObject = _.zipObject(record.values, _.map(record.values, e => ({
        key: 'period',
        name: e,
        children: []
    })));

    for (let doc of docs) {
        // doc is property
        if (doc.parents) {
            // parent means `period`
            let pdName = doc.parents.name;
            if (!periodObject[pdName]) {
                continue;
            }
            periodObject[pdName].children = _.map(doc.values, x => ({
                key: doc.key,
                name: x
            }));
        }
    }
    return _.filter(_.values(periodObject), x => x);
}


async function getCatalogInfo(params) {
    const { period, subject } = params;
    let rbacCatalog = await getRBACCatalog('book');
    let catalogRes = await db.zyk_collection(schema.zyk.catalogs).findOne({ type: 'book', school_id: school_id });
    if (catalogRes) {
        let catalogData = catalogRes;
        rbacCatalog.book.children.forEach(rbacPeriod => {
            catalogData.content.children.forEach(period => {
                if (rbacPeriod.children && rbacPeriod.children.length && rbacPeriod.name === period.name) {
                    rbacPeriod.children.forEach(rbacSubject => {
                        if (period.children && period.children.length) {
                            period.children.forEach(subject => {
                                if (subject.children && subject.children.length && rbacSubject.name === subject.name) {
                                    rbacSubject.children = subject.children;
                                }
                            });
                        }
                    });
                }
            });
        });
        if (rbacCatalog && rbacCatalog.book) {
            if (period) {
                let periodArr = rbacCatalog.book.children.filter(ele => {
                    return ele.name === period;
                });

                rbacCatalog.book.children = periodArr;
                delete rbacCatalog.content;
                delete rbacCatalog._id;
                if (subject && periodArr.length) {
                    let subjectArr = periodArr[0].children.filter(ele => {
                        return ele.name === subject;
                    });
                    periodArr[0].children = subjectArr;
                }
            }
        }
    } else {
        if (rbacCatalog && rbacCatalog.book) {
            if (period) {
                let periodArr = rbacCatalog.book.children.filter(ele => {
                    return ele.name === period;
                });

                rbacCatalog.book.children = periodArr;
                delete rbacCatalog.content;
                delete rbacCatalog._id;
                if (subject && periodArr.length) {
                    let subjectArr = periodArr[0].children.filter(ele => {
                        return ele.name === subject;
                    });
                    periodArr[0].children = subjectArr;
                }
            }
        }
    }
    return rbacCatalog;
}

async function getBookDetail(params) {
    const { book_id } = params;
    return await getBookDetailById(book_id);
}

const filterCatalog = (rbacCatalog, type, period, subject) => {
    let baseInfo = rbacCatalog[type];
    let child1 = _.filter(baseInfo.children, { name: period });

    baseInfo.children = child1;
    if (!child1.length) {
        return;
    }
    _.each(child1, (subs) => {
        subs.children = _.filter(subs.children, { name: subject });
    });
};

const getRBACCatalog = async (type) => {
    if (['book', 'knowledge_tree', 'category'].indexOf(type) === -1) {
        throw new Error('未知的类型');
    }
    const rbacPeriodCatalog = await db.zyk_collection(schema.zyk.rbac_property).findOne({
        'school_id': school_id,
        key: 'period'
    });
    const rbacSubjectCatalog = await db.zyk_collection(schema.zyk.rbac_property).find({
        'school_id': school_id,
        'key': 'subject'
    }).toArray();
    let rbacCatalog = {};
    rbacCatalog[type] = {};
    rbacCatalog[type].children = [];
    if (rbacPeriodCatalog && rbacPeriodCatalog.values && rbacPeriodCatalog.values instanceof Array) {
        rbacPeriodCatalog.values.forEach((ele) => {
            let periodObj = {};
            periodObj.key = 'period';
            periodObj.name = ele;
            rbacCatalog[type].children.push(periodObj);
        });
    }

    rbacSubjectCatalog.forEach((rbacSubjectItem) => {
        let subjectArr = [];
        rbacCatalog[type].children.forEach((periodObj) => {
            if (periodObj.name === rbacSubjectItem.parents.name) {
                rbacSubjectItem.values.forEach((rbacSubjectValue) => {
                    let subjectObj = {};
                    subjectObj.key = 'subject';
                    subjectObj.name = rbacSubjectValue;
                    subjectArr.push(subjectObj);
                });
                periodObj.children = subjectArr;
            }
        });
    });

    return rbacCatalog;
};


// 获取教材详情方法
async function getBookDetailById(book_id) {
    const cond = {
        _id: new ObjectId(book_id),
        is_del: false
    }
    const bookDetail = await db.zyk_collection(schema.zyk.books).findOne(cond);

    if (!bookDetail) {
        throw new Error('未找到教材');
    }
    bookDetail.id = bookDetail._id.toString();
    delete bookDetail._id;
    if (bookDetail.book &&
        bookDetail.book.children instanceof Array &&
        bookDetail.book.children.length) {
        addKeyForKnowledge(bookDetail.book.children);
    }
    bookDetail.id = bookDetail.id + '';
    return bookDetail;

}

/**
 * 遍历数组，如果数组中元素ref的值为old，则给其添加key属性为newValue
 * @param {*} newValue 要设置的值
 * @param {*} old 之前的值
 * @param {*} arr 要遍历的数组
 */
const addKeyForKnowledge = (arr) => {
    for (let item of arr) {
        if (item.key === 'chapter') {
            if (item.children && item.children instanceof Array) {
                addKeyForKnowledge(item.children);
            }
        } else if (item.key === 'chapter_knowledge') {
            if (item.knowledge_children && item.knowledge_children instanceof Array) {
                addKeyForKnowledge(item.knowledge_children);
            }
        } else if (item.key === 'knowledge') {
            delete item.period;
            delete item.subject;
            delete item.user_id;
            delete item.user_name;
            delete item.ctime;
            delete item.utime;
            delete item.contents;
        }
    }
};

async function getCustomResource(params) {
    return await _getCustomConfig(school_id);
}



const commonConfig = {
    repository: {
        data: {
            id: 'repository',
            name: '文库',
            limit: 15,
            values: [{
                'id' : 'lesson_ware',
                'name' : '课件'
            }, {
                'id' : 'teach_plan',
                'name' : '教案'
            }, {
                'id' : 'study_plan',
                'name' : '学案'
            }, {
                'id' : 'video',
                'name' : '视频'
            }, {
                'id' : 'image',
                'name' : '图片'
            }, {
                'id' : 'material',
                'name' : '素材'
            }]
        }
    },
    question: {
        data: {
            id: 'question',
            name: '试题',
        }
    },
    exampaper: {
        data: {
            id: 'exampaper',
            name: '试卷',
        }
    },
    intelligent: {
        data: {
            id: 'intelligent',
            name: '智能组卷',
        }
    },
    homework: {
        data: {
            id: 'homework',
            name: '教辅资料',
        }
    }
};
/**
 * 获取学校自定义配置，如果没有，则返回通用配置
 * @param {number} schoolId
 * @param {string} type - 文库repository, 导航nav, 试题question，试卷exampaper，不指定的话会返回文库/试题/试卷的自定义配置
 * @returns {*}
 */
const _getCustomConfig = async (schoolId, type) => {
    if (!type) {
        let configs = await Promise.all(ResourceTypes.map(async (resourceType) => {
            return await _getCustomConfig(schoolId, resourceType);
        }));

        return _.zipObject(ResourceTypes, configs);
    }

    let records = await db.zyk_collection(schema.zyk.common_config).find({
        $or: [
            { key: `custom_${type}`, school_id: schoolId },
            { key: `common_${type}`},
        ]}).toArray();
    let resourceConfig = records.find(e => e.key === `custom_${type}`);

    // 更新通用配置
    commonConfig[type] = records.find(e => e.key === `common_${type}`) || commonConfig[type];

    resourceConfig = resourceConfig ? resourceConfig : _.cloneDeep(commonConfig[type]);

    if (!resourceConfig) throw `获取资源分类信息失败，学校ID：${schoolId}`;

    return resourceConfig.data;
};

