# 测试Markdown文件

这是一个测试的markdown文件，用于验证通过域名+路径访问功能。

## 功能特性

- 支持通过域名+路径访问/public下的markdown文件
- 删除/public前缀，直接使用下级路径
- 自动渲染markdown为HTML格式

## 示例

### 代码块

```javascript
function hello() {
    console.log('Hello, World!');
}
```

### 列表

1. 第一项
2. 第二项
3. 第三项

- 无序列表项1
- 无序列表项2
- 无序列表项3

### 链接和图片

[访问GitHub](https://github.com)

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 结论

这个markdown文件可以通过域名直接访问，无需/public前缀。
