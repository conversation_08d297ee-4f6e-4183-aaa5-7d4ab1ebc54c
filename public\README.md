# Markdown文件服务功能说明

## 概述

本系统支持通过域名+路径直接访问`/public`目录下的markdown文件，并提供多种查看模式。

## 主要特性

### 1. 路径映射
- 文件存储在 `/public` 目录下
- 访问时删除 `/public` 前缀
- 直接使用下级路径访问

**示例：**
- 文件路径: `/public/docs/guide.md`
- 访问URL: `http://domain.com/docs/guide.md`

### 2. 查看模式切换

支持两种查看模式，通过URL参数控制：

#### HTML渲染模式 (默认)
- 参数: `?view=html` 或不带参数
- 功能: 将markdown渲染为美观的HTML页面
- 特性:
  - 支持GitHub风格的markdown语法
  - 自动生成标题ID
  - 响应式设计
  - 语法高亮支持
  - 表格、列表、链接等完整支持

#### 原始模式
- 参数: `?view=raw` 或 `?view=markdown`
- 功能: 显示原始的markdown源码
- 用途: 查看源码、复制内容、调试等

### 3. 导航功能

在HTML渲染模式下，页面顶部提供查看模式切换按钮：
- **HTML渲染**: 当前为HTML模式时高亮显示
- **原始Markdown**: 当前为原始模式时高亮显示

## 使用示例

### 基本访问
```
# HTML渲染模式
http://localhost:3000/README.md
http://localhost:3000/README.md?view=html

# 原始模式
http://localhost:3000/README.md?view=raw
http://localhost:3000/README.md?view=markdown
```

### 子目录文件
```
# 访问docs目录下的文件
http://localhost:3000/docs/guide.md
http://localhost:3000/docs/guide.md?view=raw
```

## 技术实现

### 中间件架构
- 使用Express.js中间件处理markdown请求
- 在静态文件服务之前拦截.md文件请求
- 支持查询参数解析和模式切换

### 依赖包
- `marked`: Markdown解析和HTML渲染
- `express`: Web服务器框架
- `fs`: 文件系统操作
- `path`: 路径处理

### 缓存策略
- HTML模式: 缓存1小时 (`Cache-Control: public, max-age=3600`)
- 原始模式: 缓存1小时
- 支持ETag和Last-Modified头

## 配置说明

### 文件结构
```
project/
├── public/                 # 静态文件目录
│   ├── README.md          # 根目录markdown文件
│   ├── docs/              # 文档子目录
│   │   ├── guide.md
│   │   └── markdown-feature.md
│   ├── assets/            # 静态资源
│   │   └── style.css
│   └── test.html          # 其他静态文件
├── common/
│   └── middlewares/
│       └── markdown.js    # Markdown处理中间件
└── bin/
    └── app.js            # 主应用配置
```

### 中间件配置
在 `bin/app.js` 中的配置顺序：
1. Markdown中间件 (处理.md文件)
2. 静态文件中间件 (处理其他文件)

## 注意事项

1. **文件安全**: 只能访问`/public`目录下的文件
2. **路径限制**: 不支持访问`/public`目录外的文件
3. **文件编码**: 文件必须使用UTF-8编码
4. **性能考虑**: 大文件可能影响渲染性能
5. **浏览器兼容**: HTML渲染模式支持现代浏览器

## 扩展功能

未来可以考虑添加的功能：
- 主题切换 (亮色/暗色)
- 目录导航 (TOC)
- 全文搜索
- 文件历史版本
- 在线编辑功能
