---
title: 教学计划接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 教学计划接口

教学计划接口提供学校教学计划管理和集体备课功能，支持创建教学大纲、组织集体备课活动、管理课时资源等核心功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

# 教学计划管理

## GET 查询教学计划

GET /v1/edu_plan

根据学段、科目、年级等条件查询学校的教学计划。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|科目名称|
|grade|query|string|是|年级|
|from_year|query|number|否|开始学年|
|to_year|query|number|否|结束学年|
|semester|query|string|否|学期（上学期/下学期）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_plan?period=初中&subject=数学&grade=七年级&to_year=2024&semester=上学期" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "edu_plan_001",
    "name": "七年级数学上学期教学计划",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "from_year": 2024,
    "to_year": 2024,
    "semester": "上学期",
    "school_id": "school_001",
    "user_id": "teacher_001",
    "children": [
      {
        "id": "chapter_001",
        "name": "第一章 有理数",
        "key": "chapter",
        "book_id": 1001,
        "chapter_id": 2001,
        "children": [
          {
            "id": "lesson_001",
            "name": "1.1 正数和负数",
            "key": "lesson",
            "source_id": "lesson_source_001",
            "children": []
          }
        ]
      },
      {
        "id": "exam_001",
        "name": "月考",
        "key": "exam",
        "children": []
      }
    ],
    "ctime": "2024-09-01T08:00:00Z",
    "utime": "2024-09-15T16:30:00Z"
  }
}
```

---

## GET 获取教学计划详情

GET /v1/edu_plan/{id}

获取指定教学计划的详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|教学计划ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_plan/edu_plan_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

返回结构与查询教学计划相同，包含完整的章节树形结构。

---

## POST 保存教学计划

POST /v1/edu_plan

创建或更新教学计划。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|否|编辑时传入教学计划ID|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|grade|body|string|是|年级|
|from_year|body|number|否|开始学年|
|to_year|body|number|是|结束学年|
|semester|body|string|是|学期|
|children|body|array|是|章节树，至少1个|
|» id|body|string|否|章节ID，新建时不传|
|» name|body|string|是|章节名称|
|» key|body|string|是|章节类型（chapter/lesson/exam）|
|» source_id|body|string|否|源ID|
|» book_id|body|number|否|教材ID|
|» chapter_id|body|number|否|教材章节ID|
|» children|body|array|否|子章节数组|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/edu_plan" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "to_year": 2024,
    "semester": "上学期",
    "children": [
      {
        "name": "第一章 有理数",
        "key": "chapter",
        "book_id": 1001,
        "chapter_id": 2001,
        "children": [
          {
            "name": "1.1 正数和负数",
            "key": "lesson",
            "source_id": "lesson_source_001"
          }
        ]
      },
      {
        "name": "月考",
        "key": "exam"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "edu_plan_002"
  }
}
```

### 错误示例

#### 不能重复创建

```json
{
  "code": 4,
  "msg": "不能重复创建",
  "data": ""
}
```

---

## GET 获取课时选项

GET /v1/edu_plan/chapter/lesson/items

获取教学计划中所有课时的资源配置情况。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string|是|教学计划ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_plan/chapter/lesson/items?id=edu_plan_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "lesson_001",
      "name": "1.1 正数和负数",
      "items": [
        {
          "key": "preview_material",
          "name": "预习资料",
          "configured": true
        },
        {
          "key": "homework",
          "name": "课后作业",
          "configured": false
        }
      ]
    }
  ]
}
```

---

## GET 获取课程推荐试卷

GET /v1/edu_plan/chapter/papers

获取与章节匹配的推荐试卷列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string|是|教学计划ID|
|chapter_id|query|string|是|章节ID|
|team_prep_id|query|string|否|集备ID（优先级更高）|
|lesson_id|query|string|否|课时ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_plan/chapter/papers?id=edu_plan_001&chapter_id=chapter_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 25,
    "list": [
      {
        "id": "paper_001",
        "name": "有理数单元测试卷",
        "question_count": 20,
        "score": 100,
        "difficulty": "中等",
        "period": "初中",
        "subject": "数学",
        "grade": "七年级"
      }
    ]
  }
}
```

---

# 集体备课管理

## GET 获取集备列表

GET /v1/team_prep/list

获取当前用户参与的集体备课列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|grade|query|string|否|年级|
|from_year|query|number|否|开始学年|
|to_year|query|number|否|结束学年|
|semester|query|string|否|学期|
|name|query|string|否|集备名称（模糊搜索）|
|status|query|string|否|状态（init/doing/done）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/team_prep/list?offset=0&limit=20&period=初中&subject=数学&status=doing" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 12,
    "list": [
      {
        "id": "team_prep_001",
        "name": "七年级数学第一章集备",
        "period": "初中",
        "subject": "数学",
        "grade": "七年级",
        "semester": "上学期",
        "status": "doing",
        "end_time": "2024-09-30T18:00:00Z",
        "overtime": "no",
        "main_teacher": {
          "id": "teacher_001",
          "name": "张老师"
        },
        "teacher_count": 5,
        "progress": 0.6,
        "ctime": "2024-09-15T10:00:00Z"
      }
    ]
  }
}
```

---

## GET 获取集备详情

GET /v1/team_prep/{id}

获取指定集体备课的详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|集备ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/team_prep/team_prep_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "team_prep_001",
    "name": "七年级数学第一章集备",
    "edu_plan_id": "edu_plan_001",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "semester": "上学期",
    "status": "doing",
    "end_time": "2024-09-30T18:00:00Z",
    "remark": "重点关注有理数概念的理解",
    "main_teacher": {
      "id": "teacher_001",
      "name": "张老师"
    },
    "teachers": [
      {
        "id": "teacher_002",
        "name": "李老师"
      },
      {
        "id": "teacher_003",
        "name": "王老师"
      }
    ],
    "children": [
      {
        "id": "chapter_001",
        "name": "第一章 有理数",
        "key": "chapter",
        "children": [
          {
            "id": "lesson_001",
            "name": "1.1 正数和负数",
            "key": "lesson",
            "items": [
              {
                "key": "preview_material",
                "name": "预习资料",
                "configured": true
              }
            ]
          }
        ]
      }
    ],
    "ctime": "2024-09-15T10:00:00Z",
    "utime": "2024-09-20T14:30:00Z"
  }
}
```

---

## POST 保存集备

POST /v1/team_prep

创建或更新集体备课。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|否|编辑时传入集备ID|
|edu_plan_id|body|string|是|教学计划ID|
|name|body|string|是|集备名称|
|end_time|body|string|是|截止时间（ISO格式）|
|remark|body|string|否|备注|
|status|body|string|否|状态，默认init|
|teachers|body|array|否|参备教师ID列表|
|children|body|array|是|章节树，至少1个|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/team_prep" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "edu_plan_id": "edu_plan_001",
    "name": "七年级数学第一章集备",
    "end_time": "2024-09-30T18:00:00Z",
    "remark": "重点关注有理数概念的理解",
    "teachers": ["teacher_002", "teacher_003"],
    "children": [
      {
        "name": "第一章 有理数",
        "key": "chapter",
        "children": [
          {
            "name": "1.1 正数和负数",
            "key": "lesson"
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "team_prep_002"
  }
}
```

### 错误示例

#### 集备不存在

```json
{
  "code": 4,
  "msg": "集备不存在",
  "data": ""
}
```

#### 不可编辑

```json
{
  "code": 4,
  "msg": "不可编辑",
  "data": ""
}
```

---

## PUT 更新集备状态

PUT /v1/team_prep/{id}/status/{status}

更新集体备课的状态。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|集备ID|
|status|path|string|是|目标状态（init/doing/done）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 状态说明

|状态|说明|权限|
|---|---|---|
|init|初始化|主备教师可编辑|
|doing|进行中|主备和参备教师可添加资源|
|done|已完成|只读，不可修改|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/team_prep/team_prep_001/status/doing" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "team_prep_001",
    "status": "doing"
  }
}
```

---

## DELETE 删除集备

DELETE /v1/team_prep/{id}

删除指定的集体备课（软删除）。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|string|是|集备ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/team_prep/team_prep_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "team_prep_001",
    "deleted": true
  }
}
```

---

## GET 获取课时资源

GET /v1/team_prep/chapter/lesson/resource

获取集备中指定课时的资源详情。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string|是|集备ID|
|lesson_id|query|string|是|课时ID|
|key|query|string|是|资源类型键|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/team_prep/chapter/lesson/resource?id=team_prep_001&lesson_id=lesson_001&key=preview_material" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "team_prep_001",
    "lesson_id": "lesson_001",
    "key": "preview_material",
    "name": "预习资料",
    "volumes": [
      {
        "title": "预习资料",
        "blocks": [
          {
            "name": "预习题目",
            "questions": [
              {
                "id": 12345,
                "content": "什么是正数和负数？",
                "type": "填空题",
                "score": 5,
                "source": "sys"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

---

## POST 保存课时资源

POST /v1/team_prep/chapter/lesson/resource

保存集备中指定课时的资源配置。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|是|集备ID|
|lesson_id|body|string|是|课时ID|
|key|body|string|是|资源类型键|
|children|body|array|是|资源列表|
|» id|body|number/string|是|资源ID|
|» score|body|number|否|分值|
|» source|body|string|否|来源，默认sys|
|» source_id|body|number/string|否|来源ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/team_prep/chapter/lesson/resource" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "team_prep_001",
    "lesson_id": "lesson_001",
    "key": "preview_material",
    "children": [
      {
        "id": 12345,
        "score": 5,
        "source": "sys"
      },
      {
        "id": 12346,
        "score": 8,
        "source": "user"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

返回保存后的完整资源结构，格式同获取课时资源接口。

---

## 返回数据结构

### 教学计划结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|教学计划ID|唯一标识|
|name|string|true|none|计划名称|教学计划标题|
|period|string|true|none|学段|小学/初中/高中|
|subject|string|true|none|科目|学科名称|
|grade|string|true|none|年级|适用年级|
|from_year|number|false|none|开始学年|学年范围|
|to_year|number|true|none|结束学年|学年范围|
|semester|string|true|none|学期|上学期/下学期|
|children|array|true|none|章节树|层级结构|

### 章节结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|章节ID|唯一标识|
|name|string|true|none|章节名称|章节标题|
|key|string|true|none|章节类型|chapter/lesson/exam|
|source_id|string|false|none|源ID|原始数据标识|
|book_id|number|false|none|教材ID|关联教材|
|chapter_id|number|false|none|教材章节ID|教材中的章节标识|
|children|array|false|none|子章节|下级章节列表|

### 集备信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|集备ID|唯一标识|
|name|string|true|none|集备名称|集备标题|
|edu_plan_id|string|true|none|教学计划ID|关联的教学计划|
|status|string|true|none|集备状态|init/doing/done|
|end_time|string|true|none|截止时间|ISO格式时间|
|remark|string|false|none|备注|集备说明|
|main_teacher|object|true|none|主备教师|主要负责人|
|teachers|array|false|none|参备教师|参与教师列表|
|overtime|string|false|none|是否超时|yes/no|

### 教师信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|教师ID|唯一标识|
|name|string|true|none|教师姓名|真实姓名|

### 资源配置结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|true|none|资源类型键|资源分类标识|
|name|string|true|none|资源名称|资源类型名称|
|configured|boolean|true|none|是否已配置|配置状态|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|12|参数错误|请求参数格式错误或缺失必要参数|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 教学计划管理

- **唯一性约束**: 同一学校、学段、科目、年级、学年、学期只能有一个教学计划
- **章节层级**: 支持章节-课时两级结构，可包含考试节点
- **资源关联**: 支持与教材章节的关联，便于资源推荐

### 集体备课流程

1. **创建阶段** (init): 主备教师创建集备，邀请参备教师
2. **进行阶段** (doing): 所有教师可以添加和编辑课时资源
3. **完成阶段** (done): 锁定所有修改，形成最终的备课成果

### 权限控制

- **主备教师**: 可以创建、编辑、删除集备，管理参备教师
- **参备教师**: 可以在进行阶段添加和编辑课时资源
- **其他教师**: 只能查看已完成的集备成果

### 资源类型

系统支持多种课时资源类型：
- **预习资料**: 课前预习材料
- **课后作业**: 课后练习题目
- **课堂练习**: 课中练习材料
- **拓展资源**: 额外的学习资源

### 时间管理

- **截止时间**: 集备设有截止时间，超时会标记为overtime
- **自动状态**: 状态变更会自动触发相关的数据同步
- **版本控制**: 支持草稿和正式版本的管理

### 使用场景

1. **学期规划**: 制定学期教学大纲和进度安排
2. **集体教研**: 组织同年级同科目教师进行集体备课
3. **资源共享**: 在备课过程中共享优质教学资源
4. **标准统一**: 统一教学标准和教学质量
5. **经验传承**: 将优秀的备课成果传承给其他教师