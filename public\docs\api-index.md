# OpenTiku API 接口文档总览

## 📋 文档信息

| 项目 | 值 |
|------|-----|
| **项目名称** | OpenTiku 教育平台 API 服务 |
| **文档版本** | v1.0.0 |
| **最后更新** | 2025-01-15 |
| **服务端口** | 8055 (开发环境) |
| **API版本** | v1, v2 (部分接口) |

## 📊 接口统计

### 总体统计
- **路由模块总数**: 28个
- **API接口总数**: 270+个
- **支持HTTP方法**: GET, POST, PUT, DELETE

### 按安全级别分类
| 分类 | 路由数 | 接口数 | 认证要求 | 说明 |
|------|--------|--------|----------|------|
| **公开接口** | 2 | 2 | 无 | 无需认证的公开接口 |
| **内部接口** | 1 | 3 | 内部调用 | 服务间内部通信接口 |
| **认证接口** | 25 | 265+ | JWT认证 | 需要用户登录认证的接口 |

### 按功能模块分类
| 功能分类 | 模块数 | 接口数 | 主要功能 |
|----------|--------|--------|----------|
| **核心教学** | 8 | 98 | 题目、试卷、考试、知识树管理 |
| **用户管理** | 3 | 11 | 教师、学校、地区信息管理 |
| **资源管理** | 3 | 30 | 备课资源、文件、细目表管理 |
| **教学计划** | 1 | 14 | 教学计划和集体备课管理 |
| **分析统计** | 1 | 3 | 学习数据分析和考情分析 |
| **第三方集成** | 1 | 20+ | 外部平台对接和数据同步 |
| **系统功能** | 11 | 94+ | 健康检查、内部调用等 |

## 🏗️ 架构设计

### 三层安全架构
```
┌─────────────────────────────────────────────┐
│                 公开接口                     │
│     /v1/xkw/*     /v1/teacher/region        │
│           (无需认证)                         │
├─────────────────────────────────────────────┤
│                 内部接口                     │
│              /v1/internal/*                 │
│          (服务间内部调用)                    │
├─────────────────────────────────────────────┤
│                 认证接口                     │
│     JWT认证中间件 (unify_sid cookie)        │
│              /v1/*, /v2/*                   │
│            (需要用户认证)                    │
└─────────────────────────────────────────────┘
```

### 请求处理流程
1. **路由分发**: 根据路径前缀分发到不同处理器
2. **认证验证**: 非公开接口验证JWT token
3. **请求处理**: 统一的handler中间件处理
4. **参数验证**: 使用Joi进行参数校验
5. **业务逻辑**: 调用service层处理业务
6. **数据访问**: 通过model层访问数据库
7. **响应返回**: 统一的响应格式

## 📚 接口目录导航

### 🌍 公开接口 (无需认证)
- [学科网公开接口](./apis/xkw-public.md) - 学科网平台公开数据接口
- [教师公开接口](./apis/teacher-public.md) - 教师相关公开功能接口

### 🔒 内部接口 (内部调用)
- [内部调用接口](./apis/internal.md) - 服务间内部通信接口

### 🛡️ 认证接口 (需要JWT认证)

#### 核心教学模块
- [健康检查](./apis/health.md) - 服务健康状态检查
- [教材管理](./apis/book.md) - 教材信息和章节管理 (3个接口)
- [题目管理](./apis/question.md) - 试题搜索、详情和编辑 (8个接口)
- [知识树管理](./apis/knowledge-tree.md) - 知识体系和知识点管理 (2个接口)
- [试题篮管理](./apis/basket.md) - 试题收藏和批量操作 (5个接口)
- [试卷管理](./apis/paper.md) - 试卷搜索、详情和下载 (6个接口)
- [用户试卷管理](./apis/user-paper.md) - 个人试卷创建和管理 (11个接口)
- [考试管理](./apis/exam.md) - 考试组织和数据分析 (12个接口)
- [考试管理V2](./apis/exam-v2.md) - 新版考试管理接口

#### 用户管理模块
- [教师管理](./apis/teacher.md) - 教师信息和班级管理 (6个接口)
- [学校管理](./apis/school.md) - 学校组织和资源管理 (4个接口)
- [地区管理](./apis/region.md) - 地区信息查询

#### 资源管理模块
- [备课相关接口](./apis/prep.md) - 备课资源、备考篮、目录和用户资源管理 (15个接口)
- [教育助手文件](./apis/edu-assistant-files.md) - 课件、教案、学案文件管理 (4个接口)
- [双向细目表](./apis/tw-specifications.md) - 考试双向细目表管理 (11个接口)
- [智能组卷](./apis/exampaper.md) - 智能组卷和试题推荐管理 (6个接口)

#### 教学计划模块
- [教学计划管理](./apis/edu-plan.md) - 教学计划制定和集体备课 (14个接口)

#### 分析统计模块
- [学习分析](./apis/learning-analysis.md) - 考情分析和智能组卷 (3个接口)

#### 第三方集成模块
- [第三方集成](./apis/integrations.md) - 外部平台集成接口 (17个接口)

## 🔧 通用规范

### 认证方式
```bash
# 所有认证接口需要在Cookie中携带JWT token
Cookie: unify_sid=<JWT_TOKEN>
```

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)

### 响应格式
```json
{
  "code": 0,           // 状态码：0-成功，非0-失败
  "msg": "成功",        // 响应消息
  "data": {}           // 响应数据，失败时为null
}
```

### 通用错误码
| 错误码 | HTTP状态码 | 说明 | 处理建议 |
|--------|------------|------|----------|
| 0 | 200 | 成功 | - |
| 400 | 400 | 参数错误 | 检查请求参数格式和必填项 |
| 401 | 401 | 未授权 | 检查登录状态和token有效性 |
| 403 | 403 | 权限不足 | 确认用户权限或联系管理员 |
| 404 | 404 | 资源不存在 | 确认资源ID或路径正确性 |
| 500 | 500 | 服务器错误 | 联系技术支持 |
| 1000 | 200 | 业务错误 | 查看msg字段获取具体错误信息 |

### 分页参数
```json
{
  "page": 1,           // 页码，从1开始
  "limit": 20,         // 每页数量，默认20，最大100
  "total": 150         // 总记录数（仅响应中包含）
}
```

### 排序参数
```json
{
  "sort_by": "created_at",    // 排序字段
  "order": "desc"             // 排序方向：asc升序，desc降序
}
```

## 🔍 快速开始

### 1. 获取认证Token
```bash
# 通过登录接口获取token（具体登录接口请参考认证文档）
curl -X POST "http://localhost:8055/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "teacher001", "password": "password123"}'
```

### 2. 调用API接口
```bash
# 使用获取的token调用API
curl -X GET "http://localhost:8055/v1/teacher/info" \
  -H "Cookie: unify_sid=<JWT_TOKEN>"
```

### 3. 处理响应
```javascript
// 成功响应处理
if (response.code === 0) {
  console.log('请求成功:', response.data);
} else {
  console.error('请求失败:', response.msg);
}
```

## 🔄 版本更新日志

### v1.1.0 (2025-01-15)
- ✅ 新增双向细目表管理接口文档
- ✅ 新增学习分析和考情分析接口文档
- ✅ 新增教育助手文件管理接口文档
- ✅ 新增教学计划和集体备课接口文档
- ✅ 整合第三方集成接口文档
- ✅ 完善备课相关接口文档
- ✅ 配置静态文件服务支持域名+路径访问

### v1.0.0 (2025-01-15)
- ✅ 初始版本发布
- ✅ 完善核心教学模块API文档（试卷、考试、题目等）
- ✅ 完善用户管理模块API文档（教师、学校、地区）
- ✅ 统一接口规范和错误处理
- ✅ 新增详细的请求示例和响应示例
- ✅ 完善认证和权限说明

### 计划更新
- 🔲 增加API测试工具和Postman集合
- 🔲 添加SDK和代码示例
- 🔲 完善错误码说明和处理建议
- 🔲 增加接口性能和限流说明

## 📞 技术支持

- **开发团队**: OpenTiku开发团队
- **文档维护**: API文档团队
- **技术咨询**: 请通过内部技术支持渠道联系

---

> 💡 **提示**: 本文档基于src/routers/index.js自动生成，如有疑问请参考源代码或联系开发团队。