const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const client = require('../../../common/client');
const BussError = require('../../../common/exceptions/BussError');

module.exports = {
    getUser,
    hasAuth,
    addInsertBaseInfo,
    addUpdateBaseInfo,
    handlerData,
    checkBookChaptersNotExists,
}

async function getUser(reqUser) {
    if (!reqUser) return null;
    let result = await db.jzl_collection(schema.users_permissions_user).findOne({yjUserId: Number(reqUser.id).toString()});
    if (!_.isEmpty(result)) {
        result.save_branch_id = result.pBranch;
        result.save_user_id = result._id;
    } else {
        const response = await client.jiaoyan.unifyTokenLogin(reqUser.unify_sid);
        result = response.user;
        result.save_branch_id = new ObjectId(result.pBranch.id);
        result.save_user_id = new ObjectId(result.id);
    }
    return result;
    // const teacher = await client.yj.getUserInfo(userId);
    // if (_.isEmpty(teacher)) return teacher;
    // let branch = await db.jzl_collection(schema.users_permissions_branch).findOne({yjSchoolId: teacher.schoolId});
    // if (_.isEmpty(branch)) {
    //     branch = {
    //         periods: [],
    //         subjects: [],
    //         name: teacher.schoolName,
    //         shortName: teacher.schoolName,
    //         type: teacher.schoolId.toString(),
    //         yjSchoolId: teacher.schoolId,
    //         createdAt: new Date(),
    //         updatedAt: new Date(),
    //         '__v': 0,
    //     };
    //     const res = await db.jzl_collection(schema.users_permissions_branch).insertOne(branch);
    //     branch._id = res.insertedId;
    // }
    // user = {
    //     yjUserId: userId,
    //     username: teacher.userName,
    //     confirmed: true,
    //     blocked: false,
    //     loginCodeTryCount: 0,
    //     loginPasswordTryCount: 0,
    //     // role: roles[0],
    //     // roles: roles,
    //     pBranch: branch._id,
    //     pBranches: [],
    //     thirdParties: [],
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //     '__v': 0,
    // };
    // const res = await db.jzl_collection(schema.users_permissions_user).insertOne(user);
    // user._id = res.insertedId;
    // return user;
}

function hasAuth(jzlUser, data) {
    if (_.isEmpty(data)) return false;
    if (data.deleted === enums.BOOL.YES) return false;
    return true;
}

function addInsertBaseInfo(jzlUser, data) {
    data.pBranch = jzlUser.save_branch_id;
    data.creator = jzlUser.save_user_id;
    data.operator = jzlUser.save_user_id;
    data.createdAt = new Date();
    data.updatedAt = new Date();
    data.deleted = enums.BOOL.NO;
    data['__v'] = 0;
}

function addUpdateBaseInfo(jzlUser, data) {
    data.operator = jzlUser._id;
    data.updatedAt = new Date();
}

function handlerData(data) {
    if (_.isEmpty(data)) return;
    data.id = data._id.toString();
    delete data._id;
    delete data.pBranch;
    delete data.creator;
    delete data.operator;
    delete data.deleted;
    delete data['__v'];
}

/**
 * 查找不存在的章节
 * @param ids 未匹配到的id
 * @return {Promise<[]>}
 */
async function checkBookChaptersNotExists(ids) {
    const result = [];
    if (!_.size(ids)) return result;
    const list = await db.jzl_collection(schema.jzl.book_chapter).find({_id: {$in: ids.map(e => new ObjectId(e.toString()))}}).toArray();
    if (!_.size(list)) return result;
    for (const id of ids) {
        const data = list.find(e => e._id.toString() === id);
        if (_.isEmpty(data)) result.push(id);
    }
    return result;
}
