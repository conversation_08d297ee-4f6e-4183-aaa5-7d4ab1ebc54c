# 校本教辅管理 API

## 接口概述

校本教辅管理模块提供创建、查询、更新和删除校本教辅资源的功能。

**Base URL**: 
- 测试环境: `https://devopen-tiku-serv-wan.yunxiao.com`
- 灰度环境: `https://grayopen-tiku-serv-wan.yunxiao.com`
- 正式环境: `https://open-tiku-serv-wan.yunxiao.com`

**认证要求**: 所有接口都需要 JWT 认证

## 接口列表

### 1. 创建教辅

**接口地址**: `POST /v1/textbook`

**请求参数**:
```json
{
  "name": "数学练习册",
  "description": "七年级上册数学练习册",
  "grade": "七年级",
  "period": "初中",
  "subject": "数学",
  "cover_url": "https://example.com/cover.jpg",
  "cover_file": "cover_file_id",
  "disk_file": "disk_file_id",
  "remark": "适用于课后练习"
}
```

**参数说明**:
- `name` (string, 必填): 教辅名称，最大长度200字符
- `description` (string, 可选): 教辅描述
- `grade` (string, 必填): 年级
- `period` (string, 必填): 学段，可选值: 小学、初中、高中
- `subject` (string, 必填): 科目，可选值: 语文、数学、英语、物理、化学、生物、地理、历史、政治、道德与法治、科学、信息技术、音乐、美术、体育
- `cover_url` (string, 可选): 封面URL
- `cover_file` (string, 可选): 封面文件ID
- `disk_file` (string, 可选): 网盘文件ID
- `remark` (string, 可选): 备注

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "name": "数学练习册",
    "description": "七年级上册数学练习册",
    "grade": "七年级",
    "period": "初中",
    "subject": "数学",
    "cover_url": "https://example.com/cover.jpg",
    "cover_file": "cover_file_id",
    "disk_file": "disk_file_id",
      "view_times": 0,
    "download_times": 0,
    "remark": "适用于课后练习",
    "user_id": "teacher123",
    "school_id": 1001,
    "deleted": 0,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Shell 示例**:
```bash
curl -X POST "https://devopen-tiku-serv-wan.yunxiao.com/v1/textbook" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "数学练习册",
    "description": "七年级上册数学练习册",
    "grade": "七年级",
    "period": "初中",
    "subject": "数学",
  }'
```

### 2. 获取教辅列表

**接口地址**: `GET /v1/textbook`

**查询参数**:
- `offset` (number, 可选): 偏移量，默认0
- `limit` (number, 可选): 每页数量，默认20
- `period` (string, 可选): 学段筛选
- `grade` (string, 可选): 年级筛选
- `subject` (string, 可选): 科目筛选
- `keyword` (string, 可选): 关键词搜索
- `my_only` (boolean, 可选): 只显示我创建的教辅，默认false

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "name": "数学练习册",
        "description": "七年级上册数学练习册",
        "grade": "七年级",
        "period": "初中",
        "subject": "数学",
        "cover_url": "https://example.com/cover.jpg",
              "view_times": 15,
        "download_times": 8,
        "user_id": "teacher123",
        "school_id": 1001,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "offset": 0,
    "limit": 20
  }
}
```

**Shell 示例**:
```bash
curl -X GET "https://devopen-tiku-serv-wan.yunxiao.com/v1/textbook?offset=0&limit=20&period=初中&subject=数学&my_only=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 获取教辅详情

**接口地址**: `GET /v1/textbook/{id}`

**路径参数**:
- `id` (string, 必填): 教辅ID

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "name": "数学练习册",
    "description": "七年级上册数学练习册",
    "grade": "七年级",
    "period": "初中",
    "subject": "数学",
    "cover_url": "https://example.com/cover.jpg",
    "cover_file": "cover_file_id",
    "disk_file": "disk_file_id",
      "view_times": 16,
    "download_times": 8,
    "remark": "适用于课后练习",
    "user_id": "teacher123",
    "school_id": 1001,
    "deleted": 0,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Shell 示例**:
```bash
curl -X GET "https://devopen-tiku-serv-wan.yunxiao.com/v1/textbook/64a1b2c3d4e5f6789012345" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 更新教辅

**接口地址**: `PUT /v1/textbook/{id}`

**路径参数**:
- `id` (string, 必填): 教辅ID

**请求参数**:
```json
{
  "name": "数学练习册（修订版）",
  "description": "七年级上册数学练习册修订版",
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "_id": "64a1b2c3d4e5f6789012345",
    "name": "数学练习册（修订版）",
    "description": "七年级上册数学练习册修订版",
    "grade": "七年级",
    "period": "初中",
    "subject": "数学",
    "view_times": 16,
    "download_times": 8,
    "user_id": "teacher123",
    "school_id": 1001,
    "deleted": 0,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-02T00:00:00.000Z"
  }
}
```

**Shell 示例**:
```bash
curl -X PUT "https://devopen-tiku-serv-wan.yunxiao.com/v1/textbook/64a1b2c3d4e5f6789012345" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "数学练习册（修订版）",
    "description": "七年级上册数学练习册修订版",
    }'
```

### 5. 删除教辅

**接口地址**: `DELETE /v1/textbook/{id}`

**路径参数**:
- `id` (string, 必填): 教辅ID

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": true
}
```

**Shell 示例**:
```bash
curl -X DELETE "https://devopen-tiku-serv-wan.yunxiao.com/v1/textbook/64a1b2c3d4e5f6789012345" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 错误码说明

- `0`: 成功
- `1`: 业务错误（具体错误信息在 message 字段）
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用说明

1. 所有接口都需要在请求头中携带有效的 JWT Token
2. 创建教辅时需要先上传文件到网盘文件接口，获取 disk_file ID
3. 查询列表时会自动过滤当前用户学校的数据
4. 查看教辅详情时会自动增加浏览次数
5. 删除操作为软删除，不会物理删除数据