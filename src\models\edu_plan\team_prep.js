const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const moment = require('moment');
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const utils = require('../../../common/utils/utils');
const client = require('../../../common/client');
const paper_utils = require('../../../common/utils/paper_utils');
const BussError = require('../../../common/exceptions/BussError');
const jzl = require('../jzl/index');
const school_model = require('../../models/school');
const questionModel = require('../question');
const zykModel = require('../zyk');

module.exports = {
    getList,
    getDetail,
    save,
    updateStatus,
    deleteById,
    getLessonResource,
    saveLessonResource
}

async function getList(params, user) {
    const schoolYearInfo = utils.getSemesterAndYear();
    // if (!params.from_year) params.from_year = schoolYearInfo.from_year;
    if (!params.to_year) params.to_year = schoolYearInfo.to_year;
    if (!params.semester) params.semester = schoolYearInfo.semester;
    const user_id = Number(user.id).toString();
    const query = {
        period: params.period,
        subject: params.subject,
        // from_year: params.from_year,
        to_year: params.to_year,
        semester: params.semester,
        school_id: user.school_id,
        deleted: enums.BOOL.NO,
        // $or: [
        //     { main_teacher_id: user_id },  // 不等于 main_teacher_id
        //     { teachers: user_id }  // 不在 teachers 数组里
        // ]
    };
    if (params.name) query.name = { $regex: params.name };
    if (params.status) query.status = params.status;
    if (params.grade) query.grade = params.grade;

    const result = {
        total: 0,
        list: []
    };
    // result.total = await db.jzl_collection(schema.team_prep).find(query).count();
    // if (!result.total) return result;
    let list = await db.jzl_collection(schema.team_prep).find(query).sort({createdAt: -1}).toArray();
    list = list.filter(e => e.main_teacher_id === user_id || (e.status !== enums.TeamPrepStatus.INIT && e.teachers.includes(user_id)));
    result.total = _.size(list);
    if (!result.total) return result;
    const pages = _.chunk(list, params.limit);
    result.list = pages[Math.floor(params.offset / params.limit)];
    for (const data of result.list) {
        jzl.handlerData(data);
        data.overtime = enums.BOOL.NO;
        if (data.status !== enums.TeamPrepStatus.DONE
            && moment().isAfter(moment(data.end_time).add(1, 'day'))) data.overtime = enums.BOOL.YES;
        // result.list.push(data);
    }
    return result;
}

async function getDetail(params, user) {
    const result = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(params.id)});
    if (!_.isEmpty(result)) {
        let teachers = await school_model.getTeachers({school_id: user.school_id});
        if (_.size(teachers)) {
            result.teachers = teachers.filter(e => result.teachers.includes(e.id)); // 参备
            result.main_teacher = teachers.find(e => e.id === result.main_teacher_id); // 主备
        }
        //
        const lesson_data = await db.jzl_collection(schema.team_prep_data).find({
            team_prep_id: params.id,
            deleted: enums.BOOL.NO
        }).toArray();
        for (const week of result.weeks) {
            const chapters = utils.flattenTree(week.children);
            for (const chapter of chapters) {
                if (chapter.key !== enums.EduPlanChapterKey.LESSON) continue;
                chapter.items_status = {};
                const list = lesson_data.filter(e => e.edu_plan_chapter_id === chapter.id);
                for (const item of chapter.items || []) {
                    chapter.items_status[item] = list.filter(e => e.key === item && _.size(e.children)).length;
                }
            }
        }
    }
    result.overtime = enums.BOOL.NO;
    if (result.status !== enums.TeamPrepStatus.DONE
        && moment().isAfter(moment(result.end_time).add(1, 'day'))) result.overtime = enums.BOOL.YES;
    jzl.handlerData(result);
    return result;
}
async function save(params, user) {
    let {id, edu_plan_id} = params;
    const edu_plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(edu_plan_id)});
    if (_.isEmpty(edu_plan)) throw new BussError('教学计划不存在')
    const jzlUser = await jzl.getUser(user);
    if (params.end_time) params.end_time = moment(params.end_time).startOf('day').toDate();
    if (id) {
        delete params.id;
        const data = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(id)});
        if (_.isEmpty(data)) throw new BussError('集备不存在');
        if (data.main_teacher_id !== Number(user.id).toString()
            || data.status === enums.TeamPrepStatus.DONE
        ) throw new BussError('不可编辑')
        if (data.edu_plan_id !== edu_plan_id) {
            syncEduPlanInfo(edu_plan, params);
        }
        jzl.addUpdateBaseInfo(jzlUser, params);
        const chapters = getAllChildren(params);
        const oldChapters = getAllChildren(data);
        const delIds = [];
        for (const chapter of oldChapters) {
            const data = chapters.find(e => e.id === chapter.id);
            if (chapter.key === enums.EduPlanChapterKey.LESSON && _.isEmpty(data)) delIds.push(chapter.id);
        }
        if (_.size(delIds)) {
            await db.jzl_collection(schema.team_prep_data).deleteMany({edu_plan_chapter_id: {$in: delIds}});
        }
        await db.jzl_collection(schema.team_prep).updateOne({_id: new ObjectId(id)}, {$set: params});
    } else {
        syncEduPlanInfo(edu_plan, params);
        params.school_id = user.school_id;
        params.main_teacher_id = Number(user.id).toString();
        jzl.addInsertBaseInfo(jzlUser, params);
        const insert = await db.jzl_collection(schema.team_prep).insertOne(params);
        id = insert.insertedId.toString();
    }
    if (params.status === enums.TeamPrepStatus.DOING) {
        await syncEduPlanData(user, id, params);
    }
    // 同步状态
    await syncDataStatus(id, params.status);
    return {id};
}

function getAllChildren(data) {
    const result = [];
    for (const week of data.weeks) {
        result.push(...(utils.flattenTree(week.children || [])));
    }
    return result;
}

// 同步
async function syncEduPlanData(user, team_prep_id, team_prep) {
    if (team_prep.status !== enums.TeamPrepStatus.DOING) return;
    const team_prep_lessons = getAllChildren(team_prep).filter(e => e.key === enums.EduPlanChapterKey.LESSON);
    const lesson_data = await db.jzl_collection(schema.team_prep_data).find({
        edu_plan_chapter_id: {$in: team_prep_lessons.map(e => e.id)},
        status: enums.TeamPrepStatus.DONE,
        deleted: enums.BOOL.NO
    }).sort({updatedAt: -1}).toArray();
    const array = [];
    const jzlUser = await jzl.getUser(user);
    for (const lesson of team_prep_lessons) {
        if (!_.size(lesson.items)) continue;
        for (const item of lesson.items) {
            const curr_lesson = lesson_data.find(e => e.edu_plan_chapter_id === lesson.id && e.key === item && _.size(e.children));
            if (_.isEmpty(curr_lesson)) continue;
            const clone_data = _.pick(curr_lesson, ['edu_plan_chapter_id', 'key', 'children']);
            if (_.size(clone_data.children)) {
                clone_data.children = clone_data.children.filter((item, index, self) =>
                    index === self.findIndex( t => t.id === item.id && t.source_id === item.source_id)
                );
            }
            clone_data.team_prep_id = team_prep_id;
            clone_data.status = team_prep.status;
            jzl.addInsertBaseInfo(jzlUser, clone_data);
            array.push(clone_data);
        }
    }
    if (_.size(array)) {
        await db.jzl_collection(schema.team_prep_data).insertMany(array);
    }
}

function syncEduPlanInfo(edu_plan, data) {
    if (_.isEmpty(data)) return;
    data.period = edu_plan.period;
    data.subject = edu_plan.subject;
    data.grade = edu_plan.grade;
    data.from_year = edu_plan.from_year;
    data.to_year = edu_plan.to_year;
    data.semester = edu_plan.semester;
}

async function updateStatus(params, user) {
    const {id} = params;
    const data = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(data)
        || data.deleted === enums.BOOL.YES
        || data.status === enums.TeamPrepStatus.DONE
    ) throw new BussError('非法操作');
    const jzlUser = await jzl.getUser(user);
    const doc = {status: params.status};
    jzl.addUpdateBaseInfo(jzlUser, doc);
    await db.jzl_collection(schema.team_prep).updateOne({_id: new ObjectId(id)}, {$set: doc});
    if (params.status === enums.TeamPrepStatus.DOING) {
        data.status = params.status;
        await syncEduPlanData(user, id, data);
    }
    // 同步状态
    await syncDataStatus(id, params.status);
}

async function syncDataStatus(id, status) {
    await db.jzl_collection(schema.team_prep_data).updateMany({team_prep_id: id}, {$set: {status: status}});
}

async function deleteById(params, user) {
    const {id} = params;
    const data = await db.jzl_collection(schema.team_prep).find({_id: new ObjectId(id)});
    if (_.isEmpty(data)
        || data.deleted === enums.BOOL.YES
    ) throw new BussError('非法操作');
    const jzlUser = await jzl.getUser(user);
    const doc = {deleted: enums.BOOL.YES};
    jzl.addUpdateBaseInfo(jzlUser, doc);
    await db.jzl_collection(schema.team_prep).updateOne({_id: new ObjectId(id)}, {$set: doc});
    // 删除关联资源 - 不删除
    // await db.jzl_collection(schema.team_prep_data).updateMany({team_prep_id: id}, {$set: doc});
}

async function getLessonResource(params, user) {
    const {id, lesson_id, key} = params;
    const data = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(data)
        || data.deleted === enums.BOOL.YES
    ) throw new BussError('集备不存在');
    const data_resource = await db.jzl_collection(schema.team_prep_data).findOne({
        team_prep_id: id,
        edu_plan_chapter_id: lesson_id,
        key: key,
        deleted: enums.BOOL.NO
    });
    const result = {
        total: 0,
        list: []
    };
    if (_.isEmpty(data_resource) || !_.size(data_resource.children)) return result;
    // 目前只有左右，返回试卷结构
    // 插入试题
    const kb_ids = [];
    const zx_ids = [];
    const zyk_ids = [];
    const ques_map = {};
    for (const d of data_resource.children) {
        if (d.source === enums.QuestionSource.ZX) {
            zx_ids.push(d.id);
        } else if (d.source === enums.QuestionSource.ZYK) {
            zyk_ids.push(d.id);
        } else {
            if (_.isNumber(d.id)) kb_ids.push(d.id);
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestionByIds(kb_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const questions = await questionModel.getByIds(zx_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zyk_ids)) {
        const questions = await zykModel.getQuestionByIds(zyk_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    const lesson_node = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(lesson_id)});
    const basket = paper_utils.init(user.id);
    basket.name = `第${utils.numberToChinese(lesson_node.index || 1)}课时 ${lesson_node.name}作业`;
    basket.period = data.period;
    basket.subject = data.subject;
    for (const q of data_resource.children) {
        let question = ques_map[q.id];
        if (_.isEmpty(question)) continue;
        const ques = _.assign({}, question);
        ques.source = q.source;
        ques.source_id = q.source_id;
        if (q.score) ques.score = q.score;
        paper_utils.insert_questions(basket, ques);
    }
    paper_utils.traverse_questions(basket, ques_map);
    paper_utils.render_basket(basket);
    result.total = 1;
    result.list.push(basket);
    return result;
}

async function saveLessonResource(params, user) {
    const {id, lesson_id, key} = params;
    let { children } = params;
    const team_prep = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(team_prep)
        || team_prep.school_id !== user.school_id
        || team_prep.deleted === enums.BOOL.YES
    ) throw new BussError('集备不存在');
    if (team_prep.status !== enums.TeamPrepStatus.DOING) throw new BussError('不可编辑');
    const teacher_id = Number(user.id).toString();
    if (team_prep.main_teacher_id !== teacher_id && !team_prep.teachers.includes(teacher_id)) {
        throw new BussError('非主备或参备人员不可编辑');
    }
    for (const child of children) {
        if (!child.source_id) child.source_id = child.id;
    }
    if (_.size(children)) { // 过滤重复
        children = children.filter((item, index, self) =>
            index === self.findIndex( t => t.id === item.id && t.source_id === item.source_id)
        );
    }
    let data_resource = await db.jzl_collection(schema.team_prep_data).findOne({team_prep_id: id, edu_plan_chapter_id: lesson_id, key: key, deleted: enums.BOOL.NO});
    const jzlUser = await jzl.getUser(user);
    if (_.isEmpty(data_resource)) {
        if (_.size(children)) {
            data_resource = {
                team_prep_id: id,
                edu_plan_chapter_id: lesson_id,
                key: key,
                children: children,
                status: team_prep.status
            }
            jzl.addInsertBaseInfo(jzlUser, data_resource);
            const insert = await db.jzl_collection(schema.team_prep_data).insertOne(data_resource);
            data_resource._id = insert.insertedId;
        }
    } else {
        if (_.size(children)) {
            const doc = {
                children: children,
                status: team_prep.status
            };
            jzl.addUpdateBaseInfo(jzlUser, doc);
            await db.jzl_collection(schema.team_prep_data).updateOne({_id: data_resource._id}, {$set: doc});
        } else { // 删除空的集备数据
            await db.jzl_collection(schema.team_prep_data).deleteOne({_id: data_resource._id});
        }
    }
    // 返回资源详细
    return await getLessonResource(params, user);
    // return {id: data_resource._id.toString()};
}

