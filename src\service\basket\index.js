const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/basket');
const enums = require('../../../common/enums/enums');


module.exports = {
    get_questions,
    post_questions,
    delete_questions,
    get_basket,
    get_basket_internal,
    delete_basket,
}

const JOI_GET_QUESTIONS = Joi.object({
    // user_id: Joi.string().required(), // 用户ID
    category: Joi.string().optional().default(enums.BasketCategory.BASKET).valid(...Object.values(enums.BasketCategory)), // 试题篮类型
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
});

async function get_questions(req, res) {
    const params = await JOI_GET_QUESTIONS.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_questions(params);
}


const JOI_POST_QUESTIONS = Joi.object({
    // user_id: Joi.string().required(), // 用户ID
    category: Joi.string().optional().default(enums.BasketCategory.BASKET).valid(...Object.values(enums.BasketCategory)), // 试题篮类型
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    questions: Joi.array().items(Joi.object({
        id: Joi.alternatives().try(
            Joi.number(),
            Joi.string(),
        ).required(),
        type: Joi.string().required(),
        period: Joi.string().required(), //  学段
        subject: Joi.string().required(), // 科目
        source: Joi.string().optional(), // 来源
        source_id: Joi.alternatives().try(
            Joi.number(),
            Joi.string(),
        ).optional() // 来源ID
    }).unknown(true)).required().min(1),
});

async function post_questions(req, res) {
    const params = await JOI_POST_QUESTIONS.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.post_questions(params);
}


const JOI_DELETE_QUESTIONS = Joi.object({
    // user_id: Joi.string().required(), // 用户ID
    category: Joi.string().optional().default(enums.BasketCategory.BASKET).valid(...Object.values(enums.BasketCategory)), // 试题篮类型
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    questions: Joi.array().items(Joi.alternatives().try(
        Joi.number(),
        Joi.string(),
    )).required().min(1),
});

async function delete_questions(req, res) {
    const params = await JOI_DELETE_QUESTIONS.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.delete_questions(params);
}

const JOI_GET_BASKET = Joi.object({
    user_id: Joi.string(), // 用户ID
    category: Joi.string().optional().default(enums.BasketCategory.BASKET).valid(...Object.values(enums.BasketCategory)), // 试题篮类型
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    paper_id: [Joi.string().optional(), Joi.number().optional()], // 试卷ID
    op: Joi.string().valid('delete', 'merge').optional().default('delete'),
    source_type: Joi.string().valid(...Object.values(enums.PaperSourceType)).optional().default(enums.PaperSourceType.SYS),
    template: Joi.string().valid('standard', 'exam', 'homework').optional()
});

async function get_basket(req, res) {
    const params = await JOI_GET_BASKET.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_basket(params);
}

async function get_basket_internal(req, res) {
    const params = await JOI_GET_BASKET.validateAsync(req.query);
    return await model.get_basket(params);
}

const JOI_DELETE_BASKET = Joi.object({
    // user_id: Joi.string().required(), // 用户ID
    category: Joi.string().optional().default(enums.BasketCategory.BASKET).valid(...Object.values(enums.BasketCategory)), // 试题篮类型
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
});

async function delete_basket(req, res) {
    const params = await JOI_DELETE_BASKET.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.delete_basket(params);
}

