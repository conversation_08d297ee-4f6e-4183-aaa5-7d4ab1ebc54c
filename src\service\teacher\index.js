const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/teacher');

module.exports = {
    getClasses,
    getClassStudents,
    getUserInfo,
    getHfsTeacherInfo,
    getYjTeacherInfo,
    updateUser<PERSON>roperty,
    getIpRegion,
    getProfile,
}

async function getClasses(req, res) {
    const params = {
        user_id: req.user.id
    };
    return await model.getClasses(params);
}

const JOI_CLASS_STUDENTS = Joi.object({
    class_id: Joi.string().required(),
});

async function getClassStudents(req, res) {
    const params = await JOI_CLASS_STUDENTS.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getClassStudents(params);
}


async function getUserInfo(req, res) {
    const params = {user_id: +req.user.id};
    return await model.getUserInfo(params);
}

async function getHfsTeacherInfo(req, res) {
    const params = {unify_sid: req.cookies['unify_sid']};
    return await model.getHfsTeacherInfo(params);
}
async function getYjTeacherInfo(req, res) {
    const params = {user_id: +req.user.id};
    return await model.getYjTeacherInfo(params);
}

const JOI_UPDATE_USER_PROPERTY = Joi.array().items(Joi.object({
    key: Joi.string().valid('period', 'subject').required(),
    value: Joi.any()
})).required().min(1);

async function updateUserProperty(req, res) {
    const items = await JOI_UPDATE_USER_PROPERTY.validateAsync(req.body);
    const params = {
        items,
        user_id: req.user.id
    }
    await model.updateUserProperty(params);
}

async function getIpRegion(req, res) {
    return await model.getIpRegion(req);
}

const JOI_GET_PROFILE = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
});

async function getProfile(req, res) {
    const params = await JOI_GET_PROFILE.validateAsync(req.query);
    return await model.getProfile(params, req.user);
}
