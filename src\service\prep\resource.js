const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/prep/resource');
const enums = require('../../../common/enums/enums');

module.exports = {
    getCatalogList,
    getResourceList,
    getResourceDetail,
    downloadResource,
}

const JOI_GET_CATALOG_LIST = Joi.object({
    book_id: Joi.number().required(), // 用户ID
    chapter_id: Joi.number().required(), //  学段
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required()
});

async function getCatalogList(req, res) {
    const params = await JOI_GET_CATALOG_LIST.validateAsync(req.query);
    return await model.getCatalogList(params);
}

const JOI_GET_RESOURCE_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    book_id: Joi.number().optional(), // 教材ID
    chapter_id: Joi.number().optional(), // 章节ID
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).optional(),
    catalog_id: Joi.string().optional().allow(''),
    sort_by: Joi.string().optional().default('utime')
});

async function getResourceList(req, res) {
    const params = await JOI_GET_RESOURCE_LIST.validateAsync(req.query);
    return await model.getResourceList(params);
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.string().required(),
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    from: Joi.number().required(), // 来源
});
async function getResourceDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getResourceDetail(params);
}

const JOI_DOWNLOAD = Joi.object({
    id: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    type: Joi.alternatives().try( // 类型
        Joi.number(),
        Joi.string()
    ).required(),
    from: Joi.number().required(), // 来源
    content: Joi.string().optional().allow(''), // 内容
});

async function downloadResource(req, res) {
    const params = await JOI_DOWNLOAD.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.downloadResource(req, res, params);
}
