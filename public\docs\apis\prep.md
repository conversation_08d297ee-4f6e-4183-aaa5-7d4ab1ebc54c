---
title: 备课相关接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 备课相关接口

备课相关接口提供完整的备课功能，包括资源管理、目录管理、篮子管理和用户备课资源管理，支持教师进行个性化备课。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

# 备课资源管理

## GET 获取目录列表

GET /v1/prep/catalog

获取备课目录列表，支持按教材、章节和类型筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|book_id|query|number|是|教材ID|
|chapter_id|query|number|是|章节ID|
|type|query|number/string|是|资源类型|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/prep/catalog?book_id=1001&chapter_id=2001&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "catalog_001",
      "name": "课件资源"
    },
    {
      "id": "catalog_002",
      "name": "教案资源"
    },
    {
      "id": "catalog_003",
      "name": "习题资源"
    }
  ]
}
```

---

## GET 获取资源列表

GET /v1/prep/resource/list

获取备课资源列表，支持分页和多维度筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|book_id|query|number|否|教材ID|
|chapter_id|query|number|否|章节ID|
|type|query|number/string|否|资源类型|
|catalog_id|query|string|否|目录ID|
|sort_by|query|string|否|排序字段，默认utime|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/prep/resource/list?offset=0&limit=20&book_id=1001&type=1&catalog_id=catalog_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 156,
    "list": [
      {
        "id": "resource_001",
        "name": "有理数概念课件",
        "type": 1,
        "catalog_id": "catalog_001",
        "book_id": 1001,
        "chapter_id": 2001,
        "file_size": 2048576,
        "download_count": 235,
        "view_count": 1024,
        "ctime": 1698825600000,
        "utime": 1698912000000
      }
    ]
  }
}
```

---

## GET 获取资源详情

GET /v1/prep/resource/info

获取指定资源的详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string|是|资源ID|
|type|query|number/string|是|资源类型|
|from|query|number|是|来源标识|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/prep/resource/info?id=resource_001&type=1&from=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "resource_001",
    "name": "有理数概念课件",
    "description": "有理数的基本概念和分类",
    "type": 1,
    "file_type": "pptx",
    "file_size": 2048576,
    "file_url": "https://example.com/resource/resource_001.pptx",
    "preview_url": "https://example.com/preview/resource_001.jpg",
    "author": "张老师",
    "tags": ["有理数", "概念", "初中数学"],
    "download_count": 235,
    "view_count": 1024,
    "ctime": 1698825600000,
    "utime": 1698912000000
  }
}
```

---

## POST 下载资源文件

POST /v1/prep/resource/download

下载指定的备课资源文件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|number/string|是|资源ID|
|type|body|number/string|是|资源类型|
|from|body|number|是|来源标识|
|content|body|string|否|内容|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/prep/resource/download" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "resource_001",
    "type": 1,
    "from": 1
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "download_url": "https://example.com/download/resource_001.pptx",
    "file_name": "有理数概念课件.pptx",
    "file_size": 2048576,
    "expire_time": 1698998400000
  }
}
```

---

# 备课篮子管理

## GET 获取篮子简要信息

GET /v1/prep/basket/resource

获取用户备课篮子中的元素基本信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|type|query|number/string|否|类型|
|type_id|query|string|否|类型ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/prep/basket/resource?period=初中&subject=数学&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "element_001",
      "name": "有理数课件"
    },
    {
      "id": "element_002", 
      "name": "有理数教案"
    }
  ]
}
```

---

## GET 获取篮子详细信息

GET /v1/prep/basket/resource/detail

获取用户备课篮子中元素的完整详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|type|query|number/string|否|类型|
|type_id|query|string|否|类型ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/prep/basket/resource/detail?period=初中&subject=数学&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "element_001",
      "name": "有理数课件",
      "type": 1,
      "file_type": "pptx",
      "file_size": 2048576,
      "author": "张老师",
      "ctime": 1698825600000,
      "utime": 1698912000000
    }
  ]
}
```

---

## POST 更新篮子内容

POST /v1/prep/basket/resource

创建或更新用户的备课篮子内容。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|type|body|number/string|是|类型|
|type_id|body|string|否|类型ID|
|children|body|array|否|子元素数组|
|» id|body|string|是|元素ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/prep/basket/resource" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "type": 1,
    "children": [
      {
        "id": "element_001"
      },
      {
        "id": "element_002"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "basket_001",
    "updated": true
  }
}
```

---

# 用户备课目录管理

## GET 获取用户备课目录

GET /v1/user/prep/catalog

获取用户自定义的备课目录结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|category|query|number|是|类别|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user/prep/catalog?period=初中&subject=数学&category=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "user_catalog_001",
      "name": "我的课件",
      "children": [
        {
          "id": "user_catalog_002",
          "name": "有理数",
          "children": []
        }
      ]
    },
    {
      "id": "user_catalog_003",
      "name": "我的教案",
      "children": []
    }
  ]
}
```

---

## POST 更新用户备课目录

POST /v1/user/prep/catalog

更新用户自定义的备课目录结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|category|body|number|是|类别|
|children|body|array|否|目录结构，默认为空数组|
|» id|body|string|否|目录ID，新建时自动生成|
|» name|body|string|是|目录名称|
|» children|body|any|否|子目录|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user/prep/catalog" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "category": 1,
    "children": [
      {
        "name": "我的课件",
        "children": [
          {
            "name": "有理数",
            "children": []
          }
        ]
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "user_catalog_001",
      "name": "我的课件",
      "children": [
        {
          "id": "user_catalog_002",
          "name": "有理数",
          "children": []
        }
      ]
    }
  ]
}
```

---

# 用户备课资源管理

## GET 获取资源列表

GET /v1/user/prep/resource/list

获取用户备课资源列表，支持目录树形查询。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|category|query|number|是|类别|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|分页限制|
|type|query|number/string|否|类型|
|type_id|query|string|否|类型ID|
|catalog_id|query|string|否|目录ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user/prep/resource/list?period=初中&subject=数学&category=1&offset=0&limit=20&catalog_id=user_catalog_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 25,
    "list": [
      {
        "id": "user_resource_001",
        "name": "我的有理数课件",
        "type": 1,
        "catalog_id": "user_catalog_001",
        "from": 1,
        "resource_id": "resource_001",
        "ctime": 1698825600000,
        "utime": 1698912000000
      }
    ]
  }
}
```

---

## GET 获取资源ID集合

GET /v1/user/prep/resource/keys

获取用户已添加的系统资源ID列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|query|number/string|否|类型|
|type_id|query|string|否|类型ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/user/prep/resource/keys?type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    "resource_001",
    "resource_002",
    "resource_003"
  ]
}
```

---

## PUT 修改资源基本信息

PUT /v1/user/prep/resource/base

批量更新用户备课资源的字段信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|是|资源ID|
|type|body|number/string|是|类型|
|fields|body|array|是|字段更新数组，至少1个|
|» name|body|string|是|字段名|
|» value|body|string|否|字段值|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/user/prep/resource/base" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "user_resource_001",
    "type": 1,
    "fields": [
      {
        "name": "catalog_id",
        "value": "user_catalog_002"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "updated": true
  }
}
```

---

## POST 编辑或添加自定义备课资源

POST /v1/user/prep/resource/custom

创建或更新用户自定义的备课资源。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|type|body|number/string|是|类型|
|type_id|body|string|否|类型ID|
|catalog_id|body|string|否|目录ID|
|id|body|string|否|资源ID，更新时使用|
|name|body|string|是|资源名称|
|children|body|array|是|子资源|
|» id|body|string|是|子资源ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user/prep/resource/custom" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "period": "初中",
    "subject": "数学",
    "type": 1,
    "catalog_id": "user_catalog_001",
    "name": "我的自定义课件",
    "children": [
      {
        "id": "element_001"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_resource_002",
    "created": true
  }
}
```

---

## POST 添加系统备课资源

POST /v1/user/prep/resource

将系统资源添加到用户备课资源中。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|body|number/string|否|类型|
|type_id|body|string|否|类型ID|
|catalog_id|body|string|否|目录ID|
|resource_id|body|number/string|是|资源ID|
|from|body|number|否|来源，默认为系统|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/user/prep/resource" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "type": 1,
    "catalog_id": "user_catalog_001",
    "resource_id": "resource_003"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "user_resource_003",
    "added": true
  }
}
```

---

## DELETE 删除资源

DELETE /v1/user/prep/resource

删除用户备课资源。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|string|是|资源ID|
|type|body|number/string|是|类型|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X DELETE "http://localhost:8055/v1/user/prep/resource" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "user_resource_001",
    "type": 1
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "deleted": true
  }
}
```

---

## 返回数据结构

### 资源信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|资源ID|唯一标识|
|name|string|true|none|资源名称|资源标题|
|type|number|true|none|资源类型|1-课件 2-教案 3-习题等|
|catalog_id|string|false|none|目录ID|所属目录|
|file_type|string|false|none|文件类型|如pptx、docx等|
|file_size|number|false|none|文件大小|字节数|
|file_url|string|false|none|文件地址|下载链接|
|author|string|false|none|作者|创建者|
|ctime|number|true|none|创建时间|时间戳|
|utime|number|true|none|更新时间|时间戳|

### 目录信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|目录ID|唯一标识|
|name|string|true|none|目录名称|目录标题|
|children|array|false|none|子目录|下级目录列表|

### 分页响应结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|number|true|none|总数量|符合条件的记录总数|
|list|array|true|none|数据列表|当前页数据|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|12|参数错误|请求参数格式错误或缺失必要参数|
|14|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 备课流程

1. **浏览资源**: 通过目录和条件筛选查找所需资源
2. **添加到篮子**: 将感兴趣的资源临时存放在篮子中
3. **组织目录**: 创建个人备课目录结构
4. **保存资源**: 将篮子中的资源保存到个人目录
5. **自定义资源**: 创建和编辑个人备课资源

### 数据来源

- **系统资源**: 来自资源库的标准备课资源
- **用户资源**: 用户创建的个性化备课资源
- **篮子数据**: 临时存储用户选择的资源
- **目录结构**: 用户自定义的分类组织方式

### 权限控制

- 所有接口需要用户认证
- 用户只能管理自己的备课数据
- 系统资源支持查看和引用，不支持修改
- 自定义资源支持完整的CRUD操作

### 性能优化

- 目录查询支持树形结构缓存
- 资源列表支持分页查询
- 篮子操作支持批量处理
- 文件下载支持CDN加速