/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('xkw_server');
const api_server = config.get('xkw_api_server');
const utils = require('../utils/utils');
const qs = require('querystring');

module.exports = {
    getAccessToken,
    getUserProfile,
    getDownloadPaperDetail,
    getKnowledgePoints,
    getCourses,
};

async function getAccessToken(params) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/oauth2/accessToken',
        port: server.port,
        search: qs.stringify(params)
    });
    // let opstions = { headers: {}, timeout: 5000 };
    try {
        const result = await axios.post(url);
        return result && result.data || null;
    } catch (e) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`, e);
    }
    return null;
}


async function getUserProfile(access_token) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/oauth2/profile',
        port: server.port,
        search: qs.stringify({
            access_token
        })
    });
    // let opstions = { headers: {}, timeout: 5000 };
    try {
        const result = await axios.post(url);
        if (result && result.data.error) {
            logger.error(`获取学科网用户信息失败,url:[${url}], error: ${result.data.error}` );
            return null;
        }
        return result.data || null;
    } catch (e) {
        logger.error(`获取学科网用户信息失败,url:[${url}]`, e);
    }
    return null;
}


/**
 * 获取下载试卷详细
 * wiki: https://open.xkw.com/document/sg44/71fc270eb0aafa95/get-user-down-load-paper-details-get
 * @param openId
 * @param paperId
 * @returns {Promise<void>}
 */
async function getDownloadPaperDetail(headers, query) {
    const options = {
        headers: headers
    };
    const url = URL.format({
        protocol: api_server.protocol,
        hostname: api_server.hostname,
        pathname: '/xopqbm/zj-saas/users/download-papers/details',
        port: api_server.port,
        search: qs.stringify(query)
    });
    try {
        const result = await axios.get(url, options);
        if (!result || result.data.code !== 2000000) {
            logger.error('学科网获取下载记录详情失败: ', JSON.stringify(result.data));
            return null;
        }
        return result.data.data;
    } catch (e) {
        logger.error('学科网获取下载记录详情异常: ', e);
        return null;
    }
}

/**
 * 批量获取知识点
 * wiki: https://open.xkw.com/document/sg10/746e8c07ced95ea9/get-by-ids-get
 * @param headers
 * @param query {object}
 * @returns {Promise<void>}
 */
async function getKnowledgePoints(headers, query) {
    const options = {
        headers: headers
    };
    const url = URL.format({
        protocol: api_server.protocol,
        hostname: api_server.hostname,
        pathname: '/xopqbm/knowledge-points',
        port: api_server.port,
        search: qs.stringify(query)
    });
    try {
        const result = await axios.get(url, options);
        if (!result || result.data.code !== 2000000) {
            logger.error('学科网获取下载记录详情失败: ', JSON.stringify(result.data));
            return null;
        }
        return result.data.data;
    } catch (e) {
        logger.error('学科网获取下载记录详情异常: ', e);
        return null;
    }
}

async function getCourses(headers, query) {
    const options = {
        headers: headers
    };
    const url = URL.format({
        protocol: api_server.protocol,
        hostname: api_server.hostname,
        pathname: '/xopqbm/courses',
        port: api_server.port,
        search: qs.stringify(query)
    });
    try {
        const result = await axios.get(url, options);
        if (!result || result.data.code !== 2000000) {
            logger.error('学科网获取下载记录详情失败: ', JSON.stringify(result.data));
            return null;
        }
        return result.data.data;
    } catch (e) {
        logger.error('学科网获取下载记录详情异常: ', e);
        return null;
    }
}



