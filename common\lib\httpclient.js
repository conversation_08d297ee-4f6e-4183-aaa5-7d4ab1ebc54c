const axios = require('axios');
// const logger = require('../lib/logger');

class HttpClient {
    constructor() {
        this.instance = axios.create({
            timeout: 10000
        });
    }

    async request(config) {
        try {
            const response = await this.instance.request(config);
            return response.data;
        } catch (error) {
            if (error.response) {
                logger.error(`Request to ${config.url} failed with status ${error.response.status}: ${error.response.data}`);
            } else if (error.request) {
                logger.error(`Request to ${config.url} was made but no response was received`);
            } else {
                logger.error(`<PERSON>rror setting up the request to ${config.url}: ${error.message}`);
            }
            return null;
        }
    }

    async get(url, config = {}) {
        return this.request({
            ...config,
            method: 'get',
            url
        });
    }

    async post(url, data = {}, config = {}) {
        return this.request({
            ...config,
            method: 'post',
            url,
            data
        });
    }

    async put(url, data = {}, config = {}) {
        return this.request({
            ...config,
            method: 'put',
            url,
            data
        });
    }

    async delete(url, config = {}) {
        return this.request({
            ...config,
            method: 'delete',
            url
        });
    }
}

module.exports = new HttpClient();

(async () => {
    const client = new HttpClient();
    const res = await client.get('http://www.baidu.com');
    console.log(res.data);
})();

