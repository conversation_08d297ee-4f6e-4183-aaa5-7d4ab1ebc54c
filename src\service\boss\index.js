const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/boss');

module.exports = {
    getSchoolAppUsageInfo,
    getSchoolAllAppUsageInfo,
}

const JOI_GET_SCHOOL_APP_USAGE_INFO = Joi.object({
    schoolId: Joi.string().required(),
    productCategory: Joi.string().required(),
});

async function getSchoolAppUsageInfo(req, res) {
    const params = await JOI_GET_SCHOOL_APP_USAGE_INFO.validateAsync(req.query);
    return await model.getSchoolAppUsageInfo(params);
}

async function getSchoolAllAppUsageInfo(req, res) {
    return await model.getSchoolAllAppUsageInfo(req.user);
}
