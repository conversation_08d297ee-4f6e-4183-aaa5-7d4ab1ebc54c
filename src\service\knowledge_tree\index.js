const Joi = require('@hapi/joi');
const _ = require('lodash');
const tree = require('../../models/knowledge_tree');


module.exports = {
    getAll,
    getDetail,
}

const JOI_GET_ALL = Joi.object({
    period: Joi.string().optional(),
    subject: Joi.string().optional()
});

async function getAll(req, res) {
    const params = await JOI_GET_ALL.validateAsync(req.query);
    return await tree.getAll(params);
}

const JOI_GET_DETAIL = Joi.object({
    knowledge_tree_id: Joi.number().required(),
});


async function getDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.params);
    return await tree.getDetail(params);
}

