---
title: 第三方集成接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 第三方集成接口

第三方集成接口提供与外部教育平台和服务的集成功能，包括小语种平台、错题本、学科网、知识库、管理系统等多个教育相关服务的API接口。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

# XYZ小语种平台

## GET 获取学段信息

GET /v1/xyz/study_info

获取小语种平台的学段信息列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xyz/study_info" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "period": "高中",
      "subjects": ["英语", "日语", "法语", "德语", "西班牙语", "俄语"]
    }
  ]
}
```

---

## GET 获取教材目录信息

GET /v1/xyz/books/catalog/info

获取小语种教材及版本信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|否|学段|
|subject|query|string|否|科目|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xyz/books/catalog/info?period=高中&subject=日语" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": 1001,
      "name": "日语必修第一册",
      "period": "高中",
      "subject": "日语",
      "publisher": "人民教育出版社",
      "version": "2019版"
    }
  ]
}
```

---

## GET 获取教材详情

GET /v1/xyz/books/{book_id}

获取指定教材的详细信息，包括章节结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|book_id|path|number|是|教材ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xyz/books/1001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 1001,
    "name": "日语必修第一册",
    "chapters": [
      {
        "id": 2001,
        "name": "第一课 はじめまして",
        "order": 1,
        "children": [
          {
            "id": 3001,
            "name": "词汇",
            "order": 1
          },
          {
            "id": 3002,
            "name": "语法",
            "order": 2
          }
        ]
      }
    ]
  }
}
```

---

## GET 获取备课资源列表

GET /v1/xyz/prep/resource/list

获取小语种备课资源列表，支持分页查询。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|book_id|query|number|否|教材ID|
|chapter_id|query|number|否|章节ID|
|type|query|number/string|否|资源类型|
|type_id|query|string|否|类型ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xyz/prep/resource/list?offset=0&limit=20&book_id=1001&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 85,
    "list": [
      {
        "id": "resource_001",
        "name": "日语发音练习课件",
        "type": 1,
        "file_type": "pptx",
        "file_size": 3145728,
        "download_count": 156,
        "ctime": 1698825600000
      }
    ]
  }
}
```

---

# 错题本功能

## GET 获取考试列表

GET /v1/ctb/exam/list

获取可用于错题本的考试列表。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|type|query|number|否|考试类型|
|event_time_start|query|string|否|开始时间|
|event_time_end|query|string|否|结束时间|
|exam_name|query|string|否|考试名称|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/ctb/exam/list?offset=0&limit=20&type=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 23,
    "list": [
      {
        "id": "exam_001",
        "name": "2024年春季期中考试",
        "type": 1,
        "event_time": "2024-04-15T08:00:00Z",
        "status": "已结束",
        "paper_count": 3,
        "class_count": 8
      }
    ]
  }
}
```

---

## GET 获取考试题目详情

GET /v1/ctb/exam/{exam_id}/paper/{paper_id}/questions

获取考试试卷的题目详细信息，用于错题本分析。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|zujuanId|query|string|否|组卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/ctb/exam/exam_001/paper/paper_001/questions" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "q_001",
      "content": "下列数中，是有理数的是（）",
      "type": "选择题",
      "difficulty": "容易",
      "score": 4,
      "accuracy": 0.65,
      "error_students": [
        {
          "id": "stu_001",
          "name": "张三",
          "answer": "C",
          "correct_answer": "B"
        }
      ]
    }
  ]
}
```

---

## POST 相似题搜索

POST /v1/ctb/exam/{exam_id}/paper/{paper_id}/question/search

搜索与指定题目相似的题目，用于错题本练习。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|path|string|是|考试ID|
|paper_id|path|string|是|试卷ID|
|offset|body|number|是|分页偏移量|
|limit|body|number|是|每页数量|
|type|body|string|否|题目类型|
|difficulty|body|string|否|难度|
|question_id|body|number|否|参考题目ID|
|knowledges|body|array|否|知识点数组|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/ctb/exam/exam_001/paper/paper_001/question/search" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "offset": 0,
    "limit": 10,
    "type": "选择题",
    "difficulty": "容易",
    "question_id": 12345
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 45,
    "list": [
      {
        "id": 12346,
        "content": "关于有理数的加法运算，下列说法正确的是（）",
        "type": "选择题",
        "difficulty": "容易",
        "similarity": 0.89
      }
    ]
  }
}
```

---

## POST 保存错题本配置

POST /v1/ctb/config

保存错题本的导出配置，包括题目选择和学生配置。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|exam_id|body|string|是|考试ID|
|paper_id|body|string|是|试卷ID|
|questions|body|array|是|题目配置数组|
|student_config|body|object|是|学生配置|
|class_config|body|object|是|班级配置|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/ctb/config" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "exam_id": "exam_001",
    "paper_id": "paper_001",
    "questions": [
      {
        "id": "q_001",
        "include_similar": true,
        "similar_count": 2
      }
    ],
    "student_config": {
      "min_error_rate": 0.3
    },
    "class_config": {
      "class_ids": ["class_001", "class_002"]
    }
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "config_id": "config_001",
    "created": true
  }
}
```

---

# 学科网集成

## GET 获取授权链接

GET /v1/xkw/auth/url

获取学科网OAuth授权链接。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string|是|学段|
|subject|query|string|是|科目|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/xkw/auth/url?period=初中&subject=数学" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "auth_url": "https://www.zxxk.com/oauth/authorize?client_id=xxx&redirect_uri=xxx&state=xxx",
    "state": "state_token_123",
    "expire_time": 1698998400000
  }
}
```

---

## POST 同步试题篮

POST /v1/xkw/sync/basket

从学科网同步试题篮到本地。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|open_id|body|string|是|学科网用户OpenID|
|paper_id|body|string|是|试卷ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/xkw/sync/basket" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "open_id": "xkw_user_123",
    "paper_id": "paper_001"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "sync_count": 15,
    "success": true
  }
}
```

---


# 管理系统集成

## GET 获取学校应用使用信息

GET /v1/boss/customer/app_usage/get_by_school_id

获取指定学校的应用使用统计信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|schoolId|query|number|是|学校ID|
|productCategory|query|string|是|产品类别|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/boss/customer/app_usage/get_by_school_id?schoolId=1001&productCategory=education" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "school_id": 1001,
    "school_name": "示例中学",
    "product_category": "education",
    "usage_stats": {
      "active_users": 856,
      "total_sessions": 12350,
      "avg_session_time": 1800,
      "last_active_time": 1698825600000
    },
    "feature_usage": [
      {
        "feature": "exam_management",
        "usage_count": 125,
        "last_used": 1698825600000
      }
    ]
  }
}
```

---

# 知识库集成

## GET 获取地区信息

GET /v1/kb/regions/simple

获取知识库中的地区信息（透传接口）。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/kb/regions/simple" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "110000",
      "name": "北京市",
      "level": 1,
      "children": [
        {
          "id": "110100",
          "name": "北京市区",
          "level": 2
        }
      ]
    }
  ]
}
```

---

## 返回数据结构

### 资源信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|资源ID|唯一标识|
|name|string|true|none|资源名称|资源标题|
|type|number|true|none|资源类型|1-课件 2-教案 3-习题等|
|file_type|string|false|none|文件类型|如pptx、docx等|
|file_size|number|false|none|文件大小|字节数|
|download_count|number|false|none|下载次数|下载统计|
|ctime|number|true|none|创建时间|时间戳|

### 考试信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|考试ID|唯一标识|
|name|string|true|none|考试名称|考试标题|
|type|number|true|none|考试类型|1-期中 2-期末等|
|event_time|string|true|none|考试时间|ISO格式时间|
|status|string|true|none|考试状态|已结束/进行中等|

### 题目信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|题目ID|唯一标识|
|content|string|true|none|题目内容|题目文本|
|type|string|true|none|题目类型|选择题/填空题等|
|difficulty|string|true|none|难度|容易/中等/困难|
|score|number|true|none|分值|题目分数|
|accuracy|number|false|none|正确率|0-1之间的小数|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|12|参数错误|请求参数格式错误或缺失必要参数|
|14|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 集成架构

所有第三方集成遵循统一的架构模式：
- **认证统一**: 使用JWT进行用户认证
- **参数验证**: 使用Joi进行严格的参数校验  
- **错误处理**: 统一的错误响应格式
- **数据访问**: 支持多数据库和外部API调用

### 安全机制

- **输入验证**: 所有API都进行严格的参数验证
- **权限控制**: 基于用户和学校的数据隔离
- **API限流**: 防止恶意调用和系统过载
- **数据脱敏**: 敏感信息自动脱敏处理

### 性能优化

- **数据缓存**: 热点数据进行缓存优化
- **分页查询**: 大数据量查询支持分页
- **异步处理**: 文件下载等耗时操作异步处理
- **CDN加速**: 静态资源使用CDN分发

### 错误恢复

- **服务降级**: 外部服务不可用时的降级处理
- **重试机制**: 网络异常时的自动重试
- **监控告警**: 异常情况的实时监控和告警
- **日志记录**: 详细的操作日志用于问题排查