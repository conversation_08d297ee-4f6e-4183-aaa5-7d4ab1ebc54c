const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../../common/enums/template');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const subject_utils = require('../../../common/utils/subject_utils');
const question_utils = require('../../../common/utils/question_utils');
const paper_utils = require('../../../common/utils/paper_utils');
const exam_utils = require('./exam_utils');
const logger = require('../../../common/lib/logger');
const user_paper_model = require('../user_paper/index');

module.exports = {
    get_list,
    get_exam_detail,
    get_class_detail,
    post_paper,
    getExamList,
    getExamPaperClasses,
    getExamPaperQuestionDetail,
    getExamPaperQuestionSame,
    getExamPaperQuestionAnswerImage,
    putExamPaperQuestionAnswerTag,
    getExamPaperStudentQuestionAnswer,
}

async function get_list(params) {
    const {user_id} = params;
    const hfsExamList = await client.hfsTeacherV3.getExamList(user_id);
    return hfsExamList;
}

async function get_exam_detail(params) {
    const { exam_id, paper_id, user_id, user_paper_id = '' } = params;
    const hfsExamList = await client.hfsTeacherV3.getExamList(user_id);
    const hfs_exam = hfsExamList.find(e => e.id === exam_id && e.paper_id === paper_id);
    if (_.isEmpty(hfs_exam)) return;
    const result = {
        id: exam_id,
        name: hfs_exam.name || '',
        paper_id,
        user_paper_id: user_paper_id || '',
        original_question_count: 0,
        papers: [],
        table: {},
        table_id: null,
        classes: hfs_exam.classes || [],
        original: enums.BOOL.NO, // 是否上传原卷0：否，1：是
        published: enums.BOOL.NO, // 是否发布成绩0：否，1：是
    }
    const paper_sign = paper_id.split('-')[0];
    const is_user_paper = !!user_paper_id;
    let tiku_paper, yp_data, period, subject, grade;
    if (!is_user_paper) {
        yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        if (_.isEmpty(yp_data)) return result;
        const yp_questions = _.get(yp_data, 'questions', []);
        result.original_question_count = _.size(yp_questions);
        const yp_paper = _.get(yp_data, 'paper', null);
        period = _.get(yp_paper, 'period', '');
        grade = _.get(yp_paper, 'grade', '');
        subject = subject_utils.regularSubject(_.get(yp_paper, 'subject', ''));
    } else {
        tiku_paper = await client.tiku.getPaperById(user_paper_id);
        if (_.isEmpty(tiku_paper)) return result;
        result.original_question_count = _.isEmpty(tiku_paper) ? 0 : paper_utils.get_question_num(tiku_paper);
        period = tiku_paper.period;
        grade = tiku_paper.grade;
        subject = tiku_paper.subject;
    }
    const paper_status = await getPaperStatus(exam_id, paper_id, yp_data);
    result.original = paper_status.original;
    result.published = paper_status.published;
    if (is_user_paper) {
        result.original = enums.BOOL.YES;
    }
    // 平行组卷
    const exam = await db.collection(schema.user_exam).findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
    if (!_.isEmpty(exam)) {
        if (is_user_paper && !exam.user_paper_id) {
            await db.collection(schema.user_exam).updateOne({_id: exam._id}, {$set: {user_paper_id}});
        }
        const ids = (exam.papers || []).map(e => e.id);
        if (_.size(ids)) {
            const fields = {_id: 1, name: 1, period: 1, subject: 1, type: 1, exampaper_download_num: 1, volumes: 1, category: 1, ctime: 1};
            const papers = await db.collection(schema.user_paper).find({_id: { $in: ids.map(e => new ObjectId(e))}}).project(fields).toArray();
            for (const ep of exam.papers) {
                const paper = (papers || []).find(e => e._id.toString() === ep.id);
                if (_.isEmpty(paper)) continue;
                result.papers.push(_get_paper_result_data(paper));
            }
        }
        result.table_id = exam.table_id;
    } else { // 初始化数据
        await initExam(user_id, exam_id, paper_id, user_paper_id, period || '', grade || '', subject || '');
    }
    // 细目表
    const tableIds = _.get(config.get('english_tw_list'), period, []);

    if (subject === enums.Subject.ENGLISH && _.size(tableIds)) {
        const arr = Array.from(paper_sign);
        const index = +(arr[arr.length -1]);
        const table_paper_id = tableIds[index];
        const kb_table = await client.kb.getTableById(table_paper_id);

        const table = {
            name: '',
            period: period,
            grade: grade,
            subject: subject,
            blocks: _.get(kb_table, 'blocks', [])
        };
        for (const block of table.blocks) {
            for (const q of block.questions || []) {
                q.difficulty = '不限';
                q.knowledges = q.knowledges.length <= 3 ? q.knowledges : q.knowledges.slice(0, 3);
            }
        }
        result.table = table;
    } else {
        if (is_user_paper) {
            result.table = await _transform_to_table(user_id, exam_id, paper_id, tiku_paper);
        } else {
            result.table = await _transform_to_table_by_yp(user_id, exam_id, paper_id, yp_data);
        }

    }
    return result;
}

async function initExam(user_id, exam_id, paper_id, user_paper_id = '', period, grade, subject) {
    const exam = {
        user_id: user_id,
        exam_id: exam_id,
        paper_id: paper_id,
        user_paper_id: user_paper_id, // 用户组卷ID
        grade: grade, // 年级
        period: period, // 学段
        subject: subject, // 科目
        papers: [],
        class_papers: [],
        questions: [],
        ctime: new Date(),
        utime: new Date(),
    };
    const result = await db.collection(schema.user_exam).insertOne(exam);
    exam._id = result.insertedId;
    return exam;
}


async function get_class_detail(params) {
    const { exam_id, paper_id, class_id, user_id, user_paper_id = '' } = params;
    const hfs_class_paper_info = await client.hfsTeacherV3.getClassPaperStatistics(user_id, exam_id, paper_id, class_id);
    if (_.isEmpty(hfs_class_paper_info)) return new BussError('班级信息不存在');

    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, class_id);
    if (!_.size(hfs_questions)) return new BussError('班级考试信息不存在');
    const result = {
        grade_score: {
            avg: _.get(hfs_class_paper_info, 'grade.avg', -1), // 年级平均分
            excellent_rate: _.get(hfs_class_paper_info, 'grade.excellentRate', -1), // 优秀率
            class_count: _.get(hfs_class_paper_info, 'grade.classCount', -1), // 班级数量
        },
        class_score: {
            avg: _.get(hfs_class_paper_info, 'myClass.avg', -1), // 班级平均分
            excellent_rate: _.get(hfs_class_paper_info, 'myClass.excellentRate', -1), // 优秀率
            avg_rank: _.get(hfs_class_paper_info, 'myClass.avgRank', -1), // 平均分年级排名
        },
        knowledges: [], // 知识点
        paper: null, // 当前薄弱知识点组卷
        table: null, // 细目表
        table_id: null, // 个人细目表
    };
    const is_user_paper = !!user_paper_id;
    // 元培数据
    let tiku_paper, yp_data;
    if (is_user_paper) {
        tiku_paper = await client.tiku.getPaperById(user_paper_id);
        if (_.isEmpty(tiku_paper)) return result;
    } else {
        yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        if (_.isEmpty(yp_data)) return result;
    }

    // 平行组卷
    const exam = await db.collection(schema.user_exam).findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
    if (!_.isEmpty(exam)) {
        const class_paper = (exam.class_papers || []).find(e => e.class_id === class_id);
        if (!_.isEmpty(class_paper)) {
            if (class_paper.id) {
                const paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(class_paper.id)});
                result.paper = _get_paper_result_data(paper);
            }
            result.table_id = class_paper.table_id;
        }
    }

    if (is_user_paper) {
        const { knowledges, table } = await _get_class_knowledges(paper_id.split('-')[0], hfs_questions, tiku_paper);
        result.knowledges = knowledges; // 薄弱知识点
        result.table = table; // 薄弱知识点细目表
    } else {
        const { knowledges, table } = await _get_class_knowledges_by_yp(paper_id.split('-')[0], hfs_questions, yp_data);
        result.knowledges = knowledges; // 薄弱知识点
        result.table = table; // 薄弱知识点细目表
    }

    return result;
}

async function post_paper(params) {
    const { user_id, exam_id, paper_id, class_id, class_name, category, table, user_paper_id = '' } = params;
    const now = new Date();
    const result = {
        papers: [],
        table_id: ''
    }
    let user_exam = await db.collection(schema.user_exam).findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
    for (const cat of category) {
        const cat_table = _.assign({}, table);
        let algoPaper = null;
        if (cat !== 4) {
            if (!user_paper_id) {
                const algo_params = exam_utils.build_level_paper_params(user_exam, cat);
                algoPaper = await client.algo.levelPaper(algo_params);
            } else { //
                const algo_params = exam_utils.build_detail_paper_params(user_exam, cat, cat_table, params);
                algoPaper = await client.algo.detailTablePaper(algo_params);
            }
        } else {
            // const algo_params = _build_create_paper_params(user_id, cat, cat_table, params);
            const algo_params = exam_utils.build_detail_paper_params(user_exam, cat, cat_table, params);
            algoPaper = await client.algo.detailTablePaper(algo_params);
        }

        if (_.isEmpty(algoPaper)) throw new BussError('组卷失败');
        const user_paper = await _transform_to_user_exampaper(user_id, cat, params, algoPaper);
        if (!paper_utils.get_question_num(user_paper)) throw new BussError('组卷试题数量异常');
        result.papers.push(user_paper);
        if (cat !== 4) {
            const paper = user_exam.papers.find(e => e.category === cat);
            if (_.isEmpty(paper)) {
                user_exam.papers.push({
                    id: user_paper.id,
                    type: user_paper.type,
                    category: cat, // 分类：1-基础，2-巩固，3-拔高，4-薄弱
                    time: now,
                });
            } else {
                paper.id = user_paper.id;
                paper.time = now;
            }
        } else {
            const paper = user_exam.class_papers.find(e => e.class_id === class_id);
            if (_.isEmpty(paper)) {
                user_exam.class_papers.push({
                    class_id: class_id,
                    id: user_paper.id,
                    type: user_paper.type,
                    category: cat, // 分类：1-基础，2-巩固，3-拔高，4-薄弱
                    time: now,
                });
            } else {
                paper.id = user_paper.id;
                paper.category = cat;
                paper.type = user_paper.type;
                paper.time = now;
            }
        }
    }

    // 保存
    const update_data = {
        utime: now,
        papers: user_exam.papers,
        class_papers: user_exam.class_papers
    };
    if (user_exam && !user_exam.user_paper_id) {
        update_data.user_paper_id = user_paper_id;
    }
    // 保存细目表
    let type = 1;
    const table_params = {
        user_id: user_id,
        class_id: class_id,
        type: class_id ? 1 : 2,
        table: table
    };
    let old_table_id = null;
    if (category && category[0] === 4) {
        type = 2;
    }
    if (type === 1) {
        old_table_id = user_exam.table_id;
    } else {
        const class_paper = update_data.class_papers.find(e => e.class_id === class_id);
        if (!_.isEmpty(class_paper)) {
            old_table_id = class_paper.table_id;
        }
    }
    if (old_table_id) {
        table_params.id = old_table_id;
    }
    const table_id = await createTable(table_params);
    result.table_id = table_id;
    if (type === 1) {
        if (!user_exam.table_id) update_data.table_id = table_id;
    } else {
        const class_paper = update_data.class_papers.find(e => e.class_id === class_id);
        if (!_.isEmpty(class_paper)) {
            class_paper.table_id = table_id;
        }
    }
    await db.collection(schema.user_exam).updateOne({ _id: user_exam._id }, { $set: update_data });
    return result;
}

async function createTable(params) {
    const { user_id, id, type, table } = params;
    const now = new Date();
    table.permission = 'private';
    table.relevance_type = null;
    table.relevance_id = null;
    //
    let difficulty = '不限';
    if (type === 2) {
        difficulty = '容易';
    }
    for (const b of table.blocks) {
        for (const q of b.questions) {
            q.difficulty = difficulty;
            q.period = table.period;
            q.subject = table.subject;
        }
    }
    let table_id = null;
    if (id) { // 修改
        await client.kb.updateTable(id, table, user_id);
        table_id = id;
    } else { // 新增
        const kb_table_result = await client.kb.createTable(table, user_id);
        table_id = kb_table_result.id;
        // 保存细目表
        await db.collection(schema.user_tw_specification).insertOne({
            user_id: user_id,
            table_id: new ObjectId(table_id),
            ctime: now,
            period: table.period,
            subject: table.subject,
            type: '考后巩固',
            grade: table.grade,
            province: table.province
        });
    }
    return table_id;
}


function _build_create_paper_params(user_id, category, table, body) {
    const default_difficulty = category_difficulty[category];
    const param_table = _.assign({}, table);
    const blocks = [];
    for (const b of param_table.blocks) {
        const block = {
            type: b.questions[0].type,
            questions: b.questions.map(e => {
                const type_t = TYPES[e.type] || TYPES['default'];
                const obj = {
                    type: e.type,
                    score: e.score || type_t.default_score,
                    knowledges: e.knowledges,
                    difficulty: category === 4 ? e.difficulty : default_difficulty
                };
                if (category === 4 || obj.difficulty === '不限') {
                    obj.difficulty = '中等';
                }
                return obj;
            })
        }
        blocks.push(block);
    }
    const params = {
        period: body.table.period,               // 必选，学段
        subject: body.table.subject,              // 必选，科目
        type: '平行组卷',                 // 必填，平行组卷
        // school_id: user.schoolId,            // 选填，学校ID
        user_id: user_id,              // 选填，用户ID
        paper_id: body.paper_id,             // 选填，试卷ID
        class_name: body.class_name || '',           // 选填，班级名
        grade: body.table.grade,                // 选填，年级
        // min_year: int,             // 选填，最小试题年份限制
        blocks,
    }
    return params;
}

function _get_paper_result_data(exampaper) {
    const result = {};
    if (!_.isEmpty(exampaper)) {
        result.id=exampaper._id.toString();
        result.name = exampaper.name;
        result.type = exampaper.type;
        result.category = exampaper.category;
        result.period = exampaper.period;
        result.subject = exampaper.subject;
        result.question_count = _get_paper_question_count(exampaper);
        result.download = exampaper.exampaper_download_num ? 1 : 0; // 是否下载
        result.favorite = 0; // 是否收藏
        result.ctime = exampaper.ctime.getTime(); // 组卷时间
    }
    return result;
}

function _get_paper_question_count(exampaper) {
    let count = 0;
    if (!_.isEmpty(exampaper)) {
        const question = [];
        if (exampaper.hasOwnProperty('volumes')) {
            for (const v of exampaper.volumes) {
                for (const b of v.blocks) {
                    for (const q of b.questions) {
                        question.push(q);
                    }
                }
            }
        } else if (exampaper.hasOwnProperty('blocks')) {
            for (const b of exampaper.blocks) {
                for (const q of b.questions) {
                    question.push(q);
                }
            }
        } else if (exampaper.hasOwnProperty('questions')) {
            for (const q of exampaper.questions) {
                question.push(q);
            }
        }
        count = _.size(question);
    }
    return count;
}


/**
 * 试题转换为细目表
 * 采用大题模式
 * @param user_id
 * @param exam_id
 * @param paper_id
 * @param tiku_paper
 * @returns {Promise<{period: string, subject: *, blocks: *, grade: *, name: string}|null>}
 * @private
 */
async function _transform_to_table(user_id, exam_id, paper_id, tiku_paper) {
    // 好分数试题
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, null);
    if (!_.size(hfs_questions)) return null;

    const period = _.get(tiku_paper, 'period', '');
    const grade =_.get(tiku_paper, 'grade', '');
    const subject = _.get(tiku_paper, 'subject', '');

    const tiku_questions = getTikuPaperQuestions(tiku_paper);
    const blocks = [];
    for (const q of tiku_questions) {
        let knowledges = (q.knowledges || []).map(e => _.pick(e, ['id', 'name']));
        if (!_.size(knowledges)) continue; // 没有知识点的题目跳过
        const new_ques = {
            knowledges: knowledges.length <= 3 ? knowledges : knowledges.slice(0, 3), //
            type: q.type,
            period,
            subject,
            difficulty: '不限'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
    }

    const table = {
        name: '',
        period,
        grade,
        subject,
        blocks
    };
    return table;
}

/**
 * 试题转换为细目表
 * 采用大题模式
 * @param user_id
 * @param exam_id
 * @param paper_id
 * @returns {Promise<{period: string, subject: *, blocks: *, grade: *, name: string}|null>}
 * @private
 */
async function _transform_to_table_by_yp(user_id, exam_id, paper_id, yp_data) {
    // 好分数试题
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, null);
    if (!_.size(hfs_questions)) return null;

    const yp_paper = _.get(yp_data, 'paper', null);
    const period = _.get(yp_paper, 'period', '');
    const grade =_.get(yp_paper, 'grade', '');
    const subject = subject_utils.regularSubject(_.get(yp_paper, 'subject', ''));

    const yp_questions = _.get(yp_data, 'questions', []);
    const blocks = [];
    for (const q of yp_questions) {
        const point = q.points[0]; // 大题模式-默认取第一个给分点
        const index = parseInt(point.id.split('-')[1]);
        const hfs_question = hfs_questions.find(e => e.questionId === index);
        if (_.isEmpty(hfs_question)) continue;
        const knowledges = [];
        for (const p of q.points) {
            const questionId = parseInt(point.id.split('-')[1]);
            const knowledge_questions = hfs_questions.filter( e=> e.questionId === questionId);
            if (_.size(knowledge_questions)) {
                for (const k_q of knowledge_questions) {
                    if (!_.size(k_q.knowledges)) continue;
                    const arr = k_q.knowledges.filter(n => !knowledges.find(k => k.id === n.id))
                    if (_.size(arr)) knowledges.push(...arr);
                }
            }
        }
        if (!_.size(knowledges)) continue; // 没有知识点的题目跳过
        const new_ques = {
            knowledges: knowledges.length <= 3 ? knowledges : knowledges.slice(0, 3), //
            type: _get_hfs_question_type(subject, hfs_question, q),
            period,
            subject,
            difficulty: '不限'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
        // delete block.type;
    }

    return {
        name: '',
        period,
        grade,
        subject,
        blocks
    };
}

/**
 * 获取班级知识点列表
 * @param paper_sign 科目ID
 * @param hfs_questions 试题列表
 * @param tiku_paper 元培试卷信息
 * @private
 */
async function _get_class_knowledges(paper_sign, hfs_questions, tiku_paper) {
    const subject = _.get(tiku_paper, 'subject');
    const period = _.get(tiku_paper, 'period', '');
    const grade = _.get(tiku_paper, 'grade', '');
    const tiku_questions = getTikuPaperQuestions(tiku_paper);
    let knowledges = [];
    for (const hfs_question of hfs_questions) {
        const tiku_question = tiku_questions[hfs_question.questionId];
        if (!tiku_question) continue;
        const type = tiku_question.type;
        const question_knowledges = tiku_question.knowledges;
        if (!_.size(question_knowledges)) continue;

        for (const q_k of question_knowledges) {
            let k = knowledges.find(e => e.id === q_k.id);
            if (_.isEmpty(k)) {
                k = {
                    id: q_k.id,
                    name: q_k.name,
                    classAvgScore: 0,
                    gradeAvgScore: 0,
                    score: 0,
                    count: 0,
                    question_detail: []
                };
                knowledges.push(k);
            }
            k.classAvgScore = _.add(k.classAvgScore, hfs_question.avgScore);
            k.gradeAvgScore = _.add(k.gradeAvgScore, hfs_question.gradeScore);
            k.score = _.add(k.score, hfs_question.manfen);
            k.count += 1;

            const y_q = k.question_detail.find(e => e.type === type);
            if (_.isEmpty(y_q)) {
                k.question_detail.push({
                    id: tiku_question.id,
                    classScoreRate: hfs_question.classScoreRate,
                    type
                });
            } else {
                if (hfs_question.classScoreRate < y_q.classScoreRate) {
                    y_q.classScoreRate = hfs_question.classScoreRate;
                }
            }
        }
    }
    if (!_.size(knowledges)) return {};
    // 计算差值
    for (const k of knowledges) {
        const classScoreRate = _.round(k.classAvgScore / k.score, 4);
        const gradeScoreRate = _.round(k.gradeAvgScore / k.score, 4);
        k.diff = _.round(classScoreRate - gradeScoreRate, 4);
        k.classScoreRate = classScoreRate;
        k.gradeScoreRate = gradeScoreRate;
    }

    if (_.size(knowledges)) {
        knowledges = knowledges.sort( (a, b) => {
            if (a.diff > b.diff) {
                return 1;
            } else {
                return -1;
            }
        });
    }
    // 细目表
    let table = null;
    // 英语特殊处理
    const tableIds = _.get(config.get('english_tw_list'), period, []);
    if (subject === enums.Subject.ENGLISH && _.size(tableIds)) {
        const arr = Array.from(paper_sign);
        const index = +(arr[arr.length -1]);
        const table_paper_id = tableIds[index];
        const kb_table = await client.kb.getTableById(table_paper_id);
        table = {
            name: '',
            period: period,
            grade: grade,
            subject: subject,
            blocks: _.get(kb_table, 'blocks', [])
        };
        for (const block of table.blocks) {
            for (const q of block.questions || []) {
                q.difficulty = '容易';
                q.knowledges = q.knowledges.length <= 3 ? q.knowledges : q.knowledges.slice(0, 3);
            }
        }
    } else {
        table = _get_class_knowledges_table(knowledges, tiku_paper);
    }
    // 清理数据
    for (const k of knowledges) {
        delete k.classAvgScore;
        delete k.gradeAvgScore;
        delete k.count;
        delete k.question_detail;
    }
    // 返回结果
    return { knowledges, table };
}

/**
 * 获取班级知识点列表
 * @param paper_sign 科目ID
 * @param hfs_questions 试题列表
 * @param yp_paper_info 元培试卷信息
 * @private
 */
async function _get_class_knowledges_by_yp(paper_sign, hfs_questions, yp_paper_info) {
    const subject = subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject'));
    const period = _.get(yp_paper_info, 'paper.period', '');
    const grade = _.get(yp_paper_info, 'paper.grade', '');
    const yp_questions = _.get(yp_paper_info, 'questions', []);
    let knowledges = [];
    for (const hfs_question of hfs_questions) {
        const question_knowledges = hfs_question.knowledges;
        const yp_question = yp_questions.find(e => {
            const point = e.points.find(it => parseInt(it.id.split('-')[1]) === hfs_question.questionId);
            if (!_.isEmpty(point)) return true;
            return false;
        });
        const type = _get_hfs_question_type(subject, hfs_question, yp_question);
        if (!_.size(question_knowledges)) continue;
        for (const q_k of question_knowledges) {
            let k = knowledges.find(e => e.id === q_k.id);
            if (_.isEmpty(k)) {
                k = {
                    id: q_k.id,
                    name: q_k.name,
                    classAvgScore: 0,
                    gradeAvgScore: 0,
                    score: 0,
                    count: 0,
                    question_detail: []
                };
                knowledges.push(k);
            }
            k.classAvgScore = _.add(k.classAvgScore, hfs_question.avgScore);
            k.gradeAvgScore = _.add(k.gradeAvgScore, hfs_question.gradeScore);
            k.score = _.add(k.score, hfs_question.manfen);
            k.count += 1;

            const y_q = k.question_detail.find(e => e.type === type);
            if (_.isEmpty(y_q)) {
                k.question_detail.push({
                    id: yp_question.id,
                    classScoreRate: hfs_question.classScoreRate,
                    type
                });
            } else {
                if (hfs_question.classScoreRate < y_q.classScoreRate) {
                    y_q.classScoreRate = hfs_question.classScoreRate;
                }
            }
        }
    }
    if (!_.size(knowledges)) return {};
    // 计算差值
    for (const k of knowledges) {
        const classScoreRate = _.round(k.classAvgScore / k.score, 4);
        const gradeScoreRate = _.round(k.gradeAvgScore / k.score, 4);
        k.diff = _.round(classScoreRate - gradeScoreRate, 4);
        k.classScoreRate = classScoreRate;
        k.gradeScoreRate = gradeScoreRate;
    }

    if (_.size(knowledges)) {
        knowledges = knowledges.sort( (a, b) => {
            if (a.diff > b.diff) {
                return 1;
            } else {
                return -1;
            }
        });
    }
    // 细目表
    let table = null;
    // 英语特殊处理
    const tableIds = _.get(config.get('english_tw_list'), period, []);
    if (subject === enums.Subject.ENGLISH && _.size(tableIds)) {
        const arr = Array.from(paper_sign);
        const index = +(arr[arr.length -1]);
        const table_paper_id = tableIds[index];
        const kb_table = await client.kb.getTableByRefId(table_paper_id);
        table = {
            name: '',
            period: period,
            grade: grade,
            subject: subject,
            blocks: _.get(kb_table, 'blocks', [])
        };
        for (const block of table.blocks) {
            for (const q of block.questions || []) {
                q.difficulty = '容易';
                q.knowledges = q.knowledges.length <= 3 ? q.knowledges : q.knowledges.slice(0, 3);
            }
        }
    } else {
        table = _get_class_knowledges_table_by_yp(knowledges, yp_paper_info);
    }
    // 清理数据
    for (const k of knowledges) {
        delete k.classAvgScore;
        delete k.gradeAvgScore;
        delete k.count;
        delete k.question_detail;
    }
    // 返回结果
    return { knowledges, table };
}


/**
 * 根据班级薄弱知识点生成细目表
 * @param knowledges
 * @param tiku_paper
 * @returns {{period: undefined, subject: *, blocks: *, grade: undefined, name: string}}
 * @private
 */
function _get_class_knowledges_table(knowledges, tiku_paper) {
    // 筛选知识点
    let select_knowledge = [];
    for (const k of knowledges) {
        if (k.diff < 0) {
            select_knowledge.push(k);
        }
        if (select_knowledge.length === 10) break;
    }
    if (_.size(select_knowledge) < 5) { // 小于5个薄弱知识点，根据得分差获取前5个
        select_knowledge = knowledges.slice(0, 5);
    }
    // 生成薄弱知识点细目表
    const knowledge_questions = [];
    for (const k of select_knowledge) {
        let arr = k.question_detail;
        if (arr.length > 1) {
            arr = arr.sort((a, b) => {
                if (a.classScoreRate > b.classScoreRate) {
                    return 1;
                } else {
                    return -1;
                }
            });
            for (let i = 0; i < 2; i++) {
                const q = arr[i];
                knowledge_questions.push({
                    type: q.type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        } else {
            for (let i = 0; i < 2; i++) {
                knowledge_questions.push({
                    type: arr[0].type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        }
    }
    // 组合细目表
    const blocks = [];
    for (const q of knowledge_questions) {
        const new_ques = {
            knowledges: q.knowledges, //
            type: q.type,
            period: _.get(tiku_paper, 'period'),
            subject: _.get(tiku_paper, 'subject', ''),
            difficulty: '容易'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
    }

    const table = {
        name: '',
        period: _.get(tiku_paper, 'period'),
        grade: _.get(tiku_paper, 'grade'),
        subject: _.get(tiku_paper, 'subject', ''),
        blocks
    };
    return table;
}

/**
 * 根据班级薄弱知识点生成细目表
 * @param knowledges
 * @param yp_paper_info
 * @returns {{period: undefined, subject: *, blocks: *, grade: undefined, name: string}}
 * @private
 */
function _get_class_knowledges_table_by_yp(knowledges, yp_paper_info) {
    // 筛选知识点
    let select_knowledge = [];
    for (const k of knowledges) {
        if (k.diff < 0) {
            select_knowledge.push(k);
        }
        if (select_knowledge.length === 10) break;
    }
    if (_.size(select_knowledge) < 5) { // 小于5个薄弱知识点，根据得分差获取前5个
        select_knowledge = knowledges.slice(0, 5);
    }
    // 生成薄弱知识点细目表
    const knowledge_questions = [];
    for (const k of select_knowledge) {
        let arr = k.question_detail;
        if (arr.length > 1) {
            arr = arr.sort((a, b) => {
                if (a.classScoreRate > b.classScoreRate) {
                    return 1;
                } else {
                    return -1;
                }
            });
            for (let i = 0; i < 2; i++) {
                const q = arr[i];
                knowledge_questions.push({
                    type: q.type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        } else {
            for (let i = 0; i < 2; i++) {
                knowledge_questions.push({
                    type: arr[0].type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        }
    }
    // 组合细目表
    const blocks = [];
    for (const q of knowledge_questions) {
        const new_ques = {
            knowledges: q.knowledges, //
            type: q.type,
            period: _.get(yp_paper_info, 'paper.period'),
            subject: subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject', '')),
            difficulty: '容易'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
        // delete block.type;
    }

    const table = {
        name: '',
        period: _.get(yp_paper_info, 'paper.period'),
        grade: _.get(yp_paper_info, 'paper.grade'),
        subject: subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject', '')),
        blocks
    };
    return table;
}

/**
 *
 * @param user 用户信息
 * @param category 类型
 * @param body 请求参数
 * @param algoPaper 算法组卷信息
 * @returns {Promise<{template: string, period: string, subject: string, attentions: null, volumes: [{note: string, blocks: [], title: string}, {note: string, blocks: [], title: string}], secret_tag: null, duration: null, score: null, gutter: null, subtitle: null, name: null, _id: *, paper_info: null, cand_info: null}>}
 * @private
 */
async function _transform_to_user_exampaper(user_id, category, body, algoPaper) {
    const { table } = body;
    const { grade } = table;
    const { type, blocks } = algoPaper;
    const { period, subject } = body.table;

    const date = new Date();
    const basket = paper_utils.init();
    delete basket['_id'];
    basket.type = '考后巩固';
    basket.category = category;
    // basket.display = 0; // 默认不展示
    basket.ctime = date;
    basket.utime = date;
    basket.user_id = user_id;
    // basket.sch_id = user.schoolId;
    // basket.sch_name = user.schoolName;
    basket.period = period;
    basket.subject = subject;
    basket.grade = grade;
    basket.partsList = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
    const ids = [];
    for (const b of blocks) {
        for (const q of b.questions) {
            ids.push(q.id);
        }
    }
    const kb_questions = await client.kb.getQuestionByIds(ids);
    const ques_map = {};
    for (const q of kb_questions) {
        ques_map[q.id] = q;
    }
    for (const q of kb_questions) {
        if (_.isEmpty(q)) continue;
        const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject']);
        _insert_questions(basket, simple_question);
    }
    _render_basket(basket);
    // 平行试卷名称：考试名称+平行基础卷/巩固卷/拔高卷
    let name = `${body.exam_name}平行${category_name[category]}`;
    if (category === 4) {
        // 薄弱点巩固卷：考试名称+班级名称+薄弱点巩固卷
        let class_name = body.class_name || '';
        if (class_name) {
            class_name = body.class_name.includes('班') ? body.class_name : body.class_name + '班';
        }
        name = `${body.exam_name}${class_name}${category_name[category]}`;
    }
    basket.name = name;
    basket.source_type = enums.PaperSourceType.EXAM;
    basket.source = enums.PaperSourceType.EXAM;
    basket.valid = enums.BOOL.YES;
    basket.status = enums.PaperStatus.DONE;
    basket.exam_status = enums.ExamStatus.EDITABLE;
    const insert_result = await db.collection(schema.user_paper).insertOne(basket);
    basket.id = insert_result.insertedId.toString();
    delete basket._id;
    _traverse_questions(basket, ques_map);
    basket.ctime = basket.ctime.getTime();
    basket.utime = basket.utime.getTime();
    return basket;
}

const category_name = {
    1: '基础卷',
    2: '巩固卷',
    3: '拔高卷',
    4: '薄弱点巩固卷',
    6: '追踪试卷',
}


function _render_basket(basket) {
    // 试题篮分卷信息
    basket.score = 0;
    let block_num = 0;
    const volumes = basket.volumes;
    for (const v in volumes) {
        const volume = volumes[v];
        const blocks = volume.blocks;
        for (const b in blocks) {
            const block = blocks[b];
            const questions = block.questions;
            const n = questions.length;
            if (questions.length <= 0) {
                continue;
            }
            let s = Number(questions[0].score);
            let ts = Number(s);
            let tag = true;
            for (let i = 1; i < n; ++i) {
                if (questions[i].score !== questions[i - 1].score) {
                    tag = false;
                }
                ts += Number(questions[i].score);
            }
            const order = `${DIGIT_MAP_CHINESE[++block_num]}`;
            const ss = tag ? `，每题${s}分` : '';
            block.default_score = tag ? s : block.default_score;
            const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
            const note = (block.note.length > 0) ? `，${block.note}` : '';
            block.title = `${order}、${block.type}（${detail}${note}）`;
            // block.title = `${block.type}（${detail}${note}）`;
            basket.score += Number(ts);
        }
    }
    // 试卷名称， 2017年5月3日
    // var t = new Date();
    // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
    basket.name = basket.name || genExamPaperName(basket);
    // 副标题
    basket.subtitle = basket.subtitle || '';
    // 考试时间，单位分钟
    basket.duration = basket.duration || 120;
    // 试卷信息栏，考试总分：100分；考试时间：100分钟
    const paper_info = `考试总分：${basket.score}   考试时间：${basket.duration}`;
    basket.paper_info = basket.paper_info || paper_info;
    // 候选人信息栏
    const line = '__________ ';
    const cand_info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

    basket.cand_info = basket.cand_info || cand_info;
    // 注意事项
    basket.attentions = basket.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
    // 保密标记文字
    basket.secret_tag = basket.secret_tag || '绝密★启用前';
    // 装订线
    basket.gutter = basket.gutter || 0;
    return basket;
}



function _traverse_questions(basket, ques_map) {
    const fileds = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
    for (const volume of basket.volumes || []) {
        for (const block of volume.blocks || []) {
            for (const i in block.questions) {
                const question = block.questions[i];
                const q = ques_map[question.id];
                if (!q) {
                    delete block.questions[i];
                    continue;
                }
                block.questions[i] = _.pick(q, fileds);
                block.questions[i]['score'] = question['score'];
            }
        }
    }
}

/**
 * 插入试题篮
 * @param basket
 * @param ques
 * @returns {*}
 * @private
 */
function _insert_questions(basket, ques) {
    const type = ques['type'];
    const type_t = TYPES[type] || TYPES['default'];

    const vol_pos = type_t['vol_pos'];
    const volume = basket.volumes[vol_pos];

    basket.period = ques['period'] || '';
    basket.subject = ques['subject'] || '';
    if (ques['exampaper_type'] && ques['exampaper_type'] !== enums.ExamPaperOtherType) {
        basket.type = ques['exampaper_type'];
    }

    const ques_ = {
        id: ques['id'],
        period: ques['period'],
        subject: ques['subject'],
        type: ques['type']
    }

    for (const b in volume.blocks) {
        const block = volume.blocks[b];
        if (block.type !== type) {
            continue
        }
        // deduplicated
        const questions = block.questions;
        for (const q in questions) {
            if (questions[q]['id'] === ques['id']) {
                return basket;
            }
        }
        ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
        block.questions.push(ques_);
        return basket;
    }
    // init a new block and push the question
    ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
    const block = {
        title: '',
        note: '',
        type: type,
        default_score: type_t['default_score'],
        questions: [ques_],
    };
    // find the proper postion to insert the block
    const blk_pos = type_t['blk_pos'];
    for (const i in volume.blocks) {
        const type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
        const pos = type_t_['blk_pos'];
        if (pos > blk_pos) {
            volume.blocks.splice(parseInt(i), 0, block);
            return basket;
        }
    }
    // not find proper postion, then insert to the last
    volume.blocks.push(block);
    return basket;
}


function _init_basket() {
    let basket = {
        period: '',       // 学段
        subject: '',      // 学科

        name: null,         // 试卷名称
        subtitle: null,     // 副标题
        score: null,        // 试卷总分
        duration: null,     // 考试时间，单位分钟
        paper_info: null,   // 试卷信息栏，
        cand_info: null,
        attentions: null,   // 注意事项
        secret_tag: null,   // 保密标记文字
        gutter: null,       // 装订线
        template: 'standard',   // 组卷类型，stardard, exam, homework

        volumes: [{
            title: '卷I（选择题）',  // 卷I分卷名
            //note: '请点击修改第I卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }, {
            title: '卷II（非选择题）', // 卷II分卷名
            //note: '请点击修改第II卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }]
    }
    return basket;
}


function genExamPaperName(basket) {
    const nowDate = new Date();
    const year = nowDate.getFullYear();

    // 学年
    const academicYearStartDate = new Date();
    academicYearStartDate.setMonth(0, 1);
    academicYearStartDate.setHours(0, 0, 0, 0);

    const academicYearEndDate = new Date();
    academicYearEndDate.setMonth(7, 15);
    academicYearEndDate.setHours(0, 0, 0, 0);
    let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
    if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
        academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
    }
    // 学期
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);

    let semester = "上";//学期
    if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
        semester = '下';
    }
    const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
    let info = `${academicYearLong}学年${basket.period}${basket.subject} (${semester}) ${basket.type || ''}试卷(${formatDateStr})`;
    return info;
}

const category_difficulty = {
    1: '容易',
    2: '中等',
    3: '困难',
    4: '容易',
}

function getTikuPaperQuestions(tiku_paper) {
    const questions = [];
    if (_.isEmpty(tiku_paper)) return questions;
    for (const v of tiku_paper.volumes || []) {
        for (const b of v.blocks || []) {
            questions.push(...b.questions);
        }
    }
    return questions;
}

async function getExamList(params) {
    const { user_id, offset = 0, limit = 10, type } = params;
    let list = await client.hfsTeacherV3.getTeacherExamList(user_id);
    const result = {
        total: 0,
        list: []
    };
    if (_.size(list)) {
        if (type === 1) {
            list = list.filter(e => e.type === 12);
        } else if (type === 2) {
            list = list.filter(e => e.type !== 12);
        }
    }
    if (!_.size(list)) return result;
    result.total = _.size(list);
    const pages = await _.chunk(list, limit);
    result.list = pages[parseInt(offset / limit)];
    return result;
}

async function getExamPaperClasses(params) {
    const { user_id, exam_id, paper_id } = params;
    return await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
}

async function getExamPaperQuestionDetail(params) {
    const { user_id, exam_id, paper_id, zujuanId } = params;
    const rankExam = await client.rank.getExamById(exam_id);
    if (_.isEmpty(rankExam)) throw new BussError('考试不存在或者未同步!');
    if (zujuanId && rankExam.type !== 12) throw new BussError('非考试作业');
    const rankPaper = rankExam['[papers]'].find(e => e.id === paper_id);
    const result = {
        paper: {
            exam_id: exam_id,
            exam_name: `${rankExam.name} （${rankPaper.name})`,
            original: enums.BOOL.NO, // 是否上传原卷0：否，1：是
            published: enums.BOOL.NO, // 是否发布成绩0：否，1：是
            image_url: []
        },
        questions: [],
        class_info: []
    }

    const { period, grade, subject } = getGradeInfoByRankExamPaper(rankPaper);
    result.paper.period = period;
    result.paper.grade = grade;
    result.paper.subject = subject;
    // 获取元培信息
    const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
    const paper_status = await getPaperStatus(exam_id, paper_id, yp_paper);
    result.paper.original = paper_status.original;
    result.paper.published = paper_status.published;
    if (zujuanId) {
        result.paper.original = enums.BOOL.YES;
    }

    if (!_.isEmpty(yp_paper)) {
        result.paper.image_url = _.get(yp_paper, 'paper.image_url', []);
    }

    let exam = await getUserExam(user_id, exam_id, paper_id);
    if (_.isEmpty(exam)) {
        exam = await initExam(user_id, exam_id, paper_id, zujuanId || '', period, grade, subject);
    } else {
        const ds = {};
        if (result.paper.original && !exam.period) {
            ds.period = period;
            ds.grade = grade;
            ds.subject = subject;
            exam = _.assign(exam, ds);
        }
        if (zujuanId && !exam.user_paper_id) {
            ds.user_paper_id = zujuanId;
            exam.user_paper_id = zujuanId;
        }
        if (!_.isEmpty(ds)) {
            ds.utime = new Date();
            await db.collection(schema.user_exam).updateOne({_id: exam._id}, {$set: ds});
        }
    }
    const hfs_question_info = await client.hfsTeacherV3.getExamPaperQuestionDetail(user_id, exam_id, paper_id);
    if (hfs_question_info) {
        if (exam.user_paper_id) { // 用户组卷挂载原题信息
            const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
            const kb_questions = getTikuPaperQuestions(tiku_paper);
            if (_.size(kb_questions)) {
                hfs_question_info.questions.forEach((ques, i) => {
                    const kb_question = kb_questions[i];
                    if (!_.isEmpty(kb_question)) {
                        ques.type = kb_question.type;
                        ques.origin_question = kb_question;
                    }
                })
            }
        } else {
            for (const ques of hfs_question_info.questions || []) {
                const yp_question = findYpQuestion(ques, yp_paper);
                if (yp_question) ques.type = _get_hfs_question_type(yp_paper.paper.subject, ques, yp_question);
            }
        }
        try {
            if (result.paper.published) {
                const comparison_params = [];
                for (const cls of hfs_question_info.class_info || []) {
                    comparison_params.push({
                        user_id,
                        exam_id,
                        paper_id,
                        class_id: cls.id
                    });
                }
                const comparison_res = await Promise.all(comparison_params.map(e => client.hfsTeacherV3.getExamPaperClassComparison(e.user_id, e.exam_id, e.paper_id, e.class_id)));
                for (const i in hfs_question_info.class_info || []) {
                    const cls = hfs_question_info.class_info[i];
                    const comparison = comparison_res[i];
                    sortByRank(comparison, cls);
                }
            }
        } catch (e) {
            logger.error(e);
        }
    }
    _.assign(result, _.pick(hfs_question_info, ['questions', 'class_info']) );
    return result;
}

function sortByRank(score_report, class_info) {
    if (!_.size(score_report) || _.isEmpty(class_info)) return;
    const stu_map = {};
    for (const stu of score_report) {
        stu_map[stu.studentId] = { rank: _.get(stu, 'current.classRank') };
    }
    for (const ques of class_info.question_score) {
        if (ques.hasOwnProperty('options')) { // 客观题
            for (const option of ques.options || []) {
                option.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                option.students = _.orderBy(option.students, ['rank'], ['asc']);
            }
        } else if (ques.hasOwnProperty('scores')) { // 主观题
            for (const score of ques.scores || []) {
                score.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                score.students = _.orderBy(score.students, ['rank'], ['asc']);
            }
        }
    }
}

/**
 * 获取相似题
 * @param params
 * @returns {Promise<[]>}
 */
async function getExamPaperQuestionSame(params) {
    const { user_id, exam_id, paper_id, question_id } = params;
    const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
    const status = await getPaperStatus(exam_id, paper_id, yp_paper);
    const exam = await getUserExam(user_id, exam_id, paper_id);
    if (exam.user_paper_id) {
        status.original = enums.BOOL.YES;
    }
    let result = [];
    if (!status.original) return result;
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    if (!_.size(hfs_questions)) return result;
    const hfs_question = hfs_questions.find(e => e.id === question_id);
    if (!hfs_question) return result;
    let questions = exam['questions'] || [];
    let question = questions.find(e => e.id === question_id || e.key === hfs_question.key);
    if (_.isEmpty(question)) {
        questions = await buildPaperRecommendQuestions(exam, yp_paper);
        question = questions.find(e => e.id === question_id || e.key === hfs_question.key);
    }
    if (!_.isEmpty(question)) {
        result = await client.kb.getQuestionByIds(question.same);
        question_utils.addShowTags(result);
    }
    return result;
}

async function getExamPaperQuestionAnswerImage(params) {
    const { user_id, exam_id, paper_id, question_id, class_id, min = -1, max = -1, offset, limit } = params
    const result = {
        total: 0,
        list: []
    };
    let exam = await getUserExam(user_id, exam_id, paper_id);
    let classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
    classes = (classes || []).filter(e => e.relation === 1); // 教学班级
    if (!_.size(classes)) return result;
    const classIds = classes.map(e => e.id).join(',');
    const data = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictures(user_id, exam_id, paper_id, question_id, classIds)
    let question = exam.questions.find(e => e.id === question_id);
    if (_.isEmpty(question)) return result;
    if (!question.hasOwnProperty('excellent')) {
        question.excellent = data.excellent_answer || [];
        question.mediocre = data.mediocre_answer || [];
        await db.collection(schema.user_exam).updateOne({_id: exam._id}, {$set: {questions: exam.questions}})
    }
    let list = [];
    if (class_id === 'excellent') {
        list  = question.excellent || [];
    } else if (class_id === 'mediocre') {
        list = question.mediocre || [];
    } else if (class_id){
        list = (data.answer || []).filter(e => +e.class_id === +class_id);
    } else {
        list = data.answer || [];
    }
    if (min !== -1 && max !== -1) {
        list = list.filter(e => e.score >= min && e.score <= max);
    }
    result.total = _.size(list);
    if (result.total) {
        result.list = list.slice(offset, offset + limit);
    }
    result.excellent_count = _.size(question.excellent || []);
    result.mediocre_count = _.size(question.mediocre || []);
    if (_.size(result.list)) {
        for (const data of result.list) {
            delete data.origin_pictures;
        }
        if (class_id === 'excellent' || class_id === 'mediocre') {
            let pictures = result.list.map(e => e.pictures);
            pictures = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictureUrl(exam_id, paper_id, pictures);
            for (const i in result.list) {
                result.list[i].pictures = pictures[i];
            }
        } else {
            for (const data of result.list) {
                let tagData = question.excellent.find(e => e.id === data.id);
                if (!_.isEmpty(tagData)) {
                    data.tag = tagData.tag;
                }
                tagData = question.mediocre.find(e => e.id === data.id);
                if (!_.isEmpty(tagData)) {
                    data.tag = tagData.tag;
                }
            }
            // 排序
            result.list = _.orderBy(result.list, ['score', 'id'], ['desc', 'asc']);
        }
    }
    return result;
}

async function getExamPaperStudentQuestionAnswer(params) {
    const { user_id, exam_id, paper_id, class_id, student_id, question_id } = params;
    return await client.hfsTeacherV3.getExamPaperStudentQuestionAnswer(user_id, exam_id, paper_id, class_id, student_id, question_id);
}

async function putExamPaperQuestionAnswerTag(params) {
    const { user_id, exam_id, paper_id, question_id, type, student_id } = params;
    const exam = await getUserExam(user_id, exam_id, paper_id);
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    if (!_.size(hfs_questions)) return true;
    const hfs_question = hfs_questions.find(e => e.id === question_id);
    if (!hfs_question) return true;
    const question = exam.questions.find(e => e.id === question_id || e.key === hfs_question.key);
    if (!_.isEmpty(question)) {
        const obj = (question[type] || []).find(e => e.id === student_id);
        if (_.isEmpty(obj)) {
            let classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
            classes = (classes || []).filter(e => e.relation === 1); // 教学班级
            const classIds = classes.map(e => e.id).join(',');
            const data = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictures(user_id, exam_id, paper_id, question_id, classIds)
            const newInfo = (data.answer || []).find(e => e.id === student_id);
            if (!_.isEmpty(newInfo)) {
                newInfo.pictures = newInfo.origin_pictures;
                newInfo.tag = type === 'excellent' ? 2 : 3;
                delete newInfo.origin_pictures;
                question[type] = question[type] || [];
                question[type].push(newInfo);
            }
        } else {
            // 删除
            question[type] = question[type].filter(e => e.id !== student_id);
        }
        await db.collection(schema.user_exam).updateOne({_id: exam._id}, {$set: {utime: new Date(), questions: exam.questions}});
    }
    return true;
}

async function buildPaperRecommendQuestions(exam, yp_paper) {
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    let kb_questions = [];
    if (exam.user_paper_id) {
        const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
        kb_questions = getTikuPaperQuestions(tiku_paper);
    }
    const params = [];
    const quest_map = new Map();
    const exam_questions = exam.questions || [];
    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.id === hfs_question.id || e.key === hfs_question.key);
        if (!_.isEmpty(question)) continue;

        const param = {
            period: exam.period,
            subject: exam.subject,
            grade: exam.grade,
            // ques_id: yp_question.id, // kb question id
            // ques_from: 'yuanpei', // kb
            // ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
            recom_num: 2
        };
        if (exam.user_paper_id) {
            const index = hfs_questions.findIndex(e => e.key === hfs_question.key);
            const kb_question = kb_questions[index];
            if (_.isEmpty(kb_question) || quest_map.has(kb_question.id)) continue;
            param.ques_id = kb_question.id.toString();
            param.ques_from = 'kb';
            param.ques_type = kb_question.type;
            quest_map.set(kb_question.id, []);
        } else {
            const yp_question = findYpQuestion(hfs_question, yp_paper);
            if (_.isEmpty(yp_question) || quest_map.has(yp_question.id)) continue;
            quest_map.set(yp_question.id, []);
            param.ques_id = yp_question.id;
            param.ques_from = 'yuanpei';
            param.ques_type = _get_hfs_question_type(exam.subject, hfs_question, yp_question);
        }
        params.push(param);
        // params.push(getRecomendQuestionsParam(exam, hfs_question, yp_question));
    }
    if (!_.size(params)) return exam_questions;
    let algo_result = [];
    try {
        for (const arr of _.chunk(params, 10)) {
            const res = await Promise.all(arr.map(e => client.algo.recommendQuestions(e)));
            algo_result.push(...res);
        }
    } catch (e) {
        algo_result = [];
        logger.error(e);
    }
    if (!_.size(algo_result)) return exam_questions;
    const result = [];
    //
    for (let i = 0; i < params.length; i++) {
        const param = params[i];
        quest_map.set(param.ques_id, (algo_result[i].questions || []).map(e => e.id));
    }

    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.key === hfs_question.key);
        if (!_.isEmpty(question)) {
            result.push(question);
        } else {
            let same = [];
            if (exam.user_paper_id) {
                const index = hfs_questions.findIndex(e => e.key === hfs_question.key);
                const kb_question = kb_questions[index];
                same = quest_map.get(kb_question.id.toString()) ||[];
            } else {
                const yp_question = findYpQuestion(hfs_question, yp_paper);
                same = quest_map.get(yp_question.id) ||[];
            }
            if (!_.size(same)) continue;
            result.push({
                key: hfs_question.key,
                same: same
            });
        }
    }
    await db.collection(schema.user_exam).updateOne({_id: exam._id}, {$set: {questions: result}});
    exam.questions = result;
    return result;
}


function getRecomendQuestionsParam(exam, hfs_question, yp_question) {
    const params = {
        period: exam.period,
        subject: exam.subject,
        grade: exam.grade,
        ques_id: yp_question.id, // kb question id
        ques_from: 'yuanpei', // kb
        ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
        recom_num: 2
    };
    return params;
}

function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}

function findYpQuestion(hfs_question, yp_paper) {
    let result = null;
    if (_.isEmpty(yp_paper)) return result;
    for (const yp_question of yp_paper.questions || []) {
        const point = (yp_question.points || []).find(e => e.id.split('-')[1] === hfs_question.questionId.toString());
        if (!_.isEmpty(point)) {
            result = yp_question;
            break;
        }
    }
    return result;
}

async function getUserExam(user_id, exam_id, paper_id) {
    return await db.collection(schema.user_exam).findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
}

async function getPaperStatus(examId, paperId, ypPaper) {
    const result =  {
        original: enums.BOOL.NO, // 是否上传原卷0：否，1：是
        published: enums.BOOL.NO, // 是否发布成绩0：否，1：是
    };
    if (_.isEmpty(ypPaper)) {
        ypPaper = await client.yuanpei.getPaperQuestions(paperId);
        if (!_.isEmpty(ypPaper)) {
            result.original = enums.BOOL.YES;
        }
    } else {
        result.original = enums.BOOL.YES;
    }
    const subject = _.isEmpty(ypPaper) ? '' : _.get(ypPaper, 'paper.subject');
    const published = await client.hfs.getExamPublishStatus(examId, paperId, subject);
    if (published) {
        result.published = enums.BOOL.YES;
    }
    return result;
}

function getGradeInfoByRankExamPaper(paper) {
    const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === paper.grade || e.grade === paper.grade);
    return {
        period: gradeInfo.period,
        grade: gradeInfo.grade,
        subject: paper.subject
    };
}







