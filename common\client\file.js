/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../lib/logger');
const server = config.get('file_server');
const utils = require('../utils/utils');

module.exports = {
    wordToHtml,
    pptxToImages,
};

/**
 * 解析word
 * @param period
 * @param subject
 * @param word_name
 * @param word_url
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function wordToHtml (period, subject, word_name, word_url) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/word/parse_word_card',
        port: server.port,
    });
    const params = {
        period, subject, word_name, word_url
    }
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);

    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function pptxToImages(params) {
    let uri = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/file/ppt_to_image',
        // search: queryStr,
    });
    // const params = {
    //     period: resource.period || '',
    //     subject: resource.subject || '',
    //     file_name: resource.name || '',
    //     file_url: resource.url
    // }
    let result = [];
    try {
        const response = await axios.post(uri, params);
        if (response && response.data) {
            for (const img of response.data) {
                result.push(img.url);
            }
        }
    } catch (e) {
        logger.error(e);
    }
    return result;
}


/**
 * 教师计划试卷推荐
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=29688486
 * @param params
 * @returns {Promise<unknown>}
 */
async function recommendPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/organize_paper',
        port: ALGO_SERVER.port,
    });
    let options = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, options);
    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return result;
}

/**
 * 考后巩固组卷（简单卷，中等卷，培优卷）
 * @param params
 * @returns {Promise<*>}
 */
async function levelPaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/level_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0 || !result.data.data) {
        logger.error(`算法考后巩固组卷失败,url:[${url}]`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 薄弱知识点组卷
 * @param params
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function detailTablePaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/detail_table/paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function mergeFile(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/files/process',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function recommendQuestions(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/questions',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

