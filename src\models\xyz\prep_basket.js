const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');


module.exports = {
    getBasketSimple,
    getBasketDetail,
    updateBasket,
}

async function getBasketSimple(params) {
    const {user_id, period, subject, type_id} = params;
    const basket = await db.collection(schema.user_xyz_prep_basket).findOne({user_id, period, subject, type_id});
    if (_.isEmpty(basket)) {
        return [];
    }
    const ids = (basket.children || []).map(e => e.id);
    if (!_.size(ids)) {
        return [];
    }
    const list = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).project({_id: 1, name: 1}).toArray();
    const result = [];
    for (const id of ids) {
        const data = list.find(e => e._id.toString() === id);
        if (_.isEmpty(data)) continue;
        result.push({
            id,
            name: data.name
        });
    }
    return result;
}

async function getBasketDetail(params) {
    const {user_id, type_id, period, subject } = params;
    const basket = await db.collection(schema.user_xyz_prep_basket).findOne({user_id, type_id, period, subject});
    const result = [];
    if (_.isEmpty(basket) || !_.size(basket.children)) return result;
    const ids = basket.children.map(e => e.id);
    const list = await db.zyk_collection(schema.zyk.prep_element).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    for (const id of ids) {
        const element = list.find(e => e._id.toString() === id);
        if (_.isEmpty(element)) continue;
        const data = _.assign({}, element);
        data.id = id;
        delete data._id;
        delete data.valid;
        data.utime = data.utime.getTime();
        data.ctime = data.ctime.getTime();
        result.push(data);
    }
    return result;
}

async function updateBasket(params) {
    const {user_id, period, subject, type_id} = params;
    const basket = await db.collection(schema.user_xyz_prep_basket).findOne({user_id, type_id, period, subject});
    const date = new Date();
    if (_.isEmpty(basket)) {
        const doc = {
            ...params,
            valid: enums.BOOL.YES,
            ctime: date,
            utime: date
        }
        await db.collection(schema.user_xyz_prep_basket).insertOne(doc);
    } else {
        const doc = {
            utime: date,
            children: params.children
        }
        await db.collection(schema.user_xyz_prep_basket).updateOne({_id: basket._id}, {$set: doc});
    }
    return await getBasketSimple(params);
}


