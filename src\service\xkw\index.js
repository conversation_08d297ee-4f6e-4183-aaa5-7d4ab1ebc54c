const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/xkw');

module.exports = {
    getAuthUrl,
    authCallback,
    syncBasketQuestions,
}

const JOI_GET_AUTH = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
});

async function getAuthUrl(req, res) {
    const params = await JOI_GET_AUTH.validateAsync(req.query);
    params.user = req.user;
    params.unify_sid = req.cookies['unify_sid'];
    // const params = {unify_sid: req.cookies['unify_sid'], user: req.user};
    return await model.getAuthUrl(params);
}

async function authCallback(req, res) {
    const params = req.query;
    await model.authCallback(req, res, params);
}

const JOI_SYNC_BASKET = Joi.object({
    open_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function syncBasketQuestions(req, res) {
    const params = await JOI_SYNC_BASKET.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.syncBasketQuestions(params);
}
