/**
 * 测试 get_paper_public_by_content 接口
 */

const testData = {
    data: {
        content: [
            {
                type: 'question',
                content: {
                    period: '高中',
                    subject: '数学',
                    grade: '高一',
                    type: '单选题',
                    description: '这是一道测试题目',
                    comment: '这是题目解析',
                    blocks: {
                        types: ['单选题'],
                        explanations: ['这是解析内容'],
                        solutions: ['这是解答过程'],
                        answers: ['A'],
                        stems: [
                            {
                                stem: '下列哪个选项是正确的？',
                                options: {
                                    A: '选项A',
                                    B: '选项B', 
                                    C: '选项C',
                                    D: '选项D'
                                }
                            }
                        ],
                        knowledges: [
                            [
                                {
                                    id: 1001,
                                    name: '函数',
                                    chance: 0.8,
                                    score: 5,
                                    know_methods: [
                                        {
                                            id: 2001,
                                            name: '函数性质',
                                            coefficient: 1.0
                                        }
                                    ],
                                    targets: [
                                        {
                                            id: 3001,
                                            name: '理解应用',
                                            coefficient: 1.0
                                        }
                                    ]
                                }
                            ]
                        ]
                    }
                },
                children: []
            },
            {
                type: 'question',
                content: {
                    period: '高中',
                    subject: '数学',
                    grade: '高一',
                    type: '填空题',
                    description: '这是第二道测试题目',
                    comment: '这是第二题解析',
                    blocks: {
                        types: ['填空题'],
                        explanations: ['填空题解析'],
                        solutions: ['填空题解答'],
                        answers: [['答案1', '答案2']],
                        stems: [
                            {
                                stem: '请填入正确答案：______'
                            }
                        ],
                        knowledges: [
                            [
                                {
                                    id: 1002,
                                    name: '代数',
                                    chance: 0.9,
                                    score: 4,
                                    know_methods: [
                                        {
                                            id: 2002,
                                            name: '代数运算',
                                            coefficient: 1.2
                                        }
                                    ],
                                    targets: [
                                        {
                                            id: 3002,
                                            name: '计算应用',
                                            coefficient: 0.8
                                        }
                                    ]
                                }
                            ]
                        ]
                    }
                },
                children: []
            }
        ]
    }
};

console.log('测试数据结构:');
console.log(JSON.stringify(testData, null, 2));

console.log('\n接口调用示例:');
console.log('POST /user_paper/public/content');
console.log('Content-Type: application/json');
console.log('Body:', JSON.stringify(testData, null, 2));

console.log('\n预期返回结果:');
console.log('- 包含完整的试卷结构 (basket)');
console.log('- 包含两道题目，分别为单选题和填空题');
console.log('- 试卷结构包含 volumes, blocks, questions 等字段');
console.log('- 不会插入数据库，直接返回试卷结构');

module.exports = testData;
