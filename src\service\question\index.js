const Joi = require('@hapi/joi');
const question = require('../../models/question');
const _ = require('lodash');

module.exports = {
    getFilters,
    search,
    search2,
    getAnswerImage,
    getAnswer,
    getKnowledge,
    getQuestionSame,
    getQuestionReco,
    updateQuestion,
    getQuestionDetail,
}

async function getFilters() {
    return await question.getFilters();
}

const JOI_POST_SEARCH = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    difficulty: Joi.string().optional(),
    exam_type: Joi.string().optional(),
    knowledges: Joi.string().optional(),
    sort_by: Joi.string().optional().default('integrated'),
    type: Joi.string().optional(),
    set_mode: Joi.object().optional(),
    filter_mkp: Joi.string().optional().default('true'),
    year: Joi.string().optional(),
    provinces: Joi.array().optional(), // name:string , cities:[]
    multi_or: Joi.array().optional(), // 章节、知识点综合查询
});

async function search(req, res) {
    const params = await JOI_POST_SEARCH.validateAsync(req.body);
    if (params.year && params.year === 'other') { //
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        }
        params.year = temp.join(',');
    }
    return question.search(params);
}

const JOI_POST_SEARCH2 = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    query: Joi.string().optional().allow(''),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    difficulty: Joi.string().optional(),
    exam_cate: Joi.array().items(Joi.string()).optional(),
    knows: Joi.array().items(Joi.number()).optional(),
    sort_by: Joi.string().optional().default('integrated'),
    type: Joi.string().optional(),
    set_mode: Joi.object().optional(),
    filter_mkp: Joi.string().optional().default('true'),
    // year: Joi.array().items(Joi.number()).optional(),
    year: Joi.string().optional(),
    // provinces: Joi.array().optional(), // name:string , cities:[]
    province: Joi.array().items(Joi.string()).optional(), // name:string , cities:[]
    city: Joi.array().items(Joi.string()).optional(), // name:string , cities:[]
    multi_or: Joi.array().optional(), // 章节、知识点综合查询
});

async function search2(req, res) {
    const params = await JOI_POST_SEARCH2.validateAsync(req.body);
    if (params.year && params.year === 'other') { //
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        }
        params.year = temp;
    } else {
        if (params.year) params.year = params.year.split(',').map(e => Number(e));
    }
    return await question.search2(params);
}

const JOI_GET_ANSWER_IMAGE = Joi.object({
    question_id: Joi.number().required(),
});

async function getAnswerImage(req, res) {
    const params = await JOI_GET_ANSWER_IMAGE.validateAsync(req.params);
    return await question.getAnswerImage(params);
}

async function getAnswer(req, res) {
    const params = await JOI_GET_ANSWER_IMAGE.validateAsync(req.params);
    return await question.getAnswer(params);
}

const JOI_GET_KNOWLEDGE = Joi.object({
    question_id: Joi.number().required(),
});

async function getKnowledge(req, res) {
    const params = await JOI_GET_KNOWLEDGE.validateAsync(req.params);
    return await question.getKnowledge(params);
}

const JOI_GET_SAME = Joi.object({
    question_id: Joi.alternatives().try(
        Joi.number().required(),
        Joi.string().required(),
    ).required(),
    change_times: Joi.number().required(),
});

async function getQuestionSame(req, res) {
    const params = await JOI_GET_SAME.validateAsync(_.assign(req.params, req.query));
    return await question.getQuestionSame(params);
}

const JOI_GET_RECO = Joi.object({
    question_id: Joi.number().required(),
});


async function getQuestionReco(req, res) {
    const params = await JOI_GET_RECO.validateAsync(req.params);
    return await question.getQuestionReco(params);
}

const JOI_UPDATE = Joi.object({
    question_id: Joi.string().required(),
    blocks: Joi.object({
        answers: Joi.array().items(Joi.alternatives().try( // 答案
            Joi.string().allow(''),
            Joi.array().items(Joi.string().allow(''))
        )).optional(),
        explanations: Joi.array().items(Joi.string().allow('')).optional(), // 解析
        solutions: Joi.array().items(Joi.string().allow('')).optional(), // 解答
    }),
    zujuanId: Joi.string().required(),
});

async function updateQuestion(req, res) {
    const params = await JOI_UPDATE.validateAsync(_.assign(req.params, req.body));
    return await question.updateQuestion(params, req.user);
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.alternatives().try( // 答案
        Joi.number(),
        Joi.string(),
    ).required(),
    paper_id: Joi.string().optional().allow(''),
});

async function getQuestionDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(_.assign(req.params, req.query));
    return await question.getQuestionDetail(params, req.user);
}



const JOI_BATCH_CREATE_QUESTION = Joi.object({
    questions: Joi.array().items(Joi.object({
        difficulty: Joi.string().required(),
        blocks: Joi.object({
            answers: Joi.array().items(Joi.alternatives().try( // 答案
                Joi.string().allow(''),
                Joi.array().items(Joi.string().allow(''))
            )).optional(),
            explanations: Joi.array().items(Joi.string().allow('')).optional(), // 解析
            solutions: Joi.array().items(Joi.string().allow('')).optional(), // 解答
        }),
    })),
});

async function batchCreateQuestion(req, res) {
    const params = await JOI_BATCH_CREATE_QUESTION.validateAsync(req.body);
    return await question.batchCreateQuestion(params, req.user);
}
