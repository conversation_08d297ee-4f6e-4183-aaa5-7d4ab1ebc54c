const Joi = require('@hapi/joi');
const question = require('../../models/question');
const _ = require('lodash');

module.exports = {
    getFilters,
    search,
    search2,
    getAnswerImage,
    getAnswer,
    getKnowledge,
    getQuestionSame,
    getQuestionReco,
    updateQuestion,
    getQuestionDetail,
    batchCreateQuestion,
    getDetailByAiTaskId,
}

async function getFilters() {
    return await question.getFilters();
}

const JOI_POST_SEARCH = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    difficulty: Joi.string().optional(),
    exam_type: Joi.string().optional(),
    knowledges: Joi.string().optional(),
    sort_by: Joi.string().optional().default('integrated'),
    type: Joi.string().optional(),
    set_mode: Joi.object().optional(),
    filter_mkp: Joi.string().optional().default('true'),
    year: Joi.string().optional(),
    provinces: Joi.array().optional(), // name:string , cities:[]
    multi_or: Joi.array().optional(), // 章节、知识点综合查询
});

async function search(req, res) {
    const params = await JOI_POST_SEARCH.validateAsync(req.body);
    if (params.year && params.year === 'other') { //
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        }
        params.year = temp.join(',');
    }
    return question.search(params);
}

const JOI_POST_SEARCH2 = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    query: Joi.string().optional().allow(''),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    difficulty: Joi.string().optional(),
    exam_cate: Joi.array().items(Joi.string()).optional(),
    knows: Joi.array().items(Joi.number()).optional(),
    sort_by: Joi.string().optional().default('integrated'),
    type: Joi.string().optional(),
    set_mode: Joi.object().optional(),
    filter_mkp: Joi.string().optional().default('true'),
    // year: Joi.array().items(Joi.number()).optional(),
    year: Joi.string().optional(),
    // provinces: Joi.array().optional(), // name:string , cities:[]
    province: Joi.array().items(Joi.string()).optional(), // name:string , cities:[]
    city: Joi.array().items(Joi.string()).optional(), // name:string , cities:[]
    multi_or: Joi.array().optional(), // 章节、知识点综合查询
});

async function search2(req, res) {
    const params = await JOI_POST_SEARCH2.validateAsync(req.body);
    if (params.year && params.year === 'other') { //
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        }
        params.year = temp;
    } else {
        if (params.year) params.year = params.year.split(',').map(e => Number(e));
    }
    return await question.search2(params);
}

const JOI_GET_ANSWER_IMAGE = Joi.object({
    question_id: Joi.number().required(),
});

async function getAnswerImage(req, res) {
    const params = await JOI_GET_ANSWER_IMAGE.validateAsync(req.params);
    return await question.getAnswerImage(params);
}

async function getAnswer(req, res) {
    const params = await JOI_GET_ANSWER_IMAGE.validateAsync(req.params);
    return await question.getAnswer(params);
}

const JOI_GET_KNOWLEDGE = Joi.object({
    question_id: Joi.number().required(),
});

async function getKnowledge(req, res) {
    const params = await JOI_GET_KNOWLEDGE.validateAsync(req.params);
    return await question.getKnowledge(params);
}

const JOI_GET_SAME = Joi.object({
    question_id: Joi.alternatives().try(
        Joi.number().required(),
        Joi.string().required(),
    ).required(),
    change_times: Joi.number().required(),
});

async function getQuestionSame(req, res) {
    const params = await JOI_GET_SAME.validateAsync(_.assign(req.params, req.query));
    return await question.getQuestionSame(params);
}

const JOI_GET_RECO = Joi.object({
    question_id: Joi.number().required(),
});


async function getQuestionReco(req, res) {
    const params = await JOI_GET_RECO.validateAsync(req.params);
    return await question.getQuestionReco(params);
}

const JOI_UPDATE = Joi.object({
    question_id: Joi.string().required(),
    blocks: Joi.object({
        answers: Joi.array().items(Joi.alternatives().try( // 答案
            Joi.string().allow(''),
            Joi.array().items(Joi.string().allow(''))
        )).optional(),
        explanations: Joi.array().items(Joi.string().allow('')).optional(), // 解析
        solutions: Joi.array().items(Joi.string().allow('')).optional(), // 解答
    }),
    zujuanId: Joi.string().required(),
});

async function updateQuestion(req, res) {
    const params = await JOI_UPDATE.validateAsync(_.assign(req.params, req.body));
    return await question.updateQuestion(params, req.user);
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.alternatives().try( // 答案
        Joi.number(),
        Joi.string(),
    ).required(),
    paper_id: Joi.string().optional().allow(''),
});

async function getQuestionDetail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(_.assign(req.params, req.query));
    return await question.getQuestionDetail(params, req.user);
}



const JOI_BATCH_CREATE_QUESTION = Joi.array().items(Joi.object({
    subject: Joi.string().required(),                           // 科目
    period: Joi.string().required(),                            // 学段
    description: Joi.string().optional().allow(''),             // 各小题的公共题干部分
    comment: Joi.string().optional().allow(''),                 // 试题点评
    difficulty: Joi.number().optional(),                        // 难度
    type: Joi.string().required(),                              // 试题类型
    task_id: Joi.string().optional(),                           // 任务id

    blocks: Joi.object({
        types: Joi.array().items(Joi.string()).required(),      // 内部试题类型：单选题，多选题，填空题，解答题
        explanations: Joi.array().items(Joi.string().allow('')).required(), // 解析：一维数组
        solutions: Joi.array().items(Joi.string().allow('')).required(),    // 解答：一维数组
        answers: Joi.array().items(Joi.alternatives().try(      // 答案
            Joi.string().allow(''),                             // 单选题或解答题：字符串
            Joi.array().items(Joi.string().allow(''))           // 多选题或填空题：数组
        )).required(),

        stems: Joi.array().items(Joi.object({
            options: Joi.object().optional(),
            stem: Joi.string().required()                       // 题干
        })).required(),

        knowledges: Joi.array().items(                          // 非必有字段，每个小题的子知识点
            Joi.array().items(Joi.object({
                id: Joi.number().required(),                    // 知识点id
                name: Joi.string().required(),                  // 知识点名称
                chance: Joi.number().required(),                // 概率
                know_methods: Joi.array().items(Joi.object({    // 考法/细分
                    id: Joi.number().required(),
                    name: Joi.string().required(),
                    coefficient: Joi.number().required()        // 系数
                })).optional(),
                targets: Joi.array().items(Joi.object({         // 对象
                    id: Joi.number().required(),
                    name: Joi.string().required(),
                    coefficient: Joi.number().required()        // 系数
                })).optional()
            }))
        ).optional()
    }).required().unknown(true),

    knowledges: Joi.array().items(Joi.object({                  // 整体知识点
        id: Joi.number().required(),                            // 知识点id
        name: Joi.string().required(),                          // 知识点名称
        chance: Joi.number().required(),                        // 概率
        know_methods: Joi.array().items(Joi.object({            // 考法
            id: Joi.number().required(),
            name: Joi.string().required(),
            coefficient: Joi.number().required()                // 系数
        })).optional(),
        targets: Joi.array().items(Joi.object({                 // 对象
            id: Joi.number().required(),
            name: Joi.string().required(),
            coefficient: Joi.number().required()                // 系数
        })).optional()
    })).optional()
}).unknown(true));

async function batchCreateQuestion(req, res) {
    const params = await JOI_BATCH_CREATE_QUESTION.validateAsync(req.body);
    return await question.batchCreateQuestion(params, req.user);
}
const JOI_GET_DETAIL_BY_AI_TASK_ID = Joi.object({
    id: Joi.string().required(),
});

async function getDetailByAiTaskId(req, res) {
    const params = await JOI_GET_DETAIL_BY_AI_TASK_ID.validateAsync(req.query);
    return await question.getDetailByAiTaskId(params);
}

