const os = require('os');
const config = require('config');
const _ = require('lodash');
const { v4: uuid} = require('uuid');
const enums = require('../enums/enums')

module.exports = {
    getLocalAddressAndPort,
    matchNumber,
    getAcademicYear,
    getTimestampSeconds,
    getUUid,
    getClientIp,
    getSemesterAndYear,
    flattenTree,
    findOuterParentId,
    findRootId,
    numberToChinese,
    getStandardSubject,
    maskPhoneNumber,
}

function getLocalAddressAndPort () {
    const interfaces = os.networkInterfaces();
    const addresses = [];
    for (var k in interfaces) {
        for (var k2 in interfaces[k]) {
            var ad = interfaces[k][k2];
            if (ad.family === 'IPv4' && !ad['internal']) {
                addresses.push(ad.address);
            }
        }
    }
    const ip = _.isEmpty(addresses) ? '127.0.0.1' : addresses[0];
    const port = config.app.port;
    return ip + ':' + port;
}

function matchNumber(str, no_match_value = Number.MAX_SAFE_INTEGER) {
    const arr = str.match(/\d+/g);
    if (arr && arr.length) {
        return parseInt(arr[0]);
    }
    return no_match_value;
}

/**
 * 获取学年
 * @param date
 * @returns {{to_year: number, from_year: number}}
 */
function getAcademicYear(date = new Date()) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月份从0开始，所以加1

    // 学年从8月开始
    const result = {
        from_year: year,
        to_year: year
    };
    if (month >= 8) {
        result.to_year = year + 1;
    } else {
        result.from_year = year - 1;
    }
    return result;
}

/**
 * 获取当前学年、学期
 * @param date
 * @return {{to_year: number, from_year: number, semester: string}}
 */
function getSemesterAndYear(date = new Date()) {
    const currentYear = date.getFullYear();
    const currentMonth = date.getMonth() + 1; // 月份从0开始，所以+1
    const currentDay = date.getDate();
    let semester, from_year, to_year;
    // 8月8号~1月20号上学期，其余时间下学期
    if (
        (currentMonth === 8 && currentDay >= 8) ||
        (currentMonth >= 9 && currentMonth <= 12) ||
        (currentMonth === 1 && currentDay <= 20)
    ) {
        semester = '上学期';
        // 如果是8月到12月，学年是当前年-次年，如2023-2024
        // 如果是1月，学年是前一年-当前年，如2023-2024
        if (currentMonth >= 8) {
            from_year = currentYear;
            to_year = currentYear + 1;
        } else {
            from_year = currentYear - 1;
            to_year = currentYear;
        }
    } else {
        semester = '下学期';
        from_year = currentYear - 1;
        to_year = currentYear;
    }
    return {
        semester,
        from_year,
        to_year
    };
}

/**
 * 获取时间戳(秒)
 * @returns {number}
 */
function getTimestampSeconds() {
    return Math.floor(Date.now() / 1000);
}

function getUUid() {
    return uuid().replace(/-/g, '');
}


function getClientIp(req) {
    let ip = req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || req.ip || req.connection.remoteAddress || '';
    ip = ipv6ToV4(ip);
    return ip;
}

function ipv6ToV4(ip) {
    if (ip.split(',').length > 0) {
        ip = ip.split(',')[0]
    }
    ip = ip.substr(ip.lastIndexOf(':') + 1, ip.length);
    return ip
}

function flattenTree(data) {
    let result = [];
    if (data && data.length > 0) {
        for (const child of data) {
            result.push(child);
            result = result.concat(flattenTree(child.children));
            // delete child.children;
        }
    }
    return result;
}

/**
 * 查找上一层父节点ID
 * @param treeNodes
 * @param targetId
 * @param parentId
 * @return {null|*}
 */
function findOuterParentId(treeNodes, targetId, parentId = null) {
    for (const node of treeNodes) {
        // 如果当前节点有id且匹配目标id，则返回父级id
        if (node.id && node.id.toString() === targetId.toString()) {
            return parentId || targetId;
        }

        // 如果有子节点，递归查找
        if (node.children && node.children.length > 0) {
            const result = findOuterParentId(node.children, targetId, node.id || parentId);
            if (result !== null) {
                return result;
            }
        }
    }
    return null;
}

/**
 * 查找根节点ID
 * @param tree
 * @param targetId
 * @return {null|*}
 */
function findRootId(tree, targetId) {
    const parentMap = new Map();

    // 构建父节点映射表
    function traverse(nodes, parent) {
        for (const node of nodes) {
            parentMap.set(node.id, parent);
            if (node.children) {
                traverse(node.children, node);
            }
        }
    }

    traverse(tree, null); // 初始化遍历，根节点的父节点为null

    // 检查目标ID是否存在
    if (!parentMap.has(targetId)) {
        return null; // 或者根据需求抛出错误
    }

    let currentNodeId = targetId;
    let parentNode = parentMap.get(currentNodeId);

    // 向上查找直到父节点为null（即最外层节点）
    while (parentNode !== null) {
        currentNodeId = parentNode.id;
        parentNode = parentMap.get(currentNodeId);
    }

    return currentNodeId;
}

const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
const units = ['', '十', '百', '千'];
const bigUnits = ['', '万', '亿', '兆'];

function numberToChinese(num) {
    if (typeof num !== 'number') {
        throw new Error('输入必须为数字');
    }

    // 处理负数
    if (num < 0) {
        return '负' + numberToChinese(-num);
    }

    // 处理小数
    if (num % 1 !== 0) {
        const parts = num.toString().split('.');
        const integerPart = parseInt(parts[0]);
        const decimalPart = parts[1];

        let result = integerPart !== 0 ? numberToChinese(integerPart) : '零';
        result += '点';

        for (let i = 0; i < decimalPart.length; i++) {
            result += digits[parseInt(decimalPart[i])];
        }

        return result;
    }

    // 处理0
    if (num === 0) {
        return digits[0];
    }

    let str = '';
    let unitPos = 0;
    let needZero = false;

    while (num > 0) {
        const section = num % 10000;
        if (needZero) {
            str = digits[0] + str;
        }

        const sectionStr = convertSection(section);
        if (section !== 0) {
            str = sectionStr + bigUnits[unitPos] + str;
        } else {
            str = sectionStr + str;
        }

        needZero = section < 1000 && section > 0;
        num = Math.floor(num / 10000);
        unitPos++;
    }

    // 处理"一十"开头的特殊情况
    if (str.startsWith('一十')) {
        str = str.substring(1);
    }

    return str;
}

function convertSection(section) {
    let str = '';
    let unitPos = 0;
    let zero = true;

    while (section > 0) {
        const digit = section % 10;
        if (digit === 0) {
            if (!zero) {
                zero = true;
                str = digits[0] + str;
            }
        } else {
            zero = false;
            str = digits[digit] + units[unitPos] + str;
        }
        unitPos++;
        section = Math.floor(section / 10);
    }

    return str;
}


function getStandardSubject(period, subject) {
    let result = null;
    for (let key in enums.SubjectRules) {
        if (key === subject) {
            result = subject;
            break;
        }
        const rules = enums.SubjectRules[key];
        if (rules.includes(subject)) {
            result = key;
            break;
        }
    }
    if (period === '小学' && subject === '政治') result = '道德与法治';
    return result;
}

function maskPhoneNumber(phoneNumber) {
    return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}
