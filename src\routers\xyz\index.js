const express = require('express');
const router = express.Router();
const service = require('../../service/xyz');
const resource_service = require('../../service/xyz/resource');
const user_prep_catalog_service = require('../../service/xyz/user_prep_catalog');
const user_prep_resource_service = require('../../service/xyz/user_prep_resource');
const user_prep_basket = require('../../service/xyz/prep_basket');
const { handler } = require('../../../common/middlewares/request');

// 获取学段信息
router.get('/study_info', handler(1, service.getStudyInfo));
// 教材及版本信息
router.get('/books/catalog/info', handler(1, service.getCatalogInfo));
// 教材详细
router.get('/books/:book_id', handler(1, service.getBookDetail));
// 获取自定义tab
router.get('/custom', handler(1, service.getCustomResource));

// 资源相关
// 获取目录资源
router.get('/prep/resource/list', handler(1, resource_service.getResourceList));
// 获取资源详细
router.get('/prep/resource/info', handler(1, resource_service.getResourceDetail));
// 下载文件
router.post('/prep/resource/download', handler(1, resource_service.downloadResource));

// 用户相关
// 获取我目录
router.get('/user/prep/catalog', handler(1, user_prep_catalog_service.getUserPrepCatalog));

router.post('/user/prep/catalog', handler(1, user_prep_catalog_service.updateUserPrepCatalog));

// ========== 组案篮相关
// 获取基本信息
router.get('/prep/basket/resource', handler(1, user_prep_basket.getBasketSimple));
// 获取详细
router.get('/prep/basket/resource/detail', handler(1, user_prep_basket.getBasketDetail));
// 更新
router.post('/prep/basket/resource', handler(1, user_prep_basket.updateBasket));

// ========== 我的备课资源相关
// 用户资源
router.get('/user/prep/resource/list', handler(1, user_prep_resource_service.getResourceList));
// 获取添加到备课资源ids
router.get('/user/prep/resource/keys', handler(1, user_prep_resource_service.getResourceKeys));
// 编辑资源基本信息
router.put('/user/prep/resource/base', handler(1, user_prep_resource_service.updateResourceBaseInfo));

// 编辑或添加自定义备课资源
router.post('/user/prep/resource/custom', handler(1, user_prep_resource_service.saveOrUpdateCustomResource));
// 添加系统备课资源
router.post('/user/prep/resource', handler(1, user_prep_resource_service.addResource));
// 删除资源
router.delete('/user/prep/resource', handler(1, user_prep_resource_service.deleteResource));

module.exports = router;
