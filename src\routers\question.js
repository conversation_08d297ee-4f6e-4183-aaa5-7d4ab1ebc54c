'use strict';
const express = require('express');
const router = express.Router();
const question = require('../service/question');
const { handler } = require('../../common/middlewares/request');

// 时期筛选项
router.get('/filters', handler(1, question.getFilters));
// 试题搜索
router.post('/by_search', handler(1, question.search));
router.post('/by_search2', handler(1, question.search2));
// 获取试题详细
router.get('/:id', handler(1, question.getQuestionDetail));
// 获取试题解析解答图片
router.get('/:question_id/answer', handler(1, question.getAnswer));
// 获取试题知识点
router.get('/:question_id/knowledge', handler(1, question.getKnowledge));
// 换一题
router.get('/:question_id/same', handler(1, question.getQuestionSame));
// 相似题
router.get('/:question_id/reco', handler(1, question.getQuestionReco));
// 编辑解析解答
router.put('/:question_id', handler(1, question.updateQuestion));
// 编辑解析解答
router.post('/batch', handler(1, question.batchCreateQuestion));

module.exports = router;
