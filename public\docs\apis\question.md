---
title: 题目管理接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 题目管理接口

题目管理接口提供试题搜索、详情查看、编辑和相关推荐功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取筛选项

GET /v1/question/filters

获取题目搜索的筛选项配置，包括学段、科目、难度、题型等选项。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/filters" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "小学": {
      "period": "小学",
      "subjects": {
        "语文": {
          "subject": "语文",
          "difficulties": ["容易", "较易", "中等", "较难", "困难"],
          "exam_types": ["单元测试", "期中考试", "期末考试", "中考"],
          "question_types": ["选择题", "填空题", "阅读理解", "作文"],
          "question_template": [
            {
              "id": "template_001",
              "name": "标准模板",
              "period": "小学",
              "subject": "语文"
            }
          ]
        },
        "数学": {
          "subject": "数学",
          "difficulties": ["容易", "较易", "中等", "较难", "困难"],
          "exam_types": ["单元测试", "期中考试", "期末考试"],
          "question_types": ["选择题", "填空题", "计算题", "应用题"]
        }
      }
    },
    "初中": {
      "period": "初中",
      "subjects": {
        "数学": {
          "subject": "数学",
          "difficulties": ["容易", "较易", "中等", "较难", "困难"],
          "exam_types": ["单元测试", "期中考试", "期末考试", "中考"],
          "question_types": ["选择题", "填空题", "解答题"]
        }
      }
    }
  }
}
```

---

## POST 试题搜索

POST /v1/question/by_search

根据条件搜索试题，支持复杂的筛选和排序。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|limit|body|number|是|每页数量，最大100|
|offset|body|number|是|偏移量，从0开始|
|period|body|string|是|学段|
|subject|body|string|是|科目|
|difficulty|body|string|否|难度|
|exam_type|body|string|否|考试类型|
|knowledges|body|string|否|知识点，多个用逗号分隔|
|sort_by|body|string|否|排序方式，默认"integrated"|
|type|body|string|否|题目类型|
|set_mode|body|object|否|组卷模式配置|
|filter_mkp|body|string|否|过滤标记，默认"true"|
|year|body|string|否|年份，特殊值"other"表示历年|
|provinces|body|array|否|省份信息|
|multi_or|body|array|否|多条件或查询|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/question/by_search" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 20,
    "offset": 0,
    "period": "初中",
    "subject": "数学",
    "difficulty": "中等",
    "sort_by": "integrated",
    "year": "2023",
    "knowledges": "有理数,运算法则"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 1256,
    "questions": [
      {
        "id": 12345,
        "type": "选择题",
        "difficulty": "中等",
        "content": "下列说法正确的是（）",
        "options": ["A. 有理数都是正数", "B. 0是自然数", "C. 负数都是有理数", "D. 整数包括正整数和负整数"],
        "period": "初中",
        "subject": "数学",
        "grade": "七年级",
        "knowledges": ["有理数", "数的分类"],
        "source": "2023年中考真题",
        "year": "2023",
        "province": "北京市",
        "exam_type": "中考",
        "score": 3,
        "create_time": "2023-06-15"
      }
    ],
    "facets": {
      "difficulty": {
        "容易": 156,
        "中等": 489,
        "较难": 234
      },
      "type": {
        "选择题": 567,
        "填空题": 345,
        "解答题": 344
      }
    }
  }
}
```

### 特殊参数说明

#### year参数特殊处理

当year参数为"other"时，系统会自动生成从2015年到当前年份减5年的年份列表。

例如当前年份2024年，other会转换为：`"year": "2015,2016,2017,2018,2019"`

---

## GET 获取试题详情

GET /v1/question/{id}

获取指定试题的详细信息，包括题目内容、答案、解析等。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|number/string|是|题目ID|
|paper_id|query|string|否|试卷ID，用于关联查询|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/12345?paper_id=paper_001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": 12345,
    "type": "选择题",
    "difficulty": "中等",
    "period": "初中",
    "subject": "数学",
    "grade": "七年级",
    "year": "2023",
    "source": "2023年北京中考",
    "blocks": {
      "question": "下列说法正确的是（）",
      "options": [
        "A. 有理数都是正数",
        "B. 0是自然数", 
        "C. 负数都是有理数",
        "D. 整数包括正整数和负整数"
      ],
      "answers": ["B"],
      "explanations": ["根据自然数的定义，0是自然数。有理数包括正有理数、负有理数和0。"],
      "solutions": ["分析各选项：A错误，有理数包括正数、负数和0；B正确，0是自然数；C错误，负数不都是有理数，如-π；D错误，整数还包括0。"]
    },
    "knowledges": [
      {
        "id": "k001",
        "name": "有理数概念",
        "level": 2
      },
      {
        "id": "k002", 
        "name": "自然数",
        "level": 1
      }
    ],
    "audio": null,
    "description": "考查学生对基本数学概念的理解",
    "comment": "基础概念题，重点考查分类思想"
  }
}
```

---

## GET 获取试题解析解答

GET /v1/question/{question_id}/answer

获取试题的解析和解答内容，主要用于教师查看标准答案。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|question_id|path|number|是|题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/12345/answer" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "blocks": {
      "answers": ["B"],
      "explanations": [
        "这道题考查学生对有理数、自然数等基本概念的掌握。",
        "根据数学定义：0是自然数，因此选项B正确。"
      ],
      "solutions": [
        "解答步骤：",
        "1. 分析选项A：有理数包括正有理数、负有理数和0，不都是正数，故A错误。",
        "2. 分析选项B：根据自然数定义，0∈N，故B正确。", 
        "3. 分析选项C：负数包括负有理数和负无理数，如-π是负无理数，故C错误。",
        "4. 分析选项D：整数包括正整数、负整数和0，故D错误。",
        "因此答案是B。"
      ]
    }
  }
}
```

---

## GET 获取试题知识点

GET /v1/question/{question_id}/knowledge

获取试题关联的知识点信息，用于知识点分析和学习路径规划。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|question_id|path|number|是|题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/12345/knowledge" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "blocks": {
      "knowledges": [
        {
          "id": "k001",
          "name": "有理数概念",
          "level": 2,
          "parent_id": "k000",
          "parent_name": "数与代数"
        },
        {
          "id": "k002",
          "name": "自然数定义",
          "level": 1,
          "parent_id": "k001", 
          "parent_name": "有理数概念"
        }
      ]
    },
    "knowledges": ["有理数", "自然数", "整数", "数的分类"],
    "knowledge_tree": {
      "primary": [
        {
          "name": "有理数",
          "weight": 0.8,
          "difficulty": "中等"
        }
      ],
      "secondary": [
        {
          "name": "自然数",
          "weight": 0.6,
          "difficulty": "容易"
        }
      ]
    }
  }
}
```

---

## GET 换一题

GET /v1/question/{question_id}/same

获取与当前题目相似的其他题目，实现换题功能。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|question_id|path|number/string|是|题目ID|
|change_times|query|number|是|换题次数，用于去重|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/12345/same?change_times=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total_num": 8,
    "questions": [
      {
        "id": 12346,
        "type": "选择题",
        "difficulty": "中等",
        "content": "关于数的分类，下列说法错误的是（）",
        "options": [
          "A. 正整数是自然数",
          "B. 0是整数",
          "C. 负分数是有理数", 
          "D. 无理数都是负数"
        ],
        "similarity": 0.92,
        "match_reason": "同样考查数的分类概念"
      },
      {
        "id": 12347,
        "type": "选择题",
        "difficulty": "中等", 
        "content": "在下列数中，既是有理数又是整数的是（）",
        "similarity": 0.85,
        "match_reason": "考查有理数和整数的关系"
      }
    ]
  }
}
```

---

## GET 相似题推荐

GET /v1/question/{question_id}/reco

获取与当前题目相关的推荐题目，用于拓展练习。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|question_id|path|number|是|题目ID|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/question/12345/reco" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "questions": [
      {
        "id": 12348,
        "type": "填空题",
        "difficulty": "中等",
        "content": "在数轴上，与原点距离为3的点表示的数是______。",
        "relevance": "高相关",
        "reason": "都涉及有理数概念",
        "knowledge_overlap": 0.7
      },
      {
        "id": 12349,
        "type": "解答题",
        "difficulty": "较难",
        "content": "已知a是有理数，b是无理数，判断a+b的性质。",
        "relevance": "中相关",
        "reason": "有理数和无理数的性质",
        "knowledge_overlap": 0.5
      }
    ],
    "recommendation_strategy": "基于知识点相似度和难度梯度",
    "total_related": 15
  }
}
```

---

## PUT 编辑解析解答

PUT /v1/question/{question_id}

编辑试题的解析和解答内容，限教师权限使用。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|question_id|path|string|是|题目ID|
|blocks|body|object|是|题目内容块|
|» answers|body|array|否|答案数组，可以是字符串或字符串数组|
|» explanations|body|array|否|解析内容数组，允许空字符串|
|» solutions|body|array|否|解答过程数组，允许空字符串|
|zujuanId|body|string|是|组卷ID，用于权限验证|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/question/12345" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "blocks": {
      "answers": ["B"],
      "explanations": [
        "更新后的解析：这道题考查基本概念的理解。",
        "根据定义，0属于自然数集合。"
      ],
      "solutions": [
        "详细解答过程：",
        "1. 分析有理数的定义和分类",
        "2. 判断各选项的正确性",
        "3. 得出正确答案"
      ]
    },
    "zujuanId": "zj_001"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "success": true,
    "question_id": "12345",
    "updated_fields": ["explanations", "solutions"],
    "update_time": "2024-01-15 14:30:00",
    "operator": "teacher_001"
  }
}
```

### 错误示例

#### 参数验证失败

```json
{
  "code": 3,
  "msg": "参数错误：zujuanId是必需的",
  "data": ""
}
```

#### 权限不足

```json
{
  "code": 2,
  "msg": "无权限编辑此题目",
  "data": ""
}
```

---

## 通用错误码

|错误码|含义|说明|
|---|---|---|
|0|成功|请求处理成功|
|1|URL错误|接口路径不存在|
|2|认证错误|身份认证失败或权限不足|
|3|参数错误|请求参数格式不正确或缺少必需参数|
|4|业务错误|业务逻辑处理错误|
|5|空数据|查询不到相关数据|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 搜索算法

试题搜索使用多维度匹配算法：
1. **基础匹配**：学段、科目、年级等基础信息匹配
2. **知识点匹配**：基于知识点标签的精确或模糊匹配
3. **相似度计算**：使用机器学习算法计算题目相似度
4. **质量评分**：综合考虑题目质量、使用频率等因素

### 推荐策略

相似题和换题功能使用不同的推荐策略：
- **换题**：高相似度匹配，保持难度和知识点基本一致
- **推荐**：适度扩展，包含相关知识点和递进难度

### 权限控制

- 试题查看：所有认证用户
- 试题编辑：仅限教师用户，需要组卷权限验证