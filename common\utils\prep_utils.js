
const enums = require('../enums/enums');

module.exports = {
    getTypeIdByType,
}
function getTypeIdByType(type) {
    let type_id = null;
    if (type === enums.PrepResourceType.learn) {
        type_id = enums.ResourceType.study_plan.type_id;
    } else if (type === enums.PrepResourceType.courseware) {
        type_id = enums.ResourceType.lesson_ware.type_id;
    } else if (type === enums.PrepResourceType.homework) {
        type_id = enums.ResourceType.homework.type_id
    } else if (type === enums.PrepResourceType.user_paper) {
        type_id = enums.ResourceType.user_paper.type_id
    }
    return type_id;
}
