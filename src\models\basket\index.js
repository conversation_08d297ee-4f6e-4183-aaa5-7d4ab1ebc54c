const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const path = require('path');
const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const paperUtils = require('../../../common/utils/paper_utils');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const questionModel = require('../question');
const zykModel = require('../zyk');

const basket_question_limit = 60;

module.exports = {
    get_questions,
    post_questions,
    delete_questions,
    get_basket,
    delete_basket,
    getQuestions,
}

async function get_questions(params) {
    const { category = enums.BasketCategory.BASKET, period, subject, user_id } = params;
    const questions = await db.collection(schema.user_basket).find({ user_id: user_id, category, period: period, subject: subject }).toArray();
    return paperUtils.get_profile_basket(user_id, questions);
}

async function post_questions(params) {
    const { category = enums.BasketCategory.BASKET, period, subject, questions, user_id } = params;
    const user_questions = await db.collection(schema.user_basket).find({ user_id: user_id, category, period: period, subject: subject }).toArray();
    const new_arr = questions.filter(e => {
        if (!_.size(user_questions)) return true;
        return !user_questions.find(uq => uq.id === e.id);
    });
    if (!_.size(new_arr))  return paperUtils.get_profile_basket(user_id, user_questions);
    const total_count = _.size(user_questions) + _.size(new_arr);
    if (total_count > basket_question_limit) {
        throw new BussError('您的账号已达数量上限');
    }
    // 保存试题
    const now = new Date();
    const insert_arr = [];
    for (const q of new_arr) {
        const data = _.assign({}, q);
        data.category = category;
        data.user_id = user_id;
        data.ctime = now;
        data.utime = now;
        if (!data.source) {
            data.source = enums.QuestionSource.SYS;
            data.source_id = data.id;
        }
        if (data.source === enums.QuestionSource.UPLOAD && data.id) {
            data.source = enums.QuestionSource.ZX;
            data.source_id = data.id;
        }
        insert_arr.push(data);
        user_questions.push(data);
    }
    await db.collection(schema.user_basket).insertMany(insert_arr);
    return paperUtils.get_profile_basket(user_id, user_questions);
}

async function delete_questions(params) {
    const { user_id, category = enums.BasketCategory.BASKET, period, subject, questions } = await params;
    let user_questions = await db.collection(schema.user_basket).find({ user_id: user_id, category, period: period, subject: subject }).toArray();
    if (!_.size(user_questions)) return [];
    const ids = [];
    for (const qid of questions) {
        const delete_data = user_questions.find(d => d.id === qid);
        if (delete_data) {
            user_questions = user_questions.filter(e => e.id !== delete_data.id);
            ids.push(delete_data._id);
        }
    }
    await db.collection(schema.user_basket).deleteMany({_id: {$in: ids}});
    return paperUtils.get_profile_basket(user_id, user_questions);
}

async function get_basket(params) {
    const { user_id, category = enums.BasketCategory.BASKET, period, subject, paper_id, op, source_type, template } = params;
    const basket = paperUtils.init(user_id);
    delete basket['_id'];
    basket.period = period;
    basket.subject = subject;
    let user_questions = await db.collection(schema.user_basket).find({ user_id: user_id, category, period: period, subject: subject }).toArray();
    if (paper_id) {
        if (op === 'delete') {
            // 删除同学段、科目数据
            await db.collection(schema.user_basket).deleteMany({user_id: user_id, category, period: period, subject: subject});
            user_questions = [];
        }
        const fun = HANDLER_PAPER[source_type];
        let { paper, questions }= await fun(paper_id, user_id);
        // 校验试题数量
        const new_questions = questions.filter( e => !(user_questions || []).find(uq => uq.id === e.id) );
        const total_count = _.size(user_questions) + _.size(new_questions);
        if (total_count > basket_question_limit) {
            throw new BussError('试题篮试题数量超过上限');
        }
        // 保存试题
        if (_.size(new_questions)) {
            const insert_questions = [];
            const now = new Date();
            for (const question of new_questions) {
                const insert_data = _.pick(question, ['id', 'type', 'period', 'subject', 'score', 'source', 'source_id']);
                insert_data.user_id = user_id;
                insert_data.category = category;
                insert_data.ctime = now;
                insert_data.utime = now;
                insert_questions.push(insert_data);
                user_questions.push(insert_data);
            }
            await db.collection(schema.user_basket).insertMany(insert_questions);
        }
        // 处理试题篮复用信息
        if (paper.name) {
            basket.name = paper.name + '(副本)';
        }
        if (paper.template) {
            basket.template = template;
        }
        if (paper.partsList) {
            basket.partsList = paper.partsList;
        }
        if (paper.grade) {
            basket.grade = paper.grade;
        }
        if (paper.type) {
            basket.type = paper.type;
        }
    }
    // 插入试题
    const kb_ids = [];
    const zx_ids = [];
    const zyk_ids = [];
    const ques_map = {};
    for (const question of user_questions) {
        if (question.source === enums.QuestionSource.UPLOAD) {
            ques_map[question.id] = question;
        } else if (question.source === enums.QuestionSource.ZX) {
            zx_ids.push(question.id);
        } else if (question.source === enums.QuestionSource.ZYK) {
            zyk_ids.push(question.id);
        } else {
            if (_.isNumber(question.id)) kb_ids.push(question.id);
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestionByIds(kb_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const questions = await questionModel.getByIds(zx_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zyk_ids)) {
        const questions = await zykModel.getQuestionByIds(zyk_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    for (const q of user_questions) {
        let question =ques_map[q.id];
        if (_.isEmpty(question)) continue;
        paperUtils.insert_questions(basket, q);
    }
    paperUtils.traverse_questions(basket, ques_map);
    paperUtils.render_basket(basket);
    return basket;
}

async function delete_basket(params) {
    const { user_id, category = enums.BasketCategory.BASKET, period, subject } = params;
    await db.collection(schema.user_basket).deleteMany({user_id: user_id, category, period: period, subject: subject});
    return [];
}

const HANDLER_PAPER = {
    [enums.PaperSourceType.SYS]: _handler_sys_paper,
    [enums.PaperSourceType.ASSEMBLE]: _handler_assemble_paper,
    [enums.PaperSourceType.UPLOAD]: _handler_assemble_paper,
    // [enums.PaperSourceType.UPLOAD]: _handler_upload_exampaper,
}


async function _handler_sys_paper(paper_id) {
    // 系统试卷库
    const paper = await client.kb.getPaperById(paper_id);
    const questions = [];
    for (const b of _.get(paper, 'blocks', [])) {
        for (const ques of b.questions) {
            questions.push(ques);
        }
    }
    return { paper, questions };
}

async function _handler_assemble_paper(paper_id, user_id) {
    const query = {
        _id: new ObjectId(paper_id),
        // user_id: user_id
    };
    const paper = await db.collection(schema.user_paper).findOne(query);
    if (_.isEmpty(paper) || paper.status !== enums.PaperStatus.DONE) {
        throw new BussError('组卷不存在');
    }
    const questions = [];
    for (const volume of _.get(paper, 'volumes', [])) {
        for (const block of _.get(volume, 'blocks', [])) {
            for (const question of _.get(block, 'questions', [])) {
                questions.push(question);
            }
        }
    }
    return { paper, questions };
}

/**
 * 获取当前学段科目下的所有试题
 * @param user_id
 * @param period
 * @param subject
 * @return {Promise<*>}
 */
async function getQuestions(user_id, period, subject, category = enums.BasketCategory.BASKET) {
    return await db.collection(schema.user_basket).find({ user_id: user_id, category, period: period, subject: subject }).toArray();
}


