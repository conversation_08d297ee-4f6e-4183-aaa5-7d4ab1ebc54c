const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/exampaper/paper_template');

module.exports = {
    getSysList,
    getUserList,
    create,
    deleteById,
}



async function getSysList(req, res) {
    return model.getSysList();
}

const JOI_LIST = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
});

async function getUserList(req, res) {
    const params = await JOI_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getUserList(params);
}

const JOI_CREATE = Joi.object({
    name: Joi.string().required(), // 名称
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    blocks: Joi.array().items(Joi.object({
        type: Joi.string().required(),
        num: Joi.number().required(),
    })).required().min(1),
});

async function create (req, res) {
    const params = await JOI_CREATE.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.create(params);
}

const JOI_DELETE_BY_ID = Joi.object({
    template_id: Joi.string().required(), //  模板ID
});

async function deleteById (req, res) {
    const params = await JOI_DELETE_BY_ID.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.deleteById(params);
}




