const fs = require('fs');
const path = require('path');
const marked = require('marked');

/**
 * Markdown文件处理中间件
 * 支持通过域名+路径访问/public下的markdown文件，删除/public前缀，直接使用下级路径
 * 支持通过参数切换查看模式：
 * - ?view=html (默认): 渲染为HTML格式
 * - ?view=raw 或 ?view=markdown: 显示原始markdown内容
 */
function markdownMiddleware(req, res, next) {
    // 检查请求路径是否以.md结尾
    if (!req.path.endsWith('.md')) {
        return next();
    }

    // 获取查看模式参数
    const viewMode = req.query.view || 'html';

    // 构建文件路径 - 在public目录下查找
    const filePath = path.join(process.cwd(), 'public', req.path);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，继续到下一个中间件
            return next();
        }

        // 读取markdown文件
        fs.readFile(filePath, 'utf8', (readErr, data) => {
            if (readErr) {
                console.error('读取markdown文件失败:', readErr);
                return res.status(500).json({ error: '读取文件失败' });
            }

            try {
                // 根据查看模式处理内容
                if (viewMode === 'raw' || viewMode === 'markdown') {
                    // 返回原始markdown内容
                    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
                    res.setHeader('Cache-Control', 'public, max-age=3600');
                    return res.send(data);
                }

                // 默认模式：渲染为HTML
                // 配置marked选项
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    headerIds: true,
                    mangle: false,
                    sanitize: false
                });

                // 将markdown转换为HTML
                const htmlContent = marked.parse(data);
                
                // 创建完整的HTML页面
                const currentUrl = req.originalUrl.split('?')[0]; // 获取不带参数的URL
                const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Document</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }
        h3 { font-size: 1.25em; }
        code {
            background-color: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
        }
        pre code {
            background-color: transparent;
            border: 0;
            display: inline;
            line-height: inherit;
            margin: 0;
            max-width: auto;
            overflow: visible;
            padding: 0;
            word-wrap: normal;
        }
        table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin: 16px 0;
        }
        table th, table td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }
        table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        blockquote {
            border-left: 4px solid #dfe2e5;
            color: #6a737d;
            margin: 0;
            padding: 0 16px;
        }
        ul, ol {
            margin: 0 0 16px 0;
            padding-left: 2em;
        }
        li {
            margin: 0.25em 0;
        }
        a {
            color: #0366d6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .view-mode-nav {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 10px 0;
            margin-bottom: 20px;
            text-align: center;
        }
        .view-mode-nav a {
            margin: 0 10px;
            padding: 5px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .view-mode-nav a:hover {
            background-color: #0056b3;
            text-decoration: none;
        }
        .view-mode-nav a.active {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="view-mode-nav">
        <strong>查看模式：</strong>
        <a href="${currentUrl}?view=html" class="${viewMode === 'html' ? 'active' : ''}">HTML渲染</a>
        <a href="${currentUrl}?view=raw" class="${viewMode === 'raw' || viewMode === 'markdown' ? 'active' : ''}">原始Markdown</a>
    </div>
    ${htmlContent}
</body>
</html>`;

                // 设置响应头
                res.setHeader('Content-Type', 'text/html; charset=utf-8');
                res.setHeader('Cache-Control', 'public, max-age=3600'); // 缓存1小时
                
                // 发送HTML响应
                res.send(fullHtml);
                
            } catch (parseErr) {
                console.error('解析markdown失败:', parseErr);
                res.status(500).json({ error: '解析markdown失败' });
            }
        });
    });
}

module.exports = markdownMiddleware;
