const fs = require('fs');
const path = require('path');
const marked = require('marked');

/**
 * Markdown文件处理中间件
 * 支持通过域名+路径访问/public下的markdown文件，删除/public前缀，直接使用下级路径
 */
function markdownMiddleware(req, res, next) {
    // 检查请求路径是否以.md结尾
    if (!req.path.endsWith('.md')) {
        return next();
    }

    // 构建文件路径 - 在public目录下查找
    const filePath = path.join(process.cwd(), 'public', req.path);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，继续到下一个中间件
            return next();
        }

        // 读取markdown文件
        fs.readFile(filePath, 'utf8', (readErr, data) => {
            if (readErr) {
                console.error('读取markdown文件失败:', readErr);
                return res.status(500).json({ error: '读取文件失败' });
            }

            try {
                // 配置marked选项
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    headerIds: true,
                    mangle: false,
                    sanitize: false
                });

                // 将markdown转换为HTML
                const htmlContent = marked.parse(data);
                
                // 创建完整的HTML页面
                const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Document</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }
        h3 { font-size: 1.25em; }
        code {
            background-color: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
        }
        pre code {
            background-color: transparent;
            border: 0;
            display: inline;
            line-height: inherit;
            margin: 0;
            max-width: auto;
            overflow: visible;
            padding: 0;
            word-wrap: normal;
        }
        table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin: 16px 0;
        }
        table th, table td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }
        table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        blockquote {
            border-left: 4px solid #dfe2e5;
            color: #6a737d;
            margin: 0;
            padding: 0 16px;
        }
        ul, ol {
            margin: 0 0 16px 0;
            padding-left: 2em;
        }
        li {
            margin: 0.25em 0;
        }
        a {
            color: #0366d6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

                // 设置响应头
                res.setHeader('Content-Type', 'text/html; charset=utf-8');
                res.setHeader('Cache-Control', 'public, max-age=3600'); // 缓存1小时
                
                // 发送HTML响应
                res.send(fullHtml);
                
            } catch (parseErr) {
                console.error('解析markdown失败:', parseErr);
                res.status(500).json({ error: '解析markdown失败' });
            }
        });
    });
}

module.exports = markdownMiddleware;
