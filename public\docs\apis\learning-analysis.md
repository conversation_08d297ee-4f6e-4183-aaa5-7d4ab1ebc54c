---
title: 学习分析接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 学习分析接口

学习分析接口提供教育考情分析功能，支持分析指定地区、年份、科目的考试知识点分布，获取知识点相关原题，并基于考情分析结果进行智能组卷。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## POST 获取知识点考情分析

POST /v1/learning_analysis/knowledge

分析指定条件下的知识点考情，包括考查率、题目数量、平均难度等统计信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|grade|body|string|是|学段（初中/高中/小学）|
|period|body|string|是|学段（与grade一致）|
|subject|body|string|是|科目名称|
|type|body|string|是|试卷类型（高考真卷/中考模拟等）|
|to_year|body|string|是|年份，支持逗号分隔多年份|
|provinces|body|array|是|省份列表|
|» name|body|string|是|省份名称|
|need_ques_num|body|boolean|否|是否需要试题数量|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/learning_analysis/knowledge" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "grade": "高中",
    "period": "高中",
    "subject": "数学",
    "type": "高考真卷",
    "to_year": "2023,2024",
    "provinces": [
      {
        "name": "北京市"
      },
      {
        "name": "上海市"
      }
    ],
    "need_ques_num": true
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "name": "北京市、上海市地区2023-2024学年高中数学高考真卷考情分析",
    "refer_paper_num": 48,
    "refer_real_paper_num": 32,
    "refer_moni_paper_num": 16,
    "know_num": 85,
    "knowledges": [
      {
        "id": 1001,
        "name": "函数的概念与性质",
        "exam_rate": 0.85,
        "ques_num": 156,
        "avg_diff": 0.65,
        "papers": [
          {
            "id": "paper_001",
            "name": "2024年北京市高考数学试卷",
            "category": "高考真卷"
          }
        ],
        "questions": [
          {
            "id": "q_001",
            "type": "选择题",
            "difficulty": "中等"
          }
        ],
        "ques_type": [
          {
            "name": "选择题",
            "num": 68
          },
          {
            "name": "填空题",
            "num": 45
          },
          {
            "name": "解答题",
            "num": 43
          }
        ],
        "real_exam_rate": 0.88,
        "real_ques_num": 128,
        "real_avg_diff": 0.68,
        "real_papers": [
          {
            "id": "real_paper_001",
            "name": "2024年北京市高考数学真题",
            "category": "高考真卷"
          }
        ],
        "real_ques_type": [
          {
            "name": "选择题",
            "num": 56
          },
          {
            "name": "填空题",
            "num": 38
          },
          {
            "name": "解答题",
            "num": 34
          }
        ],
        "real_questions": [
          {
            "id": "real_q_001",
            "type": "选择题",
            "difficulty": "中等"
          }
        ]
      }
    ]
  }
}
```

---

## GET 获取知识点原题

GET /v1/learning_analysis/questions

获取指定知识点的原题列表，用于学习和练习。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|string|是|知识点ID，多个ID用逗号分隔|
|refer_category|query|string|是|试卷类型过滤|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/learning_analysis/questions?ids=1001,1002,1003&refer_category=高考真卷" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": [
    {
      "id": "q_12345",
      "description": "已知函数f(x)=2x+1，求f(3)的值",
      "difficulty": "容易",
      "type": "选择题",
      "comment": "本题考查函数的基本概念和计算方法",
      "period": "高中",
      "subject": "数学",
      "year": "2024",
      "refer_times": 15,
      "refer_exampapers": [
        {
          "id": "paper_001",
          "name": "2024年**省高考数学试卷",
          "category": "高考真卷",
          "year": 2024,
          "province": "北京市"
        }
      ],
      "blocks": {
        "types": ["选择题"],
        "stems": [
          {
            "content": "已知函数f(x)=2x+1，求f(3)的值是（）",
            "options": ["A. 6", "B. 7", "C. 8", "D. 9"]
          }
        ],
        "knowledges": [
          {
            "id": 1001,
            "name": "函数的概念与性质"
          }
        ]
      }
    },
    {
      "id": "q_12346",
      "description": "设函数g(x)=x²-4x+3，求函数的最小值",
      "difficulty": "中等",
      "type": "填空题",
      "comment": "本题考查二次函数的最值问题",
      "period": "高中",
      "subject": "数学",
      "year": "2024",
      "refer_times": 23,
      "refer_exampapers": [
        {
          "id": "paper_002",
          "name": "2024年**市高考数学模拟卷",
          "category": "模拟试卷",
          "year": 2024,
          "province": "上海市"
        }
      ],
      "blocks": {
        "types": ["填空题"],
        "stems": [
          {
            "content": "设函数g(x)=x²-4x+3，则函数的最小值为_______"
          }
        ],
        "knowledges": [
          {
            "id": 1002,
            "name": "二次函数"
          }
        ]
      }
    }
  ]
}
```

---

## POST 知识点智能组卷

POST /v1/learning_analysis/paper

基于考情分析结果进行智能组卷，生成专题练习卷。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|grade|body|string|是|学段（初中/高中/小学）|
|period|body|string|是|学段（与grade一致）|
|subject|body|string|是|科目名称|
|type|body|string|是|试卷类型|
|to_year|body|string|是|年份|
|provinces|body|array|是|省份列表|
|» name|body|string|是|省份名称|
|knowledges|body|array|是|选择的知识点，最多5个|
|need_ques_num|body|boolean|否|是否需要试题数量|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/learning_analysis/paper" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "grade": "高中",
    "period": "高中",
    "subject": "数学",
    "type": "高考真卷",
    "to_year": "2024",
    "provinces": [
      {
        "name": "北京市"
      }
    ],
    "knowledges": [
      {
        "id": 1001,
        "name": "函数的概念与性质"
      },
      {
        "id": 1002,
        "name": "二次函数"
      }
    ]
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "user_id": "user_001",
    "period": "高中",
    "subject": "数学",
    "grade": "",
    "press_version": "",
    "knowledge_tree": "",
    "type": "",
    "name": "考情分析专题练习卷（2025年1月15日）",
    "subtitle": "",
    "score": 0,
    "duration": 0,
    "paper_info": "",
    "cand_info": "",
    "score_info": "",
    "attentions": "",
    "secret_tag": "",
    "gutter": 0,
    "display": 0,
    "ctime": "2025-01-15T10:30:00Z",
    "utime": "2025-01-15T10:30:00Z",
    "volumes": [
      {
        "title": "",
        "note": "",
        "blocks": [
          {
            "id": "1001",
            "name": "函数的概念与性质",
            "title": "知识点一：函数的概念与性质",
            "note": "",
            "type": "",
            "default_score": 0,
            "questions": [
              {
                "id": "q_12345",
                "description": "已知函数f(x)=2x+1，求f(3)的值",
                "difficulty": "容易",
                "type": "选择题",
                "comment": "本题考查函数的基本概念和计算方法",
                "period": "高中",
                "subject": "数学",
                "year": "2024",
                "refer_times": 15,
                "refer_exampapers": [
                  {
                    "id": "paper_001",
                    "name": "2024年**省高考数学试卷",
                    "category": "高考真卷"
                  }
                ],
                "blocks": {
                  "types": ["选择题"],
                  "stems": [
                    {
                      "content": "已知函数f(x)=2x+1，求f(3)的值是（）",
                      "options": ["A. 6", "B. 7", "C. 8", "D. 9"]
                    }
                  ],
                  "knowledges": [
                    {
                      "id": 1001,
                      "name": "函数的概念与性质"
                    }
                  ]
                }
              }
            ]
          },
          {
            "id": "1002",
            "name": "二次函数",
            "title": "知识点二：二次函数",
            "note": "",
            "type": "",
            "default_score": 0,
            "questions": [
              {
                "id": "q_12346",
                "description": "设函数g(x)=x²-4x+3，求函数的最小值",
                "difficulty": "中等",
                "type": "填空题",
                "comment": "本题考查二次函数的最值问题",
                "period": "高中",
                "subject": "数学",
                "year": "2024",
                "refer_times": 23,
                "refer_exampapers": [
                  {
                    "id": "paper_002",
                    "name": "2024年**市高考数学模拟卷",
                    "category": "模拟试卷"
                  }
                ],
                "blocks": {
                  "types": ["填空题"],
                  "stems": [
                    {
                      "content": "设函数g(x)=x²-4x+3，则函数的最小值为_______"
                    }
                  ],
                  "knowledges": [
                    {
                      "id": 1002,
                      "name": "二次函数"
                    }
                  ]
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

---

## 返回数据结构

### 考情分析结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|分析名称|考情分析报告标题|
|refer_paper_num|number|true|none|参考试卷总数|分析的试卷总数量|
|refer_real_paper_num|number|true|none|参考真题试卷数|真题试卷数量|
|refer_moni_paper_num|number|true|none|参考模拟试卷数|模拟试卷数量|
|know_num|number|true|none|知识点数量|涉及的知识点总数|
|knowledges|array|true|none|知识点列表|详细的知识点分析结果|

### 知识点分析结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|知识点ID|唯一标识|
|name|string|true|none|知识点名称|知识点标题|
|exam_rate|number|true|none|考查率|0-1之间的小数|
|ques_num|number|true|none|题目数量|该知识点的题目总数|
|avg_diff|number|true|none|平均难度|0-1之间的小数|
|papers|array|true|none|相关试卷|包含该知识点的试卷列表|
|questions|array|true|none|相关试题|该知识点下的试题列表|
|ques_type|array|true|none|题型分布|不同题型的数量统计|

### 真题分析结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|real_exam_rate|number|false|none|真题考查率|在真题中的考查率|
|real_ques_num|number|false|none|真题数量|真题试题数量|
|real_avg_diff|number|false|none|真题平均难度|真题的平均难度|
|real_papers|array|false|none|相关真题试卷|包含该知识点的真题试卷|
|real_ques_type|array|false|none|真题题型分布|真题中不同题型的统计|
|real_questions|array|false|none|相关真题|该知识点下的真题列表|

### 试题信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|试题ID|唯一标识|
|description|string|true|none|试题描述|试题简要描述|
|difficulty|string|true|none|难度|容易/中等/困难等|
|type|string|true|none|题型|选择题/填空题/解答题等|
|comment|string|false|none|试题解析|解题思路和答案解析|
|period|string|true|none|学段|适用学段|
|subject|string|true|none|科目|适用科目|
|year|string|true|none|年份|试题年份|
|refer_times|number|true|none|引用次数|被引用的次数统计|
|refer_exampapers|array|true|none|引用试卷|引用该试题的试卷列表|
|blocks|object|true|none|试题结构|题干、选项、知识点等|

### 试卷信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|试卷ID|唯一标识|
|name|string|true|none|试卷名称|使用模糊名称保护隐私|
|category|string|true|none|试卷类别|高考真卷/模拟试卷等|
|year|number|false|none|年份|试卷年份|
|province|string|false|none|省份|试卷来源省份|

### 专题练习卷结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|创建者ID|
|period|string|true|none|学段|适用学段|
|subject|string|true|none|科目|适用科目|
|name|string|true|none|试卷名称|自动生成的试卷名称|
|volumes|array|true|none|卷册|试卷结构|
|ctime|string|true|none|创建时间|ISO格式时间|
|utime|string|true|none|更新时间|ISO格式时间|

### 题块结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none|知识点ID|对应的知识点ID|
|name|string|true|none|知识点名称|知识点标题|
|title|string|true|none|题块标题|格式化的标题|
|questions|array|true|none|试题列表|该知识点下的试题|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|12|参数错误|请求参数格式错误或缺失必要参数|
|14|业务错误|业务逻辑处理错误|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 考情分析流程

1. **数据搜索**: 根据地区、年份、科目等条件搜索相关试卷（最多2000份）
2. **知识点分析**: 调用知识库服务分析知识点的考查情况
3. **统计计算**: 计算考查率、平均难度、题型分布等统计指标
4. **真题区分**: 分别统计真题和模拟题的分析结果
5. **结果合并**: 将分析结果合并成完整的考情分析报告

### 智能组卷算法

1. **知识点选择**: 最多支持5个知识点的专题组卷
2. **题目推荐**: 每个知识点推荐4-6道题目
3. **难度匹配**: 根据知识点的平均难度推荐相应难度的题目
4. **题型平衡**: 按题型比例分配题目数量
5. **去重处理**: 使用AI服务过滤相似题目，避免重复
6. **结构组织**: 按知识点分组生成专题练习卷

### 数据安全保护

- **隐私保护**: 试卷名称使用模糊处理，避免暴露具体信息
- **字段过滤**: 删除敏感的from字段和其他内部标识
- **访问控制**: 通过认证确保只有授权用户可以访问
- **数据脱敏**: 对试卷来源等信息进行适当脱敏处理

### 性能优化

- **批量处理**: 外部服务调用采用批量处理，每批10个请求
- **并发控制**: 避免过高并发对外部服务造成压力
- **结果缓存**: 相同条件的分析结果可以进行缓存优化
- **数据分页**: 大量数据查询采用分页机制

### 使用场景

1. **教师备考**: 分析历年考试趋势，制定教学计划
2. **学生复习**: 了解知识点考查频率，合理分配复习时间
3. **专题练习**: 生成针对性的专题练习卷，强化重点知识
4. **考试预测**: 基于历年数据预测未来考试重点
5. **教研分析**: 为教研活动提供数据支撑和分析依据