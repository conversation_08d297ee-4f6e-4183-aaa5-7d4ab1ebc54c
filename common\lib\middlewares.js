// 'use strict';

const utils = require('./utils');
const errCodes = require('./error');
const BusError = require('./BusError');

/**
 * API 异常/返回值统一处理
 * @param apiCode lib/api.js中的代码
 * @param fun api逻辑 async function (req, res)
 */
function reqHandler (code, fun) {
    return async (req, res) => {
        let errMsg = utils.buildErrMsg(req, code);
        try {
            const result = await fun(req, res);
            errMsg.msg = 'success';
            return res.json(utils.resJSON(errCodes.SUCCESS, '成功', result, null));
        } catch (err) {
            errMsg.msg = 'err';
            errMsg.logLevel = 'error';
            errMsg.errStack = err.stack || err;
            if (err instanceof BusError) {
                errMsg.msg = err.message;
                return res.json(utils.resJSON(apiCode, err.message, {}, null));
            }
            if (err.isJoi) { // 参数验证错误
                return res.json(utils.resJSON(errCodes.PARAM_ERR, '参数错误: ' + err.message, {}, errMsg));
            }
            // 可增加其余自定义异常处理
            return res.json(utils.resJSON(errCodes.INTERNAL_ERR, err.message || err || '内部错误(oms)', {}, errMsg));
        }
    };
}

module.exports = {
    reqHandler
};
