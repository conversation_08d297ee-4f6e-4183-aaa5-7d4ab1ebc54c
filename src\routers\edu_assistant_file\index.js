const express = require('express');
const router = express.Router();
const service = require('../../service/edu_assistant_file');
const { handler } = require('../../../common/middlewares/request');

// 获取目录资源
router.get('/list', handler(1, service.getList));
// 获取资源详细
router.get('/info', handler(1, service.getDetail));
// 下载文件
router.post('/download', handler(1, service.download));
// 更新浏览或者下载次数
router.put('/times', handler(1, service.updateTimes));

module.exports = router;
