const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const path = require('path');
const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const paperUtils = require('../../../common/utils/paper_utils');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const questionModel = require('../question');
const knowTitlePrefix = ["知识点一", "知识点二", "知识点三", "知识点四", "知识点五", "知识点六"];

module.exports = {
    knowledge,
    knowledgeQuestions,
    knowledgePaper
}

async function knowledge(params) {
    const {period, subject, grade, type, to_year } = params;
    params['tiku'] = true;
    // 地区限制
    let pro_names = [];
    const provinces = params.provinces || [];
    provinces.forEach(element => {
        pro_names.push(element['name'])
    });
    if (pro_names.length !== 0){
        params['province'] = pro_names.join(',');
    }
    delete params['provinces'];
    params['limit'] = 2000;
    params['offset'] = 0;

    const exampapers = await client.kbSe.filterExampapers(params);
    const paperIds = exampapers && exampapers.datas || [];
    let paperName = getPaperName(period, subject, grade, type, provinces, to_year);
    const result = {
        name: paperName,
        refer_paper_num: 0,
        know_num: 0,
        knowledges: []
    };
    if (!_.size(paperIds)) {
        return result;
    }
    const analysisResult = await client.kb.knowledgeAnalysis({paper_ids:paperIds});// 获取中高考分析结果
    let {knowDict, realPaperNum, moniPaperNum}  = await realPaperAnalysis(period, subject, pro_names);
    let newKnowledges = []
    let knowledges = analysisResult.knowledges || [];
    for (let k=0; k<knowledges.length; k++){
        let knowId = knowledges[k].id;
        if (!_.isNumber(knowId)) {
            continue;
        }
        let k_know_dict = knowledges[k];
        if (knowDict.hasOwnProperty(knowId)){
            k_know_dict['real_papers'] = knowDict[knowId]['real_papers'];
            k_know_dict['real_ques_type'] = knowDict[knowId]['real_ques_type'];
            k_know_dict['real_avg_diff'] = knowDict[knowId]['real_avg_diff'];
            k_know_dict['real_exam_rate'] = knowDict[knowId]['real_exam_rate'];
            k_know_dict['real_questions'] = knowDict[knowId]['real_questions'];
            k_know_dict['real_ques_num'] = knowDict[knowId]['real_ques_num'];
        }
        newKnowledges.push(k_know_dict);
    }
    analysisResult['knowledges'] = newKnowledges;
    analysisResult['name'] = paperName;
    analysisResult['refer_real_paper_num'] = realPaperNum;
    analysisResult['refer_moni_paper_num'] = moniPaperNum;
    return analysisResult;
}

const getPaperName = (period, subject, grade, type, provinces, to_year) => {
    // 省份名字
    let provinceName = ''
    let provinceNames = []
    provinces.forEach(element => {
        if (provinceNames.length < 2){
            provinceNames.push(element.name)
        }
    })
    if (provinceNames.length === 1){
        provinceName = `${provinceNames[0]}地区`
    } else if (provinceNames.length === 2){
        provinceName = `${provinceNames[0]}、${provinceNames[1]}等地区`
    }
    // 年份
    to_year = to_year.replace(/,/g, '、')

    // 年级
    grade = grade.replace(/,/g, '、')

    // 类型
    type = type.replace(/,/g, '、')
    let paperName = `${provinceName}${to_year}学年${period}${grade}学期${subject}${type}考情分析`;
    return paperName
}

async function knowledgeQuestions(params) {
    const {ids, refer_category} = params;
    let questions = await client.kb.getQuestionByIds(ids.split(','));
    let quesInfo = []
    let quesDict = {}
    questions.forEach(element => {
        let new_ques = {};
        new_ques['id'] = element.id
        new_ques['description'] = element.description || ''
        new_ques['difficulty'] = element.difficulty || ''
        new_ques['type'] = element.type || ''
        new_ques['comment'] = element.comment || ''
        new_ques['period'] = element.period || ''
        new_ques['subject'] = element.subject || ''
        new_ques['year'] = element.year || ''
        new_ques['refer_times'] = element.refer_times || 0

        // 引用试卷
        let newRefer = [];
        let refer_exampapers = element.refer_exampapers || [];
        refer_exampapers.forEach(refer => {
            let paper_category = refer.category || ''
            if (refer_category.includes(paper_category) || refer_category === '') {
                if (refer.hasOwnProperty('from')){
                    delete refer['from']
                }
                refer['name'] = refer['vague_name']
                newRefer.push(refer)
            }
        });
        new_ques['refer_exampapers'] = newRefer

        // 试题结构
        let newBlocks = {};
        let blocks = element.blocks || [];
        newBlocks['types'] = blocks.types || []
        newBlocks['stems'] = blocks.stems || []
        newBlocks['knowledges'] = blocks.knowledges || []
        new_ques['blocks'] = newBlocks

        quesInfo.push(new_ques);
        quesDict[element.id] = new_ques
    });
    quesInfo = _.reverse(_.sortBy(quesInfo, function(it) {
        return it.type
    }));
    // return {'quesInfo': quesInfo, 'quesDict': quesDict};
    return quesInfo;
}

async function knowledgePaper(params) {
    const {period, subject, user_id } = params;
    // 分析结果中的原题ID集合
    let quesIds = [];
    let knowledges = params.knowledges || [];
    knowledges = knowledges.slice(0, 5)
    knowledges.forEach(know_dict => {
        let questions = know_dict.questions || [];
        questions.forEach(ques_dict => {
            quesIds.push(ques_dict.id);
        })
    });
    let searchParams = [];
    let knowQuesType = [];
    let recomTypeNum = [];
    knowledges.forEach(knowDict => {
        let knowId = knowDict.id || ''
        let knowName = knowDict.name || ''

        // 每个知识点选择的试题数量
        let knowQuesNum = knowDict.ques_num || 0;
        let selectQuesNum = Math.max(Math.min(6, knowQuesNum), 4)

        // 知识点难度 和 推荐试题难度
        let knowDiff = knowDict.avg_diff || 0.45
        let RecoDiff = getQuesDiff(knowDiff)

        // 每个类型的试题都推荐一些试题
        let quesType = knowDict.ques_type || [];
        quesType.forEach(typeDict => {
            let typeName = typeDict.name || ''
            let typeQuesNum = typeDict.num || 0;
            let targetQuesNum = Math.ceil((typeQuesNum/knowQuesNum)*selectQuesNum)
            // 搜索试题参数
            let searchQues = {
                "period": period,
                "subject": subject,
                "type": typeName,
                "knowledges": `${knowId}`,
                "sort_by": "year",
                "offset": 0,
                "limit": 200,
                "difficulty": RecoDiff,
                "set_mode": {
                    "knowledges": "intersection"
                }
            }
            searchParams.push(searchQues)
            recomTypeNum.push(targetQuesNum)
            knowQuesType.push(`${knowName}__${typeName}`)
        })
    });
    // 试题搜索
    let algo_result = [];
    try {
        for (const arr of _.chunk(searchParams, 10)) {
            const res = await Promise.all(arr.map(e => client.kb.questionSearch(e)));
            algo_result.push(...res);
        }
    } catch (err) {
        logger.error(err);
        algo_result = [];
    }
    // 获取结果试题ID集合
    let recomAllQues = [];
    algo_result.forEach(element => {
        let ques_list = element.questions || [];
        ques_list.forEach(ques => {
            if (!recomAllQues.includes(ques.id)){
                recomAllQues.push(ques.id)
            }
        })
    });

    // 查重
    let saveQuesIds = await client.aiKb.filterRepeatQues(recomAllQues);
    // 筛选试题
    let knowTypeQues = {}
    let selectedQuesIds = []
    for (let i = 0; i < searchParams.length; i++) {
        let targetNum = recomTypeNum[i];
        let knowTypeName = knowQuesType[i];
        let recoQues = algo_result[i]['questions'] || []
        for (let j=0; j < recoQues.length; j++){
            let id = recoQues[j].id;
            // 已过滤重复试题
            if (!saveQuesIds.includes(id)){
                continue
            }

            // 已选试题
            if (selectedQuesIds.includes(id)){
                continue
            }
            selectedQuesIds.push(id)

            // 50%的概率允许原题出现
            if (quesIds.includes(id) && Math.random() > 0.5) {
                continue
            }

            // 试题来源
            let oneQues = recoQues[j]
            if (oneQues.hasOwnProperty('from')){
                delete oneQues['from']
            }

            // 引用试卷
            let newRefer = [];
            let refer_exampapers = oneQues.refer_exampapers || [];
            refer_exampapers.forEach(refer => {
                if (refer.hasOwnProperty('from')){
                    delete refer['from']
                }
                refer['name'] = refer['vague_name']
                newRefer.push(refer)
            });
            oneQues['refer_exampapers'] = newRefer

            let quesList = knowTypeQues[knowTypeName] || []
            quesList.push(oneQues)
            knowTypeQues[knowTypeName] = quesList

            // 选够停止
            targetNum = targetNum - 1;
            if (targetNum === 0){
                break
            }
        }
    }
    // 专题结构
    let blocks = []
    knowledges.forEach(knowDict => {
        let knowId = knowDict.id || ''
        let knowName = knowDict.name || ''
        // 获取试题
        let questions = []
        let quesType = knowDict.ques_type || [];
        quesType.forEach(typeDict => {
            let typeName = typeDict.name || ''
            questions = questions.concat(knowTypeQues[`${knowName}__${typeName}`] || [])
        })
        if (_.size(questions)){
            questions = _.reverse(_.sortBy(questions, function(it) {
                return it.type
            }))
            let block = {
                'id': knowId,
                'name': knowName,
                'title': `${knowTitlePrefix[blocks.length]}：${knowName}`,
                'note': '',
                'type': '',
                'default_score': 0,
                'questions': questions
            }
            blocks.push(block)
        }
    })

    let dateTime = new Date()
    let exampaper = {
        user_id: user_id,
        period: period,
        subject: subject,
        grade: '',
        press_version: '',
        knowledge_tree: '',
        type: '',
        name: `考情分析专题练习卷（${dateTime.getFullYear()}年${dateTime.getMonth()}月${dateTime.getDate()}日）`,
        subtitle: '',
        score: 0,
        duration: 0,
        paper_info: '',
        cand_info: '',
        score_info: '',
        attentions: '',
        secret_tag: '',
        gutter: 0,
        display: 0,
        ctime: dateTime,
        utime: dateTime,
        volumes:[{
            title: '',
            note: '',
            blocks: blocks
        }]
    };
    return exampaper;
}


const realPaperAnalysis = async (period, subject, pro_names) => {
    // 根据条件搜索试卷
    let currentYear = new Date().getFullYear()
    let realBody = {
        'offset': 0,
        'tiku': true,
        'limit': 2000,
        'period': period,
        'subject': subject,
        'to_year': `${currentYear+1},${currentYear},${currentYear-1}`
    }
    let moniBody = {
        'offset': 0,
        'tiku': true,
        'limit': 5000,
        'period': period,
        'subject': subject,
        'to_year': `${currentYear+1},${currentYear},${currentYear-1}`
    }
    moniBody['type'] = '高考模拟'
    realBody['type'] = '高考真卷'
    if (period === '初中') {
        moniBody['type'] = '中考模拟'
        realBody['type'] = '中考真卷'
        if (pro_names.length !== 0){
            moniBody['province'] = pro_names.join(',')
            realBody['province'] = pro_names.join(',')
        }
    } else if (period === '小学') {
        moniBody['type'] = '小升初模拟'
        realBody['type'] = '小升初真卷'
        if (pro_names.length !== 0){
            moniBody['province'] = pro_names.join(',')
            realBody['province'] = pro_names.join(',')
        }
    }

    let realPaperNum = 0
    let moniPaperNum = 0
    let knowDict = {}
    try {
        let paperIds = []
        // 获取真卷
        let realExampapers = await client.kbSe.filterExampapers(realBody);
        if (realExampapers && realExampapers.datas){
            let realPaperIds = realExampapers.datas || [];
            realPaperIds.forEach(real_id => {
                paperIds.push(real_id)
            })
            realPaperNum = realPaperIds.length
        }
        // 获取模拟卷
        let moniExampapers = await client.kbSe.filterExampapers(moniBody);
        if (moniExampapers && moniExampapers.datas){
            let moniPaperIds = moniExampapers.datas || [];
            moniPaperIds.forEach(moni_id => {
                paperIds.push(moni_id)
            })
            moniPaperNum = moniPaperIds.length
        }

        if (!paperIds.length) {
            return {knowDict, realPaperNum, moniPaperNum}
        }

        // 获取分析结果
        const analysisResult = await client.kb.knowledgeAnalysis({paper_ids:paperIds, "from_limit":["ai_organize_paper"]});

        // 获取知识点及其分析结果
        let knowledges = analysisResult && analysisResult.knowledges || [];
        knowledges.forEach( know => {
            let know_id = know.id;
            knowDict[know_id] = {
                "real_papers": know.papers || [],
                "real_ques_type": know.ques_type || [],
                "real_questions": know.questions || [],
                "real_ques_num": know.ques_num || 0,
                "real_exam_rate": know.exam_rate || 0,
                "real_avg_diff": know.avg_diff || 0.5
            }
        })
        return {knowDict, realPaperNum, moniPaperNum}
    } catch (err) {
        logger.error(err);
        return {knowDict, realPaperNum, moniPaperNum}
    }
}


const getQuesDiff = (diff) => {
    let diffName = '中等,较难,较易'
    if (0 < diff <= 0.35){
        diffName = '困难,较难,中等'
    } else if  (0.35 < diff <= 0.55){
        diffName = '较难,中等,较易'
    } else if  (0.55 < diff <= 0.7){
        diffName = '中等,较难,较易'
    } else if  (0.7 < diff <= 0.85){
        diffName = '较易,容易,中等'
    } else {
        diffName = '容易,较易,中等'
    }
    return diffName
}

