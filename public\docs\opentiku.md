---
title: open_tiku
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# open_tiku

Base URLs:

# Authentication

# 学校

## GET 获取教师

GET /v1/school/teachers

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "name": "string",
      "phone": "string",
      "title": [
        "string"
      ],
      "xueke": [
        "string"
      ],
      "nianji": [
        "string"
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|true|none|教师ID|none|
|»» name|string|true|none|姓名|none|
|»» phone|string|true|none|手机号|none|
|»» title|[string]|true|none||none|
|»» xueke|[string]|true|none|科目列表|none|
|»» nianji|[string]|true|none|年级列表|none|

## GET 获取年级组

GET /v1/school/grade/groups

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "name": "string",
      "id": "string",
      "grades": [
        {
          "sequence": 0,
          "gradeName": "string"
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|[object]|true|none||none|
|»» name|string|true|none||none|
|»» id|string|true|none||none|
|»» grades|[object]|true|none||none|
|»»» sequence|integer|true|none||none|
|»»» gradeName|string|true|none||none|

# 教师

## GET 获取当前登录教师信息

GET /v1/teacher/hfs/info

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Cookie|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string",
    "name": "string",
    "avatar": "string",
    "phone": "string",
    "account": "string",
    "schoolId15": 0,
    "schoolId": 0,
    "schoolName": "string",
    "roles": [
      "string"
    ],
    "roleAll": [
      {
        "title": "string",
        "from": 0
      }
    ],
    "classes": [
      "string"
    ],
    "isLiveTeacher": true,
    "organizationType": 0,
    "isOpenPractice": true,
    "isOpenMessage": true,
    "logoPath": "string",
    "vipType": true,
    "schoolVersion": {
      "schoolType": "string",
      "vipType": 0,
      "displayVersion": true,
      "zdyVersion": {
        "open": true,
        "time": "string"
      },
      "endTime": "string",
      "startTime": "string",
      "useType": "string"
    },
    "schoolId20": 0,
    "banner": "string",
    "user_tag": "string",
    "standard_period": "string",
    "standard_subject": "string",
    "curr_property": {
      "period": "string",
      "subject": "string"
    },
    "authRoles": [
      {
        "key": "string",
        "name": "string",
        "children": [
          "string"
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» name|string|true|none||none|
|»» avatar|string|true|none||none|
|»» phone|string|true|none||none|
|»» account|string|true|none||none|
|»» schoolId15|integer|true|none||none|
|»» schoolId|integer|true|none||none|
|»» schoolName|string|true|none||none|
|»» roles|[string]|true|none||none|
|»» roleAll|[object]|true|none||none|
|»»» title|string|false|none||none|
|»»» from|integer|false|none||none|
|»» classes|[string]|true|none||none|
|»» isLiveTeacher|boolean|true|none||none|
|»» organizationType|integer|true|none||none|
|»» isOpenPractice|boolean|true|none||none|
|»» isOpenMessage|boolean|true|none||none|
|»» logoPath|string|true|none||none|
|»» vipType|boolean|true|none||none|
|»» schoolVersion|object|true|none||none|
|»»» schoolType|string|true|none||none|
|»»» vipType|integer|true|none||none|
|»»» displayVersion|boolean|true|none||none|
|»»» zdyVersion|object|true|none||none|
|»»»» open|boolean|true|none||none|
|»»»» time|string|true|none||none|
|»»» endTime|string|true|none||none|
|»»» startTime|string|true|none||none|
|»»» useType|string|true|none||none|
|»» schoolId20|integer|true|none||none|
|»» banner|string|true|none||none|
|»» user_tag|string|true|none||none|
|»» standard_period|string|true|none||none|
|»» standard_subject|string|true|none||none|
|»» curr_property|object|true|none||none|
|»»» period|string|true|none||none|
|»»» subject|string|true|none||none|
|»» authRoles|[object]|true|none||none|
|»»» key|string|false|none||none|
|»»» name|string|false|none||none|
|»»» children|[string]|false|none||none|

## GET 用户数据

GET /v1/teacher/profile

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string| 是 |none|
|subject|query|string| 是 |none|
|Cookie|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "comment_homework": {
      "total": 0,
      "list": [
        {
          "id": "string",
          "name": "string",
          "paper_id": "string",
          "grade": "string",
          "subject": "string",
          "event_time": null,
          "published": 0,
          "type": 0,
          "paper": "string",
          "zujuanId": "string"
        }
      ]
    },
    "comment_exam": {
      "total": 0,
      "list": [
        {
          "exam_id": "string",
          "exam_name": "string",
          "paper_id": "string",
          "period": "string",
          "subject": "string",
          "grade": "string",
          "id": "string",
          "class_info": [
            null
          ],
          "ctime": 0,
          "utime": 0
        }
      ]
    },
    "team_prep": {
      "total": 0,
      "doing": 0,
      "done": 0,
      "list": [
        {
          "name": "string",
          "end_time": "string",
          "remark": "string",
          "edu_plan_id": "string",
          "teachers": [
            null
          ],
          "children": [
            null
          ],
          "status": "string",
          "period": "string",
          "subject": "string",
          "grade": "string",
          "from_year": 0,
          "to_year": 0,
          "semester": "string",
          "school_id": 0,
          "main_teacher_id": "string",
          "createdAt": "string",
          "updatedAt": "string",
          "id": "string",
          "overtime": 0
        }
      ]
    },
    "user_paper": {
      "total": 0,
      "list": [
        {
          "id": "string",
          "name": "string",
          "question_num": 0,
          "grade": "string",
          "period": "string",
          "subject": "string",
          "source": "string",
          "type": "string",
          "from_year": 0,
          "to_year": 0,
          "status": "string",
          "ctime": 0,
          "utime": 0,
          "error": "string",
          "from_enum": 0
        }
      ]
    },
    "upload_paper": {
      "total": 0,
      "list": [
        {
          "id": "string",
          "name": "string",
          "question_num": 0,
          "grade": "string",
          "period": "string",
          "subject": "string",
          "type": "string",
          "from_year": 0,
          "to_year": 0,
          "status": "string",
          "tiku_paper_id": "string",
          "ctime": 0,
          "utime": 0,
          "error": "string",
          "from_enum": 0,
          "task_type": "string",
          "exam_status": "string",
          "source": "string"
        }
      ]
    },
    "disk_file": {
      "total": 0,
      "list": [
        {
          "size": 0,
          "suffix": "string",
          "category": "string",
          "hash": "string",
          "url": "string",
          "is_folder": 0,
          "parent_id": "string",
          "source": "string",
          "download_times": 0,
          "view_times": 0,
          "shared": 0,
          "is_top": 0,
          "period": "string",
          "subject": "string",
          "name": "string",
          "school_id": 0,
          "createdAt": "string",
          "updatedAt": "string",
          "__v": 0,
          "creator": {}
        }
      ],
      "share_list": [
        {
          "size": 0,
          "suffix": "string",
          "category": "string",
          "hash": "string",
          "url": "string",
          "is_folder": 0,
          "parent_id": "string",
          "source": "string",
          "download_times": 0,
          "view_times": 0,
          "shared": 0,
          "is_top": 0,
          "period": "string",
          "subject": "string",
          "name": "string",
          "school_id": 0,
          "createdAt": "string",
          "updatedAt": "string",
          "__v": 0,
          "creator": {}
        }
      ]
    }
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» comment_homework|object|true|none|讲评作业|none|
|»»» total|integer|true|none||none|
|»»» list|[object]|true|none||none|
|»»»» id|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» paper_id|string|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» event_time|null|true|none||none|
|»»»» published|integer|true|none||none|
|»»»» type|integer|true|none||none|
|»»»» paper|string|true|none||none|
|»»»» zujuanId|string|true|none||none|
|»» comment_exam|object|true|none|讲评考试|none|
|»»» total|integer|true|none||none|
|»»» list|[object]|true|none||none|
|»»»» exam_id|string|false|none||none|
|»»»» exam_name|string|false|none||none|
|»»»» paper_id|string|false|none||none|
|»»»» period|string|false|none||none|
|»»»» subject|string|false|none||none|
|»»»» grade|string|false|none||none|
|»»»» id|string|false|none||none|
|»»»» class_info|[object]|false|none||none|
|»»»»» id|string|false|none||none|
|»»»»» name|string|false|none||none|
|»»»»» courseware_name|string|false|none||none|
|»»»»» question_num|integer|false|none||none|
|»»»» ctime|integer|false|none||none|
|»»»» utime|integer|false|none||none|
|»» team_prep|object|true|none|集备|none|
|»»» total|integer|true|none||none|
|»»» doing|integer|true|none||none|
|»»» done|integer|true|none||none|
|»»» list|[object]|true|none||none|
|»»»» name|string|true|none||none|
|»»»» end_time|string|true|none||none|
|»»»» remark|string|true|none||none|
|»»»» edu_plan_id|string|true|none||none|
|»»»» teachers|[string]|true|none||none|
|»»»» children|[object]|true|none||none|
|»»»»» key|string|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» children|[object]|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» key|string|true|none||none|
|»»»»»» chapter_id|integer|true|none||none|
|»»»»»» children|[object]|true|none||none|
|»»»»»»» name|string|true|none||none|
|»»»»»»» key|string|true|none||none|
|»»»»»»» chapter_id|integer|true|none||none|
|»»»»»»» children|[object]|true|none||none|
|»»»»»»»» name|string|true|none||none|
|»»»»»»»» key|string|true|none||none|
|»»»»»»»» chapter_id|integer|true|none||none|
|»»»»»»»» children|[object]|true|none||none|
|»»»»»»»»» key|string|true|none||none|
|»»»»»»»»» name|string|true|none||none|
|»»»»»»»»» index|integer|true|none||none|
|»»»»»»»»» id|string|true|none||none|
|»»»»»»»»» items|[string]|true|none||none|
|»»»»»»»» book_id|integer|true|none||none|
|»»»»»»»» id|string|true|none||none|
|»»»»»»»» index|integer|true|none||none|
|»»»»»»»» items|[string]|true|none||none|
|»»»»»»» book_id|integer|true|none||none|
|»»»»»»» id|string|true|none||none|
|»»»»»»» index|integer|true|none||none|
|»»»»»»» items|[string]|true|none||none|
|»»»»»» book_id|integer|true|none||none|
|»»»»»» id|string|true|none||none|
|»»»»»» items|[string]|true|none||none|
|»»»»» source_id|string|true|none||none|
|»»»»» book_id|integer|true|none||none|
|»»»»» chapter_id|integer|true|none||none|
|»»»»» id|string|true|none||none|
|»»»» status|string|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» from_year|integer|true|none||none|
|»»»» to_year|integer|true|none||none|
|»»»» semester|string|true|none||none|
|»»»» school_id|integer|true|none||none|
|»»»» main_teacher_id|string|true|none||none|
|»»»» createdAt|string|true|none||none|
|»»»» updatedAt|string|true|none||none|
|»»»» id|string|true|none||none|
|»»»» overtime|integer|true|none||none|
|»» user_paper|object|true|none|用户组卷|none|
|»»» total|integer|true|none||none|
|»»» list|[object]|true|none||none|
|»»»» id|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» question_num|integer|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» source|string|true|none||none|
|»»»» type|string|true|none||none|
|»»»» from_year|integer|true|none||none|
|»»»» to_year|integer|true|none||none|
|»»»» status|string|true|none||none|
|»»»» ctime|integer|true|none||none|
|»»»» utime|integer|true|none||none|
|»»»» error|string|true|none||none|
|»»»» from_enum|integer|true|none||none|
|»» upload_paper|object|true|none|上传|none|
|»»» total|integer|true|none||none|
|»»» list|[object]|true|none||none|
|»»»» id|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» question_num|integer|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» type|string|true|none||none|
|»»»» from_year|integer|true|none||none|
|»»»» to_year|integer|true|none||none|
|»»»» status|string|true|none||none|
|»»»» tiku_paper_id|string|true|none||none|
|»»»» ctime|integer|true|none||none|
|»»»» utime|integer|true|none||none|
|»»»» error|string|true|none||none|
|»»»» from_enum|integer|true|none||none|
|»»»» task_type|string|true|none||none|
|»»»» exam_status|string|true|none||none|
|»»»» source|string|true|none||none|
|»» disk_file|object|true|none|网盘|none|
|»»» total|integer|true|none||none|
|»»» list|[object]|true|none|我的上传|none|
|»»»» size|integer|true|none||none|
|»»»» suffix|string|true|none||none|
|»»»» category|string|true|none||none|
|»»»» hash|string|true|none||none|
|»»»» url|string|true|none||none|
|»»»» is_folder|integer|true|none||none|
|»»»» parent_id|string|true|none||none|
|»»»» source|string|true|none||none|
|»»»» download_times|integer|true|none||none|
|»»»» view_times|integer|true|none||none|
|»»»» shared|integer|true|none||none|
|»»»» is_top|integer|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» school_id|integer|true|none||none|
|»»»» createdAt|string|true|none||none|
|»»»» updatedAt|string|true|none||none|
|»»»» __v|integer|true|none||none|
|»»»» creator|object|true|none||none|
|»»»»» id|string|true|none||none|
|»»»»» username|string|true|none||none|
|»»» share_list|[object]|true|none|共享列表|none|
|»»»» size|integer|true|none||none|
|»»»» suffix|string|true|none||none|
|»»»» category|string|true|none||none|
|»»»» hash|string|true|none||none|
|»»»» url|string|true|none||none|
|»»»» is_folder|integer|true|none||none|
|»»»» parent_id|string|true|none||none|
|»»»» source|string|true|none||none|
|»»»» download_times|integer|true|none||none|
|»»»» view_times|integer|true|none||none|
|»»»» shared|integer|true|none||none|
|»»»» is_top|integer|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» school_id|integer|true|none||none|
|»»»» createdAt|string|true|none||none|
|»»»» updatedAt|string|true|none||none|
|»»»» __v|integer|true|none||none|
|»»»» creator|object|true|none||none|
|»»»»» id|string|true|none||none|
|»»»»» username|string|true|none||none|

# 教学计划

## GET 查询教学计划

GET /v1/edu_plan

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|period|query|string| 是 |none|
|subject|query|string| 是 |none|
|grade|query|string| 是 |none|
|from_year|query|integer| 否 |学年-默认当前|
|to_year|query|integer| 否 |学年-默认当前学年|
|semester|query|string| 否 |学期(上学期、下学期)|
|Cookie|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "period": "string",
    "subject": "string",
    "grade": "string",
    "from_year": 0,
    "to_year": 0,
    "semester": "string",
    "children": [
      {
        "name": "string",
        "key": "string",
        "source_id": "string",
        "children": [
          {
            "name": null,
            "key": null,
            "source_id": null,
            "children": null,
            "id": null
          }
        ],
        "id": "string"
      }
    ],
    "school_id": 0,
    "createdAt": "string",
    "updatedAt": "string",
    "id": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» period|string|true|none||none|
|»» subject|string|true|none||none|
|»» grade|string|true|none||none|
|»» from_year|integer|true|none||none|
|»» to_year|integer|true|none||none|
|»» semester|string|true|none||none|
|»» children|[object]|true|none||none|
|»»» name|string|false|none||none|
|»»» key|string|false|none||none|
|»»» source_id|string|false|none||none|
|»»» children|[object]|false|none||none|
|»»»» name|string|true|none||none|
|»»»» key|string|true|none||none|
|»»»» source_id|string|true|none||none|
|»»»» children|[object]|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» key|string|true|none||none|
|»»»»» source_id|string|true|none||none|
|»»»»» index|integer|true|none||none|
|»»»»» id|string|true|none||none|
|»»»» id|string|true|none||none|
|»»» id|string|false|none||none|
|»» school_id|integer|true|none||none|
|»» createdAt|string|true|none||none|
|»» updatedAt|string|true|none||none|
|»» id|string|true|none||none|

## POST 保存教学计划

POST /v1/edu_plan

> Body 请求参数

```json
{
  "period": "高中",
  "subject": "数学",
  "grade": "高一",
  "from_year": 2024,
  "to_year": 2025,
  "semester": "下学期",
  "children": [
    {
      "name": "第一章 集合与常用逻辑用语",
      "key": "chapter",
      "source_id": "67d9423aecf1584f64b62950",
      "children": [
        {
          "name": "1.1 集合的概念",
          "key": "chapter",
          "source_id": "67d9423aecf1584f64b62951",
          "children": [
            {
              "name": "1.1 集合的概念",
              "key": "lesson",
              "source_id": "",
              "index": 1
            }
          ]
        },
        {
          "name": "1.2 集合间的基本关系",
          "key": "chapter",
          "source_id": "67d9423aecf1584f64b62963",
          "children": [
            {
              "name": "1.2 集合间的基本关系",
              "key": "lesson",
              "source_id": "",
              "index": 1
            }
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Cookie|header|string| 是 ||none|
|body|body|object| 否 ||none|
|» id|body|string| 否 | 计划ID|编辑时必填|
|» period|body|string| 是 | 学段|none|
|» subject|body|string| 是 | 科目|none|
|» grade|body|string| 是 | 年级|none|
|» from_year|body|integer| 否 | 学年|none|
|» to_year|body|integer| 否 | 学年|none|
|» semester|body|string| 否 | 学期|上学期、下学期|
|» children|body|[object]| 是 ||计划章节|
|»» id|body|string| 否 | ID 编号|none|
|»» name|body|string| 是 | 名字|节点名|
|»» key|body|string| 是 | 类型|exam: 考试, chapter: 章节, lesson:  课时|
|»» source_id|body|string| 是 | 知识体系章节ID|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取详细

GET /v1/edu_plan/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 查看章节资源列表

GET /v1/edu_plan/chapter/resource

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||教学计划ID|
|chapter_id|query|string| 是 ||章节ID|
|key|query|string| 是 ||资源类型|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取章节推荐试卷列表

GET /v1/edu_plan/chapter/papers

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 ||教学计划ID|
|chapter_id|query|string| 否 ||章节ID|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 查看课时资源详细

GET /v1/edu_plan/chapter/lesson/resource

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||教学计划ID|
|lesson_id|query|string| 是 ||课时ID|
|key|query|string| 是 ||类型(homework)|
|Pragma|header|string| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 查看教学计划课时选项

GET /v1/edu_plan/chapter/lesson/items

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 否 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 集备

## GET 查询详细

GET /v1/team_prep/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||none|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "name": "string",
    "end_time": "string",
    "remark": "string",
    "edu_plan_id": "string",
    "teachers": [
      {
        "id": "string",
        "name": "string",
        "phone": "string",
        "title": [
          "string"
        ],
        "xueke": [
          "string"
        ],
        "nianji": [
          "string"
        ]
      }
    ],
    "children": [
      {
        "name": "string",
        "key": "string",
        "source_id": "string",
        "children": [
          {
            "name": null,
            "key": null,
            "source_id": null,
            "children": null,
            "id": null
          }
        ],
        "id": "string"
      }
    ],
    "status": "string",
    "period": "string",
    "subject": "string",
    "grade": "string",
    "from_year": 0,
    "to_year": 0,
    "semester": "string",
    "school_id": 0,
    "main_teacher_id": "string",
    "createdAt": "string",
    "updatedAt": "string",
    "main_teacher": {
      "id": "string",
      "name": "string",
      "phone": "string",
      "title": [
        "string"
      ],
      "xueke": [
        "string"
      ],
      "nianji": [
        "string"
      ]
    },
    "overtime": 0,
    "id": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» name|string|true|none||none|
|»» end_time|string|true|none||none|
|»» remark|string|true|none||none|
|»» edu_plan_id|string|true|none||none|
|»» teachers|[object]|true|none||none|
|»»» id|string|true|none||none|
|»»» name|string|true|none||none|
|»»» phone|string|true|none||none|
|»»» title|[string]|true|none||none|
|»»» xueke|[string]|true|none||none|
|»»» nianji|[string]|true|none||none|
|»» children|[object]|true|none||none|
|»»» name|string|false|none||none|
|»»» key|string|false|none||none|
|»»» source_id|string|false|none||none|
|»»» children|[object]|false|none||none|
|»»»» name|string|true|none||none|
|»»»» key|string|true|none||none|
|»»»» source_id|string|true|none||none|
|»»»» children|[object]|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» key|string|true|none||none|
|»»»»» source_id|string|true|none||none|
|»»»»» index|integer|true|none||none|
|»»»»» id|string|true|none||none|
|»»»»» items|[string]|true|none||none|
|»»»»» items_status|object|true|none||none|
|»»»»»» homework|integer|true|none||none|
|»»»» id|string|true|none||none|
|»»» id|string|false|none||none|
|»» status|string|true|none||none|
|»» period|string|true|none||none|
|»» subject|string|true|none||none|
|»» grade|string|true|none||none|
|»» from_year|integer|true|none||none|
|»» to_year|integer|true|none||none|
|»» semester|string|true|none||none|
|»» school_id|integer|true|none||none|
|»» main_teacher_id|string|true|none||none|
|»» createdAt|string|true|none||none|
|»» updatedAt|string|true|none||none|
|»» main_teacher|object|true|none||none|
|»»» id|string|true|none||none|
|»»» name|string|true|none||none|
|»»» phone|string|true|none||none|
|»»» title|[string]|true|none||none|
|»»» xueke|[string]|true|none||none|
|»»» nianji|[string]|true|none||none|
|»» overtime|integer|true|none||none|
|»» id|string|true|none||none|

## DELETE 根据ID删除

DELETE /v1/team_prep/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|string|true|none||none|

## GET 查询列表

GET /v1/team_prep/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|offset|query|string| 是 ||none|
|limit|query|string| 是 ||none|
|period|query|string| 是 ||none|
|subject|query|string| 是 ||none|
|grade|query|string| 是 ||none|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 保存集备

POST /v1/team_prep/

> Body 请求参数

```json
{
  "edu_plan_id": "6830244474636f3157d950ea",
  "name": "测试集备1",
  "end_time": "2025-05-30",
  "remark": "测试",
  "teachers": [
    "5590548544831488",
    "5593271049076736"
  ],
  "children": [
    {
      "id": "6830244474636f3157d950e5",
      "name": "第一章 集合与常用逻辑用语",
      "key": "chapter",
      "source_id": "67d9423aecf1584f64b62950",
      "children": [
        {
          "id": "6830244474636f3157d950e6",
          "name": "1.1 集合的概念",
          "key": "chapter",
          "source_id": "67d9423aecf1584f64b62951",
          "children": [
            {
              "id": "6830244474636f3157d950e7",
              "name": "1.1 集合的概念",
              "key": "lesson",
              "source_id": "",
              "index": 1,
              "items": [
                "homework"
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Cookie|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» id|body|string| 否 | 集备ID|编辑时候不能为空|
|» edu_plan_id|body|string| 是 | 教学计划ID|none|
|» name|body|string| 是 | 名称|none|
|» end_time|body|string| 是 | 结束日期|none|
|» remark|body|string| 是 | 说明|none|
|» teachers|body|[string]| 是 | 参备教师|none|
|» children|body|[object]| 是 | 选择课时信息|none|
|»» id|body|string| 是 ||none|
|»» name|body|string| 是 ||none|
|»» key|body|string| 是 ||none|
|»» source_id|body|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## PUT 修改状态

PUT /v1/team_prep/{id}/status/{status}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||none|
|status|path|string| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|string|true|none||none|

## POST 保存课时资源

POST /v1/team_prep/chapter/lesson/resource

> Body 请求参数

```json
{
  "id": "683024c374636f3157d950eb",
  "lesson_id": "6830244474636f3157d950e7",
  "key": "homework",
  "children": [
    {
      "id": 4278250179,
      "source": "sys"
    },
    {
      "id": 4283951811,
      "source": "sys"
    },
    {
      "id": 4293520067,
      "source": "sys"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Cookie|header|string| 是 ||none|
|body|body|object| 否 ||none|
|» id|body|string| 是 | 集备ID|none|
|» lesson_id|body|string| 是 | 课时ID|none|
|» key|body|string| 是 | 资源类型|作业: homework|
|» children|body|[object]| 是 | 资源列表|none|
|»» id|body|any| 是 ||资源ID|
|»»» *anonymous*|body|object| 否 ||none|
|»»» *anonymous*|body|object| 否 ||none|
|»» source|body|string| 是 | 来源|sys: 系统|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取课时作业

GET /v1/team_prep/chapter/lesson/resource

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|query|string| 是 ||集备ID|
|lesson_id|query|string| 是 ||课时ID|
|key|query|string| 是 ||资源类型(homework)|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 试卷

## POST 根据内容下载试卷

POST /v1/paper/content_download

> Body 请求参数

```json
{
  "filename": "string",
  "content": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» filename|body|string| 是 | 试卷名|none|
|» content|body|string| 是 | 试卷内容html|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获取试卷详细

GET /v1/paper/68353835618e0676730a8881

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|from|query|string| 否 ||none|
|Pragma|header|string| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string",
    "name": "string",
    "period": "string",
    "grade": "string",
    "subject": "string",
    "type": "string",
    "from_year": 0,
    "to_year": 0,
    "subtitle": "string",
    "score": 0,
    "duration": 0,
    "paper_info": "string",
    "cand_info": "string",
    "attentions": "string",
    "secret_tag": "string",
    "gutter": 0,
    "template": "string",
    "source": "string",
    "source_id": "string",
    "status": "string",
    "exam_status": "string",
    "volumes": [
      {
        "title": "string",
        "note": "string",
        "blocks": [
          {
            "title": null,
            "note": null,
            "type": null,
            "default_score": null,
            "questions": null
          }
        ]
      }
    ],
    "ctime": 0,
    "utime": 0,
    "from_enum": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|
|»» name|string|true|none||none|
|»» period|string|true|none||none|
|»» grade|string|true|none||none|
|»» subject|string|true|none||none|
|»» type|string|true|none||none|
|»» from_year|integer|true|none||none|
|»» to_year|integer|true|none||none|
|»» subtitle|string|true|none||none|
|»» score|integer|true|none||none|
|»» duration|integer|true|none||none|
|»» paper_info|string|true|none||none|
|»» cand_info|string|true|none||none|
|»» attentions|string|true|none||none|
|»» secret_tag|string|true|none||none|
|»» gutter|integer|true|none||none|
|»» template|string|true|none||none|
|»» source|string|true|none||none|
|»» source_id|string|true|none||none|
|»» status|string|true|none||none|
|»» exam_status|string|true|none||none|
|»» volumes|[object]|true|none||none|
|»»» title|string|true|none||none|
|»»» note|string|true|none||none|
|»»» blocks|[object]|true|none||none|
|»»»» title|string|true|none||none|
|»»»» note|string|true|none||none|
|»»»» type|string|true|none||none|
|»»»» default_score|integer|true|none||none|
|»»»» questions|[object]|true|none||none|
|»»»»» id|string|true|none||none|
|»»»»» subject|string|true|none||none|
|»»»»» period|string|true|none||none|
|»»»»» description|string|true|none||none|
|»»»»» comment|string|true|none||none|
|»»»»» blocks|object|true|none||none|
|»»»»»» stems|[object]|true|none||none|
|»»»»»»» stem|string|true|none||none|
|»»»»»»» options|object|true|none||none|
|»»»»»»»» A|string|true|none||none|
|»»»»»»»» B|string|true|none||none|
|»»»»»»»» C|string|true|none||none|
|»»»»»»»» D|string|true|none||none|
|»»»»»» answers|[oneOf]|true|none||none|

*oneOf*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»»»» *anonymous*|array|false|none||none|

*xor*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»»»» *anonymous*|string|false|none||none|

*continued*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»»» explanations|[string]|true|none||none|
|»»»»»» solutions|[string]|true|none||none|
|»»»»»» types|[string]|true|none||none|
|»»»»»» knowledges|[array]|true|none||none|
|»»»»»»» id|integer|true|none||none|
|»»»»»»» name|string|true|none||none|
|»»»»»»» chance|number|true|none||none|
|»»»»»»» know_methods|[string]|true|none||none|
|»»»»»»» targets|[string]|true|none||none|
|»»»» core_knowledges|[array]|true|none||none|
|»»» difficulty|integer|true|none||none|
|»»» type|string|true|none||none|
|»»» score|integer|true|none||none|
|»»» refer_exampapers|[string]|true|none||none|
|»»» year|integer|true|none||none|
|»»» ctime|string|true|none||none|
|»»» utime|string|true|none||none|
|»»» source|string|true|none||none|
|»»» source_id|string|true|none||none|
|ctime|integer|true|none||none|
|utime|integer|true|none||none|
|from_enum|integer|true|none||none|

# 算法

## POST 章节推荐试卷

POST /recommend/sync_paper

> Body 请求参数

```json
{
  "period": "高中",
  "subject": "数学",
  "type": "期中考试",
  "grade": "高三",
  "term": "下学期",
  "trees": [],
  "ai_paper": 0,
  "real_paper": 10,
  "school_id": 61872
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 试题

## GET 获取试题详细

GET /v1/internal/question/{id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||none|
|access-key|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# boss

## GET 获取所有应用使用情况

GET /v1/boss/customer/app_usage/get_all_by_school_id

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "schoolId": 0,
    "appUsages": [
      {
        "type": 0,
        "name": "string",
        "appCategory": "string",
        "productCategory": "string",
        "beginDate": "string",
        "endDate": "string",
        "leftDays": 0,
        "status": 0,
        "isTrial": 0,
        "code": "string",
        "productCategoryCode": "string",
        "params": [
          {
            "name": null,
            "value": null
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» schoolId|integer|true|none||none|
|»» appUsages|[object]|true|none||none|
|»»» type|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» appCategory|string|true|none||none|
|»»» productCategory|string|true|none||none|
|»»» beginDate|string|true|none||none|
|»»» endDate|string|true|none||none|
|»»» leftDays|integer|true|none||none|
|»»» status|integer|true|none||none|
|»»» isTrial|integer|true|none||none|
|»»» code|string|true|none||none|
|»»» productCategoryCode|string|true|none||none|
|»»» params|[object]|true|none||none|
|»»»» name|string|true|none||none|
|»»»» value|[string]|true|none||none|

# kb_api

## GET 根据ID获取试题信息

GET /kb_api/v2/questions/{question_id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|question_id|path|number| 是 ||none|
|api_key|query|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "elite": 0,
  "description": "string",
  "type": "string",
  "comment": "string",
  "difficulty": 0,
  "period": "string",
  "subject": "string",
  "ctime": "string",
  "from": "string",
  "year": 0,
  "refer_exampapers": [
    {
      "name": "string",
      "year": 0,
      "from_year": 0,
      "region": "string",
      "province": "string",
      "city": "string",
      "provinces": [
        {
          "name": "string",
          "cities": [
            "string"
          ]
        }
      ],
      "id": 0,
      "ques_score": 0,
      "sch_name": "string",
      "category": "string",
      "vague_name": "string",
      "from": "string",
      "grade": "string",
      "score": 0
    }
  ],
  "dbs": [
    "string"
  ],
  "refer_times": 0,
  "knowledges": [
    {
      "id": 0,
      "name": "string",
      "chance": 0,
      "score": 0,
      "videos": [
        "string"
      ]
    }
  ],
  "elements": [
    "string"
  ],
  "blocks": {
    "types": [
      "string"
    ],
    "explanations": [
      "string"
    ],
    "solutions": [
      "string"
    ],
    "answers": [
      "string"
    ],
    "stems": [
      {
        "options": {
          "A": "string",
          "B": "string",
          "C": "string",
          "D": "string"
        },
        "stem": "string"
      }
    ],
    "knowledges": [
      [
        {
          "id": 0,
          "name": "string",
          "chance": 0,
          "score": 0
        }
      ]
    ],
    "core_knowledges": [
      [
        "string"
      ]
    ],
    "elements": [
      [
        "string"
      ]
    ],
    "text_tags": [
      "string"
    ]
  },
  "core_knowledges": [
    "string"
  ],
  "id": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» elite|integer|true|none||none|
|» description|string|true|none||none|
|» type|string|true|none||none|
|» comment|string|true|none||none|
|» difficulty|integer|true|none||none|
|» period|string|true|none||none|
|» subject|string|true|none||none|
|» ctime|string|true|none||none|
|» from|string|true|none||none|
|» year|integer|true|none||none|
|» refer_exampapers|[object]|true|none||none|
|»» name|string|false|none||none|
|»» year|integer|false|none||none|
|»» from_year|integer|false|none||none|
|»» region|string|false|none||none|
|»» province|string|false|none||none|
|»» city|string|false|none||none|
|»» provinces|[object]|false|none||none|
|»»» name|string|false|none||none|
|»»» cities|[string]|false|none||none|
|»» id|integer|false|none||none|
|»» ques_score|integer|false|none||none|
|»» sch_name|string|false|none||none|
|»» category|string|false|none||none|
|»» vague_name|string|false|none||none|
|»» from|string|false|none||none|
|»» grade|string|false|none||none|
|»» score|number|false|none||none|
|» dbs|[string]|true|none||none|
|» refer_times|integer|true|none||none|
|» knowledges|[object]|true|none||none|
|»» id|integer|true|none||none|
|»» name|string|true|none||none|
|»» chance|integer|true|none||none|
|»» score|integer|true|none||none|
|»» videos|[string]|true|none||none|
|» elements|[string]|true|none||none|
|» blocks|object|true|none||none|
|»» types|[string]|true|none||none|
|»» explanations|[string]|true|none||none|
|»» solutions|[string]|true|none||none|
|»» answers|[string]|true|none||none|
|»» stems|[object]|true|none||none|
|»»» options|object|false|none||none|
|»»»» A|string|true|none||none|
|»»»» B|string|true|none||none|
|»»»» C|string|true|none||none|
|»»»» D|string|true|none||none|
|»»» stem|string|false|none||none|
|»» knowledges|[array]|true|none||none|
|»»» id|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» chance|integer|true|none||none|
|»»» score|integer|true|none||none|
|core_knowledges|[array]|true|none||none|
|elements|[array]|true|none||none|
|text_tags|[string]|true|none||none|
|core_knowledges|[string]|true|none||none|
|id|integer|true|none||none|

# 好分数-教师端

## GET 获取教师考试列表

GET /v3/teachers/exams

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|teacherId|query|string| 否 ||none|
|appKey|query|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "name": "string",
      "paper_id": "string",
      "subject": "string",
      "event_time": "string",
      "publish_time": "string",
      "grade": "string",
      "manfen": 0,
      "type": 0,
      "paper": "string",
      "classes": [
        {
          "name": "string",
          "id": "string"
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|false|none||none|
|»» name|string|false|none||none|
|»» paper_id|string|false|none||none|
|»» subject|string|false|none||none|
|»» event_time|string|false|none||none|
|»» publish_time|string|false|none||none|
|»» grade|string|false|none||none|
|»» manfen|integer|false|none||none|
|»» type|integer|false|none||none|
|»» paper|string|false|none||none|
|»» classes|[object]|false|none||none|
|»»» name|string|true|none||none|
|»»» id|string|true|none||none|

# 教材

## POST 教材章节推荐资源

POST /v1/book/chapter/papers

> Body 请求参数

```json
{
  "book_id": **********,
  "chapter_id": 784990207,
  "grade": "七年级"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Cookie|header|string| 是 ||none|
|body|body|object| 否 ||none|
|» book_id|body|number| 是 | 教材ID|none|
|» chapter_id|body|number| 是 | 章节ID|none|
|» grade|body|string| 是 | 年级|none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 考后一节课

## GET 好分数-获取考试列表

GET /v3/classes/exam-list-v2

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|unify_sid|cookie|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "list": [
      {
        "id": 0,
        "name": "string",
        "grade": "string",
        "type": 0,
        "scale": 0,
        "eventTime": "string",
        "papers": [
          {
            "id": null,
            "paper": null,
            "subject": null,
            "name": null,
            "classes": null
          }
        ],
        "creator": "string",
        "beginTime": "string",
        "endTime": "string",
        "status": "string"
      }
    ],
    "grades": [
      "string"
    ],
    "subjects": [
      "string"
    ],
    "schoolYears": [
      {
        "schoolYear": 0,
        "name": "string"
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» list|[object]|true|none||none|
|»»» id|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» grade|string|true|none||none|
|»»» type|integer|true|none||none|
|»»» scale|integer|true|none||none|
|»»» eventTime|string|true|none||none|
|»»» papers|[object]|true|none||none|
|»»»» id|string|true|none||none|
|»»»» paper|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» classes|[object]|true|none||none|
|»»»»» id|string|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» class_type|integer|true|none||none|
|»»»»» grade|string|true|none||none|
|»»» creator|string|true|none||none|
|»»» beginTime|string|true|none||none|
|»»» endTime|string|true|none||none|
|»»» status|string|true|none||none|
|»» grades|[string]|true|none||none|
|»» subjects|[string]|true|none||none|
|»» schoolYears|[object]|true|none||none|
|»»» schoolYear|integer|true|none||none|
|»»» name|string|true|none||none|

## GET 获取班级列表

GET /v2/exam/{exam_id}/paper/{paper_id}/classes

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||考试ID|
|paper_id|path|string| 是 ||科目ID|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "name": "string",
      "relation": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|true|none||none|
|»» name|string|true|none||none|
|»» relation|integer|true|none||none|

## GET 获取试题列表

GET /v2/exam/{exam_id}/paper/{paper_id}/questions

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||考试ID|
|paper_id|path|string| 是 ||科目ID|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "class_info": [
      {
        "id": "string",
        "name": "string",
        "courseware_name": "string",
        "questions": [
          "string"
        ]
      }
    ],
    "questions": [
      {
        "id": "string",
        "type": 0,
        "typeOnPaper": 0,
        "name": "string",
        "key": "string",
        "manfen": 0,
        "classScoreRate": null,
        "gradeScoreRate": 0,
        "avgScore": null,
        "gradeScore": 0,
        "pictures": [
          "string"
        ],
        "rightNum": 0,
        "halfRightNum": 0,
        "wrongNum": 0,
        "knowledges": [
          {
            "id": null,
            "name": null
          }
        ],
        "questionId": 0,
        "answer": "string",
        "options": [
          "string"
        ],
        "answerStatistics": {
          "01班": {},
          "02班": {},
          "18班": {},
          "20班": {}
        },
        "answerSheet": [
          {
            "class": null,
            "answer": null,
            "student": null,
            "score": null,
            "id": null
          }
        ],
        "scoringRate": {
          "01班": "string",
          "02班": "string",
          "18班": "string",
          "20班": "string"
        },
        "sim": [
          {
            "elite": null,
            "description": null,
            "type": null,
            "comment": null,
            "difficulty": null,
            "period": null,
            "subject": null,
            "ctime": null,
            "from": null,
            "year": null,
            "refer_exampapers": null,
            "refer_times": null,
            "knowledges": null,
            "elements": null,
            "blocks": null,
            "core_knowledges": null,
            "dbs": null,
            "id": null,
            "type_tags": null,
            "tags": null,
            "reco_questions": null,
            "attach": null,
            "use_times": null
          }
        ],
        "scores": [
          {
            "min": null,
            "max": null,
            "students": null
          }
        ],
        "excellentAnswers": [
          {
            "answer": null,
            "id": null,
            "name": null,
            "className": null,
            "score": null
          }
        ],
        "xbAnswers": [
          {
            "answer": null,
            "id": null,
            "name": null,
            "className": null,
            "score": null
          }
        ],
        "mark_info": {
          "excellent": [
            null
          ],
          "mediocre": [
            null
          ]
        }
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» class_info|[object]|true|none||none|
|»»» id|string|true|none||none|
|»»» name|string|true|none||none|
|»»» courseware_name|string|true|none||none|
|»»» questions|[string]|true|none||none|
|»» questions|[object]|true|none||none|
|»»» id|string|true|none||none|
|»»» type|integer|true|none||none|
|»»» typeOnPaper|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» key|string|true|none||none|
|»»» manfen|integer|true|none||none|
|»»» classScoreRate|null|true|none||none|
|»»» gradeScoreRate|number|true|none||none|
|»»» avgScore|null|true|none||none|
|»»» gradeScore|number|true|none||none|
|»»» pictures|[string]|true|none||none|
|»»» rightNum|integer|true|none||none|
|»»» halfRightNum|integer|true|none||none|
|»»» wrongNum|integer|true|none||none|
|»»» knowledges|[object]|true|none||none|
|»»»» id|integer|true|none||none|
|»»»» name|string|true|none||none|
|»»» questionId|integer|true|none||none|
|»»» answer|string|true|none||none|
|»»» options|[string]|true|none||none|
|»»» answerStatistics|object|true|none||none|
|»»»» 01班|object|true|none||none|
|»»»»» B|integer|true|none||none|
|»»»»» A|integer|false|none||none|
|»»»»» D|integer|true|none||none|
|»»»»» C|integer|true|none||none|
|»»»»» -|integer|false|none||none|
|»»»»» BC|integer|true|none||none|
|»»»»» AC|integer|true|none||none|
|»»»»» BD|integer|true|none||none|
|»»»»» AD|integer|false|none||none|
|»»»»» ABC|integer|false|none||none|
|»»»»» CD|integer|true|none||none|
|»»»»» ACD|integer|false|none||none|
|»»»»» AB|integer|true|none||none|
|»»»»» BCD|integer|true|none||none|
|»»»» 02班|object|true|none||none|
|»»»»» B|integer|true|none||none|
|»»»»» A|integer|false|none||none|
|»»»»» D|integer|false|none||none|
|»»»»» C|integer|true|none||none|
|»»»»» -|integer|false|none||none|
|»»»»» BC|integer|true|none||none|
|»»»»» AC|integer|true|none||none|
|»»»»» AB|integer|false|none||none|
|»»»»» BD|integer|true|none||none|
|»»»»» ABC|integer|false|none||none|
|»»»»» ABD|integer|false|none||none|
|»»»»» AD|integer|false|none||none|
|»»»»» ACD|integer|false|none||none|
|»»»»» CD|integer|true|none||none|
|»»»»» BCD|integer|true|none||none|
|»»»» 18班|object|true|none||none|
|»»»»» B|integer|true|none||none|
|»»»»» C|integer|true|none||none|
|»»»»» A|integer|true|none||none|
|»»»»» D|integer|false|none||none|
|»»»»» ABC|integer|true|none||none|
|»»»»» BC|integer|true|none||none|
|»»»»» AB|integer|true|none||none|
|»»»»» AC|integer|true|none||none|
|»»»»» ABD|integer|true|none||none|
|»»»»» AD|integer|true|none||none|
|»»»»» ABCD|integer|true|none||none|
|»»»»» ACD|integer|true|none||none|
|»»»»» CD|integer|true|none||none|
|»»»»» BD|integer|true|none||none|
|»»»»» -|integer|false|none||none|
|»»»»» BCD|integer|true|none||none|
|»»»» 20班|object|true|none||none|
|»»»»» B|integer|true|none||none|
|»»»»» C|integer|true|none||none|
|»»»»» A|integer|true|none||none|
|»»»»» D|integer|true|none||none|
|»»»»» BD|integer|true|none||none|
|»»»»» -|integer|true|none||none|
|»»»»» AC|integer|true|none||none|
|»»»»» BC|integer|true|none||none|
|»»»»» BCD|integer|true|none||none|
|»»»»» AB|integer|true|none||none|
|»»»»» ABC|integer|true|none||none|
|»»»»» AD|integer|true|none||none|
|»»»»» ACD|integer|true|none||none|
|»»»»» ABD|integer|true|none||none|
|»»»»» CD|integer|true|none||none|
|»»» answerSheet|[object]|true|none||none|
|»»»» class|string|true|none||none|
|»»»» answer|[string]|true|none||none|
|»»»» student|string|true|none||none|
|»»»» score|integer|true|none||none|
|»»»» id|integer|true|none||none|
|»»» scoringRate|object|true|none||none|
|»»»» 01班|string|true|none||none|
|»»»» 02班|string|true|none||none|
|»»»» 18班|string|true|none||none|
|»»»» 20班|string|true|none||none|
|»»» sim|[object]|true|none||none|
|»»»» elite|integer|true|none||none|
|»»»» description|string|true|none||none|
|»»»» type|string|true|none||none|
|»»»» comment|string|true|none||none|
|»»»» difficulty|integer|true|none||none|
|»»»» period|string|true|none||none|
|»»»» subject|string|true|none||none|
|»»»» ctime|string|true|none||none|
|»»»» from|string|true|none||none|
|»»»» year|integer|true|none||none|
|»»»» refer_exampapers|[object]|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» year|integer|true|none||none|
|»»»»» from_year|integer|true|none||none|
|»»»»» region|string|true|none||none|
|»»»»» province|string|true|none||none|
|»»»»» city|string|true|none||none|
|»»»»» provinces|[object]|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» cities|[string]|true|none||none|
|»»»»» id|integer|true|none||none|
|»»»»» ques_score|integer|true|none||none|
|»»»»» sch_name|string|true|none||none|
|»»»»» category|string|true|none||none|
|»»»»» vague_name|string|true|none||none|
|»»»»» from|string|true|none||none|
|»»»»» grade|string|true|none||none|
|»»»»» score|number|true|none||none|
|»»»» refer_times|integer|true|none||none|
|»»»» knowledges|[object]|true|none||none|
|»»»»» id|integer|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» chance|integer|true|none||none|
|»»»»» score|integer|true|none||none|
|»»»»» videos|[object]|true|none||none|
|»»»»»» id|integer|true|none||none|
|»»»»»» url|string|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» similarity|integer|true|none||none|
|»»»»»» from|string|true|none||none|
|»»»»»» type|string|true|none||none|
|»»»»»» order|integer|true|none||none|
|»»»»» know_methods|[object]|true|none||none|
|»»»»»» id|integer|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» coefficient|integer¦null|true|none||none|
|»»»»» targets|[object]|true|none||none|
|»»»»»» id|integer|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» coefficient|number¦null|true|none||none|
|»»»» elements|[string]|true|none||none|
|»»»» blocks|object|true|none||none|
|»»»»» types|[string]|true|none||none|
|»»»»» explanations|[string]|true|none||none|
|»»»»» solutions|[string]|true|none||none|
|»»»»» answers|[oneOf]|true|none||none|

*oneOf*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»»» *anonymous*|array|false|none||none|

*xor*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»»» *anonymous*|string|false|none||none|

*continued*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|»»»»» stems|[object]|true|none||none|
|»»»»»» options|object|true|none||none|
|»»»»»»» A|string|true|none||none|
|»»»»»»» B|string|true|none||none|
|»»»»»»» C|string|true|none||none|
|»»»»»»» D|string|true|none||none|
|»»»»»» stem|string|true|none||none|
|»»»»» knowledges|[array]|true|none||none|
|»»»»»» id|integer|true|none||none|
|»»»»»» name|string|true|none||none|
|»»»»»» score|number|true|none||none|
|»»»»»» chance|number|true|none||none|
|»»»»»» know_methods|[object]|true|none||none|
|»»»»»»» name|string|true|none||none|
|»»»»»»» id|integer|true|none||none|
|»»»»»»» coefficient|integer|true|none||none|
|»»»»»» targets|[object]|true|none||none|
|»»»»»»» name|string|true|none||none|
|»»»»»»» id|integer|true|none||none|
|»»» core_knowledges|[array]|true|none||none|
|»»» elements|[array]|true|none||none|
|»»» text_tags|[string]|true|none||none|
|»» core_knowledges|[string]|true|none||none|
|»» dbs|[string]|true|none||none|
|»» id|integer|true|none||none|
|»» type_tags|[string]|true|none||none|
|»» tags|object|true|none||none|
|»»» count_sub_ques|integer|true|none||none|
|»»» count_options|[integer]|true|none||none|
|»»» grades|[string]|true|none||none|
|»» reco_questions|[integer]|true|none||none|
|»» attach|object|true|none||none|
|»»» is_mobile|integer|false|none||none|
|»»» is_new_gaokao|integer|true|none||none|
|»»» is_famous|integer|false|none||none|
|»» use_times|integer|true|none||none|
|» scores|[object]|true|none||none|
|»» min|integer|true|none||none|
|»» max|integer|true|none||none|
|»» students|[string]|true|none||none|
|» excellentAnswers|[object]|true|none||none|
|»» answer|[string]|true|none||none|
|»» id|integer|true|none||none|
|»» name|string|true|none||none|
|»» className|string|true|none||none|
|»» score|integer|true|none||none|
|» xbAnswers|[object]|true|none||none|
|»» answer|[string]|true|none||none|
|»» id|integer|true|none||none|
|»» name|string|true|none||none|
|»» className|string|true|none||none|
|»» score|integer|true|none||none|
|» mark_info|object|true|none||none|
|»» excellent|[object]|true|none||none|
|»»» answer|[string]|true|none||none|
|»»» id|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» className|string|true|none||none|
|»»» score|integer|true|none||none|
|»» mediocre|[string]|true|none||none|

## PUT 保存讲评试题信息

PUT /v2/exam/{exam_id}/paper/{paper_id}/comment

> Body 请求参数

```json
[
  {
    "id": "000000000000000251097598",
    "name": "01班",
    "courseware_name": "01班一中数据复制讲评课件11",
    "questions": [
      "1.1",
      "1.2"
    ]
  },
  {
    "id": "000000000000000251097606",
    "name": "02班",
    "courseware_name": "02班一中数据复制讲评课件",
    "questions": [
      "1.1",
      "1.2"
    ]
  },
  {
    "id": "000000000000000251097607",
    "name": "18班",
    "courseware_name": "18班一中数据复制讲评课件",
    "questions": [
      "1.1",
      "1.2"
    ]
  },
  {
    "id": "000000000000000251052402",
    "name": "20班",
    "courseware_name": "20班一中数据复制讲评课件",
    "questions": [
      "1.1",
      "1.2"
    ]
  }
]
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||考试ID|
|paper_id|path|string| 是 ||科目ID|
|Cookie|header|string| 否 ||none|
|body|body|object| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 相似题搜索

POST /v2/exam/{exam_id}/paper/{paper_id}/question/search

> Body 请求参数

```json
{
  "offset": 20,
  "limit": 10,
  "knowledges": [
    {
      "id": 2128674815,
      "name": "正弦定理"
    },
    {
      "id": 2128543743,
      "name": "余弦定理"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||none|
|paper_id|path|string| 是 ||none|
|Cookie|header|string| 否 ||none|
|body|body|object| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "elite": 0,
        "description": "string",
        "type": "string",
        "comment": "string",
        "difficulty": 0,
        "period": "string",
        "subject": "string",
        "ctime": "string",
        "from": "string",
        "year": 0,
        "refer_exampapers": [
          {
            "name": null,
            "year": null,
            "from_year": null,
            "region": null,
            "province": null,
            "city": null,
            "provinces": null,
            "id": null,
            "ques_score": null,
            "sch_name": null,
            "category": null,
            "vague_name": null,
            "from": null,
            "grade": null,
            "score": null
          }
        ],
        "refer_times": 0,
        "knowledges": [
          {
            "id": null,
            "name": null,
            "chance": null,
            "score": null,
            "know_methods": null,
            "targets": null,
            "videos": null
          }
        ],
        "elements": [
          "string"
        ],
        "blocks": {
          "types": [
            null
          ],
          "explanations": [
            null
          ],
          "solutions": [
            null
          ],
          "answers": [
            null
          ],
          "stems": [
            null
          ],
          "knowledges": [
            null
          ],
          "core_knowledges": [
            null
          ],
          "elements": [
            null
          ],
          "text_tags": [
            null
          ]
        },
        "core_knowledges": [
          "string"
        ],
        "dbs": [
          "string"
        ],
        "id": 0,
        "show_tags": [
          "string"
        ],
        "attach": {
          "is_new_gaokao": 0
        },
        "reco_questions": [
          0
        ],
        "use_times": 0
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» total|integer|true|none||none|
|»» list|[object]|true|none||none|
|»»» elite|integer|true|none||none|
|»»» description|string|true|none||none|
|»»» type|string|true|none||none|
|»»» comment|string|true|none||none|
|»»» difficulty|integer|true|none||none|
|»»» period|string|true|none||none|
|»»» subject|string|true|none||none|
|»»» ctime|string|true|none||none|
|»»» from|string|true|none||none|
|»»» year|integer|true|none||none|
|»»» refer_exampapers|[object]|true|none||none|
|»»»» name|string|true|none||none|
|»»»» year|integer|true|none||none|
|»»»» from_year|integer|true|none||none|
|»»»» region|string|true|none||none|
|»»»» province|string|true|none||none|
|»»»» city|string|true|none||none|
|»»»» provinces|[object]|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» cities|[string]|true|none||none|
|»»»» id|integer|true|none||none|
|»»»» ques_score|integer|true|none||none|
|»»»» sch_name|string|true|none||none|
|»»»» category|string|true|none||none|
|»»»» vague_name|string|true|none||none|
|»»»» from|string|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» score|number|true|none||none|
|»»» refer_times|integer|true|none||none|
|»»» knowledges|[object]|true|none||none|
|»»»» id|integer|true|none||none|
|»»»» name|string|true|none||none|
|»»»» chance|number|true|none||none|
|»»»» score|number|true|none||none|
|»»»» know_methods|[string]|true|none||none|
|»»»» targets|[string]|true|none||none|
|»»»» videos|[object]|true|none||none|
|»»»»» id|integer|true|none||none|
|»»»»» url|string|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» similarity|number|true|none||none|
|»»»»» from|string|true|none||none|
|»»»»» type|string|true|none||none|
|»»»»» order|integer|true|none||none|
|»»» elements|[string]|true|none||none|
|»»» blocks|object|true|none||none|
|»»»» types|[string]|true|none||none|
|»»»» explanations|[string]|true|none||none|
|»»»» solutions|[string]|true|none||none|
|»»»» answers|[oneOf]|true|none||none|
|»»»» stems|[object]|true|none||none|
|»»»»» stem|string|true|none||none|
|»»»»» options|object|true|none||none|
|»»»»»» A|string|true|none||none|
|»»»»»» B|string|true|none||none|
|»»»»»» C|string|true|none||none|
|»»»»»» D|string|true|none||none|
|»»»» knowledges|[array]|true|none||none|
|»»»»» id|integer|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» chance|number|true|none||none|
|»»»»» score|number|true|none||none|
|»» core_knowledges|[array]|true|none||none|
|»» elements|[array]|true|none||none|
|»» text_tags|[string]|true|none||none|
|» core_knowledges|[string]|true|none||none|
|» dbs|[string]|true|none||none|
|» id|integer|true|none||none|
|» show_tags|[string]|true|none||none|
|» attach|object|true|none||none|
|»» is_new_gaokao|integer|true|none||none|
|» reco_questions|[integer]|false|none||none|
|» use_times|integer|false|none||none|

## PUT 保存变式练习

PUT /v2/exam/{exam_id}/paper/{paper_id}/question/{key}/sim

> Body 请求参数

```json
{
  "ids": [
    2367485123
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||考试ID|
|paper_id|path|string| 是 ||科目ID|
|key|path|string| 是 ||试题key|
|Cookie|header|string| 否 ||none|
|body|body|object| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## PUT 试题优秀作答/典型错答标记

PUT /v2/exam/{exam_id}/paper/{paper_id}/question/{key}/answer/mark

> Body 请求参数

```json
{
  "type": "excellent",
  "student_id": 178673522
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||none|
|paper_id|path|string| 是 ||none|
|key|path|string| 是 ||none|
|Cookie|header|string| 是 ||none|
|body|body|object| 否 ||none|
|» type|body|string| 是 | 类型|none|
|» student_id|body|number| 是 | 学生ID|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## GET 获取平行试卷

GET /v2/exam/{exam_id}/paper/{paper_id}/category/{category}/paper

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||none|
|paper_id|path|string| 是 ||none|
|category|path|integer| 是 ||none|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|string|true|none||none|

## PUT 修改讲评数据状态

PUT /v2/exam/{exam_id}/paper/{paper_id}/status/{status}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|exam_id|path|string| 是 ||none|
|paper_id|path|string| 是 ||none|
|status|path|string| 是 ||状态|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "id": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» id|string|true|none||none|

## GET 获取讲评列表

GET /v2/exam/comment/list

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|offset|query|integer| 是 ||分页参数|
|limit|query|integer| 是 ||分页参数|
|Cookie|header|string| 是 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "list": [
      {
        "exam_id": "string",
        "exam_name": "string",
        "paper_id": "string",
        "period": "string",
        "subject": "string",
        "grade": "string",
        "class_info": [
          {
            "id": null,
            "name": null,
            "courseware_name": null,
            "question_num": null
          }
        ],
        "id": "string",
        "ctime": 0,
        "utime": 0
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» total|integer|true|none||none|
|»» list|[object]|true|none||none|
|»»» exam_id|string|false|none||none|
|»»» exam_name|string|false|none||none|
|»»» paper_id|string|false|none||none|
|»»» period|string|false|none||none|
|»»» subject|string|false|none||none|
|»»» grade|string|false|none||none|
|»»» class_info|[object]|false|none||none|
|»»»» id|string|true|none||none|
|»»»» name|string|true|none||none|
|»»»» courseware_name|string|true|none||none|
|»»»» question_num|integer|true|none||none|
|»»» id|string|false|none||none|
|»»» ctime|integer|false|none||none|
|»»» utime|integer|false|none||none|

## GET 获取班级讲评数据详细

GET /v2/exam/comment/{id}/class/{class_id}

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|id|path|string| 是 ||讲评数据ID|
|class_id|path|string| 是 ||班级ID|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "id": "string",
      "type": 0,
      "typeOnPaper": 0,
      "name": "string",
      "key": "string",
      "manfen": 0,
      "classScoreRate": null,
      "gradeScoreRate": 0,
      "avgScore": null,
      "gradeScore": 0,
      "pictures": [
        "string"
      ],
      "rightNum": 0,
      "halfRightNum": 0,
      "wrongNum": 0,
      "knowledges": [
        {
          "id": 0,
          "name": "string"
        }
      ],
      "questionId": 0,
      "answer": "string",
      "options": [
        "string"
      ],
      "answerStatistics": {
        "01班": {
          "B": 0,
          "A": 0,
          "D": 0,
          "C": 0,
          "-": 0
        },
        "02班": {
          "B": 0,
          "A": 0,
          "D": 0,
          "C": 0
        },
        "18班": {
          "B": 0,
          "C": 0,
          "A": 0,
          "D": 0
        },
        "20班": {
          "B": 0,
          "C": 0,
          "A": 0,
          "D": 0
        }
      },
      "answerSheet": [
        "string"
      ],
      "scoringRate": {
        "01班": "string",
        "02班": "string",
        "18班": "string",
        "20班": "string"
      },
      "sim": [
        {
          "elite": 0,
          "description": "string",
          "type": "string",
          "comment": "string",
          "difficulty": 0,
          "period": "string",
          "subject": "string",
          "ctime": "string",
          "from": "string",
          "year": 0,
          "refer_exampapers": [
            {}
          ],
          "refer_times": 0,
          "knowledges": [
            {}
          ],
          "elements": [
            "string"
          ],
          "blocks": {
            "types": null,
            "explanations": null,
            "solutions": null,
            "answers": null,
            "stems": null,
            "knowledges": null,
            "core_knowledges": null,
            "elements": null,
            "text_tags": null
          },
          "core_knowledges": [
            "string"
          ],
          "dbs": [
            "string"
          ],
          "id": 0
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|[object]|true|none||none|
|»» id|string|true|none||none|
|»» type|integer|true|none||none|
|»» typeOnPaper|integer|true|none||none|
|»» name|string|true|none||none|
|»» key|string|true|none||none|
|»» manfen|integer|true|none||none|
|»» classScoreRate|null|true|none||none|
|»» gradeScoreRate|number|true|none||none|
|»» avgScore|null|true|none||none|
|»» gradeScore|number|true|none||none|
|»» pictures|[string]|true|none||none|
|»» rightNum|integer|true|none||none|
|»» halfRightNum|integer|true|none||none|
|»» wrongNum|integer|true|none||none|
|»» knowledges|[object]|true|none||none|
|»»» id|integer|true|none||none|
|»»» name|string|true|none||none|
|»» questionId|integer|true|none||none|
|»» answer|string|true|none||none|
|»» options|[string]|true|none||none|
|»» answerStatistics|object|true|none||none|
|»»» 01班|object|true|none||none|
|»»»» B|integer|true|none||none|
|»»»» A|integer|true|none||none|
|»»»» D|integer|true|none||none|
|»»»» C|integer|true|none||none|
|»»»» -|integer|true|none||none|
|»»» 02班|object|true|none||none|
|»»»» B|integer|true|none||none|
|»»»» A|integer|true|none||none|
|»»»» D|integer|true|none||none|
|»»»» C|integer|true|none||none|
|»»» 18班|object|true|none||none|
|»»»» B|integer|true|none||none|
|»»»» C|integer|true|none||none|
|»»»» A|integer|true|none||none|
|»»»» D|integer|true|none||none|
|»»» 20班|object|true|none||none|
|»»»» B|integer|true|none||none|
|»»»» C|integer|true|none||none|
|»»»» A|integer|true|none||none|
|»»»» D|integer|true|none||none|
|»» answerSheet|[string]|true|none||none|
|»» scoringRate|object|true|none||none|
|»»» 01班|string|true|none||none|
|»»» 02班|string|true|none||none|
|»»» 18班|string|true|none||none|
|»»» 20班|string|true|none||none|
|»» sim|[object]|true|none||none|
|»»» elite|integer|true|none||none|
|»»» description|string|true|none||none|
|»»» type|string|true|none||none|
|»»» comment|string|true|none||none|
|»»» difficulty|integer|true|none||none|
|»»» period|string|true|none||none|
|»»» subject|string|true|none||none|
|»»» ctime|string|true|none||none|
|»»» from|string|true|none||none|
|»»» year|integer|true|none||none|
|»»» refer_exampapers|[object]|true|none||none|
|»»»» name|string|true|none||none|
|»»»» year|integer|true|none||none|
|»»»» from_year|integer|true|none||none|
|»»»» region|string|true|none||none|
|»»»» province|string|true|none||none|
|»»»» city|string|true|none||none|
|»»»» provinces|[object]|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» cities|[string]|true|none||none|
|»»»» id|integer|true|none||none|
|»»»» ques_score|integer|true|none||none|
|»»»» sch_name|string|true|none||none|
|»»»» category|string|true|none||none|
|»»»» vague_name|string|true|none||none|
|»»»» from|string|true|none||none|
|»»»» grade|string|true|none||none|
|»»»» score|number|true|none||none|
|»»» refer_times|integer|true|none||none|
|»»» knowledges|[object]|true|none||none|
|»»»» id|integer|true|none||none|
|»»»» name|string|true|none||none|
|»»»» chance|integer|true|none||none|
|»»»» score|integer|true|none||none|
|»»»» videos|[string]|true|none||none|
|»»» elements|[string]|true|none||none|
|»»» blocks|object|true|none||none|
|»»»» types|[string]|true|none||none|
|»»»» explanations|[string]|true|none||none|
|»»»» solutions|[string]|true|none||none|
|»»»» answers|[string]|true|none||none|
|»»»» stems|[object]|true|none||none|
|»»»»» options|object|true|none||none|
|»»»»»» A|string|true|none||none|
|»»»»»» B|string|true|none||none|
|»»»»»» C|string|true|none||none|
|»»»»»» D|string|true|none||none|
|»»»»» stem|string|true|none||none|
|»»»» knowledges|[array]|true|none||none|
|»»»»» id|integer|true|none||none|
|»»»»» name|string|true|none||none|
|»»»»» chance|integer|true|none||none|
|»»»»» score|integer|true|none||none|
|»» core_knowledges|[array]|true|none||none|
|»» elements|[array]|true|none||none|
|»» text_tags|[string]|true|none||none|
|» core_knowledges|[string]|true|none||none|
|» dbs|[string]|true|none||none|
|» id|integer|true|none||none|

## GET 好分数-获取试题详细

GET /v3/classes/{cls_id}/exams/{exam_id}/papers/{paper_id}/question-detail-v2

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|cls_id|path|string| 是 ||none|
|exam_id|path|string| 是 ||none|
|paper_id|path|string| 是 ||none|
|Cookie|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_prep_catalog">prep_catalog</h2>

<a id="schemaprep_catalog"></a>
<a id="schema_prep_catalog"></a>
<a id="tocSprep_catalog"></a>
<a id="tocsprep_catalog"></a>

```json
{
  "period": "高中",
  "subject": "地理",
  "book_id": 0,
  "chapter_id": 0,
  "category": 0,
  "name": "string",
  "type": 1,
  "order": 0,
  "user_id": 0,
  "user_name": "system",
  "knowledges": "string",
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "type_id": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "初中"]|
|subject|string|true|none|学科|enum: ["地理", "历史", "政治", "生物", "化学", "物理", "英语", "语文", "数学"]|
|book_id|number|true|none|教材ID|none|
|chapter_id|number|true|none|章节ID|none|
|category|number|true|none|分类|none|
|name|string|true|none|名称|none|
|type|number|true|none|类型|enum: [1, 2]|
|order|number|true|none|排序/订单|none|
|user_id|number|true|none|用户ID|enum: [0]|
|user_name|string|true|none|用户名称|enum: ["system"]|
|knowledges|string|true|none|知识点|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|type_id|string|true|none|type的ID|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|subject|地理|
|subject|历史|
|subject|政治|
|subject|生物|
|subject|化学|
|subject|物理|
|subject|英语|
|subject|语文|
|subject|数学|
|type|1|
|type|2|
|user_id|0|
|user_name|system|
|valid|1|

<h2 id="tocS_prep_element">prep_element</h2>

<a id="schemaprep_element"></a>
<a id="schema_prep_element"></a>
<a id="tocSprep_element"></a>
<a id="tocsprep_element"></a>

```json
{
  "catalog_id": "string",
  "category": 3,
  "name": "string",
  "knowledges": [
    "string"
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "children": [
    {
      "type": 34,
      "content": [
        {
          "value": {
            "content": null,
            "contentAnswer": null
          }
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|catalog_id|string|true|none|catalog的ID|none|
|category|number|true|none|分类|enum: [3, 10, 9, 8, 7, 6, 5, 4, 2, 1]|
|name|string|true|none|名称|none|
|knowledges|[string]|true|none|知识点|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|children|[object]|true|none|子项|none|
|» type|number|true|none|类型|enum: [34]|
|» content|[object]|true|none|内容|none|
|»» value|object|true|none|值|none|
|»»» content|string|true|none|内容|none|
|»»» contentAnswer|string|true|none|contentAnswer|enum: [""]|

#### 枚举值

|属性|值|
|---|---|
|category|3|
|category|10|
|category|9|
|category|8|
|category|7|
|category|6|
|category|5|
|category|4|
|category|2|
|category|1|
|valid|1|
|type|34|
|contentAnswer||

<h2 id="tocS_prep_resource">prep_resource</h2>

<a id="schemaprep_resource"></a>
<a id="schema_prep_resource"></a>
<a id="tocSprep_resource"></a>
<a id="tocsprep_resource"></a>

```json
{
  "period": "高中",
  "subject": "地理",
  "book_id": 0,
  "chapter_id": 0,
  "type": 1,
  "name": "城镇化（学案）",
  "children": [
    {
      "id": "string",
      "name": "[课堂小结]"
    }
  ],
  "knowledges": "string",
  "view_times": 0,
  "download_times": 0,
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "file": {
    "mime": "pptx",
    "size": 0,
    "host": "string",
    "url": "string",
    "images": [
      "string"
    ]
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "初中"]|
|subject|string|true|none|学科|enum: ["地理", "历史", "政治", "生物", "化学", "物理", "英语", "语文", "数学"]|
|book_id|number|true|none|教材ID|none|
|chapter_id|number|true|none|章节ID|none|
|type|number|true|none|类型|enum: [1, 2]|
|name|string|true|none|名称|enum: ["城镇化（学案）", "时代的主题学案", "激素表格", "4.3生态工程", "第2节 电 阻", "第1节 电 流"]|
|children|[object]|true|none|子项|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["[课堂小结]"]|
|knowledges|string|true|none|知识点|none|
|view_times|number|true|none|view_times|none|
|download_times|number|true|none|下载次数|enum: [0, 1, 8, 2, 7]|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|file|object|false|none|文件信息|none|
|» mime|string|true|none|mime|enum: ["pptx"]|
|» size|number|true|none|大小|none|
|» host|string|true|none|域名|none|
|» url|string|true|none|资源地址|none|
|» images|[string]|true|none|图片|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|subject|地理|
|subject|历史|
|subject|政治|
|subject|生物|
|subject|化学|
|subject|物理|
|subject|英语|
|subject|语文|
|subject|数学|
|type|1|
|type|2|
|name|城镇化（学案）|
|name|时代的主题学案|
|name|激素表格|
|name|4.3生态工程|
|name|第2节 电 阻|
|name|第1节 电 流|
|name|[课堂小结]|
|download_times|0|
|download_times|1|
|download_times|8|
|download_times|2|
|download_times|7|
|valid|1|
|mime|pptx|

<h2 id="tocS_user_basket">user_basket</h2>

<a id="schemauser_basket"></a>
<a id="schema_user_basket"></a>
<a id="tocSuser_basket"></a>
<a id="tocsuser_basket"></a>

```json
{
  "id": 0,
  "type": "string",
  "period": "高中",
  "subject": "string",
  "source": "sys",
  "source_id": 0,
  "category": "basket",
  "user_id": "string",
  "ctime": "string",
  "utime": "string",
  "score": 3,
  "elite": 0,
  "description": "计算：",
  "comment": "",
  "blocks": {
    "types": [
      "string"
    ],
    "explanations": [
      "string"
    ],
    "solutions": [
      "string"
    ],
    "answers": [
      "string"
    ],
    "stems": [
      {
        "stem": "string"
      }
    ],
    "knowledges": [
      "string"
    ],
    "core_knowledges": [
      "string"
    ],
    "elements": [
      "string"
    ],
    "text_tags": [
      "string"
    ]
  },
  "knowledges": [
    {
      "id": 3530358783,
      "name": "实数的混合运算",
      "chance": 0,
      "score": 0,
      "know_methods": [
        "string"
      ],
      "targets": [
        "string"
      ],
      "videos": [
        "string"
      ]
    }
  ],
  "difficulty": 3,
  "refer_exampapers": [
    {
      "name": "string",
      "year": 2025,
      "from_year": 2024,
      "region": "重庆",
      "province": "重庆",
      "city": "全部地区",
      "provinces": [
        {
          "name": "重庆",
          "cities": [
            "string"
          ]
        }
      ],
      "id": 791434180,
      "ques_score": 0,
      "sch_name": "",
      "category": "期中试卷",
      "vague_name": "string",
      "from": "string",
      "grade": "七年级下",
      "score": 0.7237
    }
  ],
  "year": 2025
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|id|none|
|type|string|true|none|类型|none|
|period|string|false|none|学段|enum: ["高中", "小学", "初中"]|
|subject|string|false|none|学科|none|
|source|string|false|none|来源|enum: ["sys", "zyk", "zx", "upload"]|
|source_id|number|false|none|source的ID|none|
|category|string|false|none|分类|enum: ["basket"]|
|user_id|string|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|score|number|false|none|分数|enum: [3, 0, 10, 4, 15, 12, 14]|
|elite|number|false|none|elite|enum: [0]|
|description|string|false|none|描述|enum: ["计算："]|
|comment|string|false|none|点评/注释|enum: [""]|
|blocks|object|false|none|blocks列表|none|
|» types|[string]|true|none|types列表|none|
|» explanations|[string]|true|none|explanations列表|none|
|» solutions|[string]|true|none|solutions列表|none|
|» answers|[string]|true|none|answers列表|none|
|» stems|[object]|true|none|stems列表|none|
|»» stem|string|true|none|stem|none|
|» knowledges|[string]|true|none|知识点|none|
|» core_knowledges|[string]|true|none|core_knowledges列表|none|
|» elements|[string]|true|none|elements列表|none|
|» text_tags|[string]|true|none|text_tags列表|none|
|knowledges|[object]|false|none|知识点|none|
|» id|number|true|none|id|enum: [3530358783, 3528982527, 3529637887, 3529703423]|
|» name|string|true|none|名称|enum: ["实数的混合运算"]|
|» chance|number|true|none|chance|enum: [0]|
|» score|number|true|none|分数|enum: [0]|
|» know_methods|[string]|true|none|know_methods列表|none|
|» targets|[string]|true|none|targets列表|none|
|» videos|[string]|true|none|videos列表|none|
|difficulty|number|false|none|难度|enum: [3]|
|refer_exampapers|[object]|false|none|关联考试列表|none|
|» name|string|true|none|名称|none|
|» year|number|true|none|年份|enum: [2025]|
|» from_year|number|true|none|from_year|enum: [2024]|
|» region|string|true|none|region|enum: ["重庆"]|
|» province|string|true|none|省份|enum: ["重庆"]|
|» city|string|true|none|城市|enum: ["全部地区"]|
|» provinces|[object]|true|none|provinces列表|none|
|»» name|string|true|none|名称|enum: ["重庆"]|
|»» cities|[string]|true|none|cities列表|none|
|» id|number|true|none|id|enum: [791434180]|
|» ques_score|number|true|none|ques_score|enum: [0]|
|» sch_name|string|true|none|sch名称|enum: [""]|
|» category|string|true|none|分类|enum: ["期中试卷"]|
|» vague_name|string|true|none|vague名称|none|
|» from|string|true|none|来源|none|
|» grade|string|true|none|年级|enum: ["七年级下"]|
|» score|number|true|none|分数|enum: [0.7237]|
|year|number|false|none|年份|enum: [2025]|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|小学|
|period|初中|
|source|sys|
|source|zyk|
|source|zx|
|source|upload|
|category|basket|
|score|3|
|score|0|
|score|10|
|score|4|
|score|15|
|score|12|
|score|14|
|elite|0|
|description|计算：|
|comment||
|id|3530358783|
|id|3528982527|
|id|3529637887|
|id|3529703423|
|name|实数的混合运算|
|chance|0|
|score|0|
|difficulty|3|
|year|2025|
|from_year|2024|
|region|重庆|
|province|重庆|
|city|全部地区|
|name|重庆|
|id|791434180|
|ques_score|0|
|sch_name||
|category|期中试卷|
|grade|七年级下|
|score|0.7237|
|year|2025|

<h2 id="tocS_user_exam">user_exam</h2>

<a id="schemauser_exam"></a>
<a id="schema_user_exam"></a>
<a id="tocSuser_exam"></a>
<a id="tocsuser_exam"></a>

```json
{
  "user_id": "string",
  "exam_id": "string",
  "paper_id": "string",
  "user_paper_id": "",
  "grade": "高二",
  "period": "高中",
  "subject": "生物",
  "papers": "string",
  "class_papers": "string",
  "questions": "string",
  "ctime": "string",
  "utime": "string",
  "table_id": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|exam_id|string|true|none|考试的ID|none|
|paper_id|string|true|none|试卷ID|none|
|user_paper_id|string|true|none|user_paper的ID|enum: [""]|
|grade|string|false|none|年级|enum: ["高二", "", "高三", "初二", "高一", "初一", "七年级", "初三", "六年级"]|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|subject|string|true|none|学科|enum: ["生物", "政治", "语文", "数学", "物理", "地理", "英语", "化学", "历史"]|
|papers|string|true|none|papers列表|none|
|class_papers|string|true|none|class_papers列表|none|
|questions|string|false|none|试题列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|table_id|string|false|none|table的ID|none|

#### 枚举值

|属性|值|
|---|---|
|user_paper_id||
|grade|高二|
|grade||
|grade|高三|
|grade|初二|
|grade|高一|
|grade|初一|
|grade|七年级|
|grade|初三|
|grade|六年级|
|period|高中|
|period|初中|
|period|小学|
|subject|生物|
|subject|政治|
|subject|语文|
|subject|数学|
|subject|物理|
|subject|地理|
|subject|英语|
|subject|化学|
|subject|历史|

<h2 id="tocS_user_exam_ctb">user_exam_ctb</h2>

<a id="schemauser_exam_ctb"></a>
<a id="schema_user_exam_ctb"></a>
<a id="tocSuser_exam_ctb"></a>
<a id="tocsuser_exam_ctb"></a>

```json
{
  "user_id": "string",
  "exam_id": "string",
  "paper_id": "string",
  "user_paper_id": "",
  "grade": "五年级",
  "period": "小学",
  "subject": "数学",
  "questions": "string",
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|exam_id|string|true|none|考试的ID|none|
|paper_id|string|true|none|试卷ID|none|
|user_paper_id|string|true|none|user_paper的ID|enum: [""]|
|grade|string|true|none|年级|enum: ["五年级", "高二", "七年级", "高一", "高三"]|
|period|string|true|none|学段|enum: ["小学", "高中", "初中"]|
|subject|string|true|none|学科|enum: ["数学", "政治", "化学", "生物", "语文", "物理"]|
|questions|string|true|none|试题列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|user_paper_id||
|grade|五年级|
|grade|高二|
|grade|七年级|
|grade|高一|
|grade|高三|
|period|小学|
|period|高中|
|period|初中|
|subject|数学|
|subject|政治|
|subject|化学|
|subject|生物|
|subject|语文|
|subject|物理|

<h2 id="tocS_user_exam_ctb_config">user_exam_ctb_config</h2>

<a id="schemauser_exam_ctb_config"></a>
<a id="schema_user_exam_ctb_config"></a>
<a id="tocSuser_exam_ctb_config"></a>
<a id="tocsuser_exam_ctb_config"></a>

```json
{
  "user_id": "string",
  "exam_id": "string",
  "exam_name": "string",
  "paper_id": "string",
  "student_config": {
    "origin_question": 0,
    "same_question": 0
  },
  "class_config": {
    "origin_question": 1,
    "same_question": 1
  },
  "questions": [
    {
      "id": "string"
    }
  ],
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|exam_id|string|true|none|考试的ID|none|
|exam_name|string|true|none|考试名称|none|
|paper_id|string|true|none|试卷ID|none|
|student_config|object|true|none|student_config|none|
|» origin_question|number|true|none|origin_question|enum: [0, 1]|
|» same_question|number|true|none|same_question|enum: [0, 1]|
|class_config|object|true|none|class_config|none|
|» origin_question|number|true|none|origin_question|enum: [1, 0]|
|» same_question|number|true|none|same_question|enum: [1, 0]|
|questions|[object]|false|none|试题列表|none|
|» id|string|true|none|id|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|origin_question|0|
|origin_question|1|
|same_question|0|
|same_question|1|
|origin_question|1|
|origin_question|0|
|same_question|1|
|same_question|0|

<h2 id="tocS_user_paper">user_paper</h2>

<a id="schemauser_paper"></a>
<a id="schema_user_paper"></a>
<a id="tocSuser_paper"></a>
<a id="tocsuser_paper"></a>

```json
{
  "period": "高中",
  "subject": "string",
  "grade": "string",
  "name": "string",
  "subtitle": "",
  "score": 0,
  "duration": 120,
  "paper_info": "",
  "cand_info": "",
  "attentions": "",
  "secret_tag": "绝密★启用前",
  "gutter": 0,
  "template": "exam",
  "partsList": "string",
  "volumes": [
    {
      "title": "卷I（选择题）",
      "note": "",
      "blocks": "string"
    }
  ],
  "type": "string",
  "score_info": "",
  "user_id": "string",
  "ctime": "string",
  "utime": "string",
  "valid": 1,
  "source_type": "upload",
  "source": "upload",
  "status": "done",
  "exam_status": "string",
  "from_year": 2024,
  "to_year": 2025,
  "source_id": "string",
  "category": 4,
  "source_url": "string",
  "error": "未获取到试题",
  "province": "新疆"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|subject|string|true|none|学科|none|
|grade|string|false|none|年级|none|
|name|string|true|none|名称|none|
|subtitle|string|false|none|subtitle|enum: ["", "1111", "高一地理试题卷"]|
|score|number|false|none|分数|none|
|duration|number|false|none|持续时间（分钟）|enum: [120, 0, "60", 90, 75, 40]|
|paper_info|string|false|none|paper_info|enum: [""]|
|cand_info|string|false|none|cand_info|enum: ["", " ", "历史", "学生："]|
|attentions|string|false|none|attentions|enum: ["", "<br/>"]|
|secret_tag|string|false|none|secret_tag|enum: ["绝密★启用前", ""]|
|gutter|number|false|none|gutter|enum: [0]|
|template|string|false|none|模板|enum: ["exam"]|
|partsList|string|false|none|partsList列表|none|
|volumes|[object]|false|none|volumes列表|none|
|» title|string|true|none|标题|enum: ["卷I（选择题）"]|
|» note|string|true|none|note|enum: [""]|
|» blocks|string|true|none|blocks列表|none|
|type|string|false|none|类型|none|
|score_info|string|false|none|score_info|enum: [""]|
|user_id|string|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|valid|number|true|none|是否有效|enum: [1, 0, 2]|
|source_type|string|true|none|source_type|enum: ["upload", "exam"]|
|source|string|false|none|来源|enum: ["upload", "exam"]|
|status|string|true|none|状态|enum: ["done", "edit", "error"]|
|exam_status|string|false|none|exam_status|none|
|from_year|number|false|none|from_year|enum: [2024, 2023]|
|to_year|number|false|none|to_year|enum: [2025, 2024]|
|source_id|string|false|none|source的ID|none|
|category|number|false|none|分类|enum: [4, 1, 3, 2]|
|source_url|string|false|none|来源URL|none|
|error|string|false|none|error|enum: ["未获取到试题", "服务错误::"]|
|province|string|false|none|省份|enum: ["新疆", "安徽", "河南"]|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小学|
|subtitle||
|subtitle|1111|
|subtitle|高一地理试题卷|
|duration|120|
|duration|0|
|duration|60|
|duration|90|
|duration|75|
|duration|40|
|paper_info||
|cand_info||
|cand_info| |
|cand_info|历史|
|cand_info|学生：|
|attentions||
|attentions|<br/>|
|secret_tag|绝密★启用前|
|secret_tag||
|gutter|0|
|template|exam|
|title|卷I（选择题）|
|note||
|score_info||
|valid|1|
|valid|0|
|valid|2|
|source_type|upload|
|source_type|exam|
|source|upload|
|source|exam|
|status|done|
|status|edit|
|status|error|
|from_year|2024|
|from_year|2023|
|to_year|2025|
|to_year|2024|
|category|4|
|category|1|
|category|3|
|category|2|
|error|未获取到试题|
|error|服务错误::|
|province|新疆|
|province|安徽|
|province|河南|

<h2 id="tocS_user_prep_basket">user_prep_basket</h2>

<a id="schemauser_prep_basket"></a>
<a id="schema_user_prep_basket"></a>
<a id="tocSuser_prep_basket"></a>
<a id="tocsuser_prep_basket"></a>

```json
{
  "period": "高中",
  "subject": "数学",
  "type": 2,
  "children": "string",
  "user_id": "string",
  "type_id": "string",
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学", "语文"]|
|type|string|true|none|类型|enum: [2]|
|children|string|true|none|子项|none|
|user_id|string|true|none|用户ID|none|
|type_id|string|true|none|type的ID|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|subject|数学|
|subject|语文|
|type|2|
|valid|1|

<h2 id="tocS_user_prep_catalog">user_prep_catalog</h2>

<a id="schemauser_prep_catalog"></a>
<a id="schema_user_prep_catalog"></a>
<a id="tocSuser_prep_catalog"></a>
<a id="tocsuser_prep_catalog"></a>

```json
{
  "user_id": "string",
  "period": "高中",
  "subject": "string",
  "category": 1,
  "children": [
    {
      "id": "string",
      "name": "我的备课",
      "children": [
        {
          "name": "string",
          "children": [
            {}
          ],
          "id": "string"
        }
      ]
    }
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学", "中职", "小语种"]|
|subject|string|true|none|学科|none|
|category|number|true|none|分类|enum: [1]|
|children|[object]|true|none|子项|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["我的备课"]|
|» children|[object]|false|none|子项|none|
|»» name|string|true|none|名称|none|
|»» children|[object]|true|none|子项|none|
|»»» name|string|true|none|名称|none|
|»»» children|[object]|true|none|子项|none|
|»»»» name|string|true|none|名称|enum: ["3.3 幂函数", "4.1 指数", "4.3 对数"]|
|»»»» id|string|true|none|id|none|
|»»»» children|[object]|false|none|子项|none|
|»»» id|string|true|none|id|none|
|»» id|string|true|none|id|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小学|
|period|中职|
|period|小语种|
|category|1|
|name|我的备课|
|name|3.3 幂函数|
|name|4.1 指数|
|name|4.3 对数|
|valid|1|

<h2 id="tocS_user_prep_resource">user_prep_resource</h2>

<a id="schemauser_prep_resource"></a>
<a id="schema_user_prep_resource"></a>
<a id="tocSuser_prep_resource"></a>
<a id="tocsuser_prep_resource"></a>

```json
{
  "user_id": "string",
  "catalog_id": "string",
  "from": 1,
  "from_id": 0,
  "period": "高中",
  "subject": "语文",
  "type_id": "string",
  "name": "集合的概念",
  "file": {
    "host": "string",
    "url": "string",
    "mime": "pptx",
    "size": 0,
    "images": "string"
  },
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "grade": "",
  "type": "",
  "children": [
    {
      "id": "string",
      "name": "【知识点】补集"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|catalog_id|string|true|none|catalog的ID|none|
|from|number|true|none|来源|enum: [1, 3]|
|from_id|number|true|none|from的ID|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|subject|string|true|none|学科|enum: ["语文", "数学", "化学", "地理", "英语", "政治", "生物", "物理"]|
|type_id|string|true|none|type的ID|none|
|name|string|true|none|名称|enum: ["集合的概念", "有理数"]|
|file|object|false|none|文件信息|none|
|» host|string|true|none|域名|none|
|» url|string|true|none|资源地址|none|
|» mime|string|true|none|mime|enum: ["pptx", "docx"]|
|» size|number|true|none|大小|none|
|» images|string|false|none|图片|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|grade|string|false|none|年级|enum: ["", "必修第一册", "高二"]|
|type|string|false|none|类型|enum: ["", "同步练习", "课件", "教案", "学案"]|
|children|[object]|false|none|子项|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["【知识点】补集"]|

#### 枚举值

|属性|值|
|---|---|
|from|1|
|from|3|
|period|高中|
|period|初中|
|period|小学|
|subject|语文|
|subject|数学|
|subject|化学|
|subject|地理|
|subject|英语|
|subject|政治|
|subject|生物|
|subject|物理|
|name|集合的概念|
|name|有理数|
|mime|pptx|
|mime|docx|
|valid|1|
|grade||
|grade|必修第一册|
|grade|高二|
|type||
|type|同步练习|
|type|课件|
|type|教案|
|type|学案|
|name|【知识点】补集|

<h2 id="tocS_user_property">user_property</h2>

<a id="schemauser_property"></a>
<a id="schema_user_property"></a>
<a id="tocSuser_property"></a>
<a id="tocsuser_property"></a>

```json
{
  "user_id": "string",
  "ctime": "string",
  "utime": "string",
  "period": "初中",
  "subject": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|true|none|学科|none|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|

<h2 id="tocS_user_question">user_question</h2>

<a id="schemauser_question"></a>
<a id="schema_user_question"></a>
<a id="tocSuser_question"></a>
<a id="tocsuser_question"></a>

```json
{
  "type": "string",
  "period": "高中",
  "subject": "string",
  "score": 10,
  "description": "",
  "comment": "",
  "source": "upload",
  "source_id": "upload",
  "blocks": {
    "stems": [
      {
        "stem": "string",
        "options": {
          "A": "[",
          "B": "[",
          "C": "[",
          "D": "["
        },
        "option": {
          "A": "string"
        }
      }
    ],
    "answers": "string",
    "explanations": [
      "string"
    ],
    "solutions": [
      "string"
    ],
    "types": [
      "string"
    ],
    "knowledges": [
      "string"
    ],
    "core_knowledges": "string"
  },
  "grade": "高一上",
  "user_id": "string",
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "year": 2025,
  "refer_exampapers": [
    "string"
  ],
  "difficulty": 3,
  "knowledges": [
    {
      "id": 0,
      "name": "分式化简求值",
      "source": "xkw"
    }
  ],
  "audio": {
    "name": "录音文件",
    "url": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|string|true|none|类型|none|
|period|string|false|none|学段|enum: ["高中", "小学", "初中"]|
|subject|string|false|none|学科|none|
|score|number|true|none|分数|enum: [10, 3, 8, 20, 15, 25, 30, 5, 4]|
|description|string|true|none|描述|enum: ["", "题干题干"]|
|comment|string|false|none|点评/注释|enum: [""]|
|source|string|true|none|来源|enum: ["upload", "xkw"]|
|source_id|string|true|none|source的ID|enum: ["upload"]|
|blocks|object|true|none|blocks列表|none|
|» stems|[object]|true|none|stems列表|none|
|»» stem|string|true|none|stem|none|
|»» options|object|false|none|options|none|
|»»» A|string|true|none|A|enum: ["${4}$", "${13}$"]|
|»»» B|string|true|none|B|enum: ["${3}$", "${26}$"]|
|»»» C|string|true|none|C|enum: ["${2}$", "${52}$"]|
|»»» D|string|true|none|D|enum: ["${1}$", "${156}$"]|
|»» option|object|false|none|option|none|
|»»» A|string|true|none|A|none|
|» answers|string|true|none|answers列表|none|
|» explanations|[string]|true|none|explanations列表|none|
|» solutions|[string]|true|none|solutions列表|none|
|» types|[string]|true|none|types列表|none|
|» knowledges|[string]|true|none|知识点|none|
|» core_knowledges|string|true|none|core_knowledges列表|none|
|grade|string|false|none|年级|enum: ["高一上", "高二下", "六年级下", "高一下", "七年级下", "高二上"]|
|user_id|string|true|none|用户ID|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|year|number|false|none|年份|enum: [2025]|
|refer_exampapers|[string]|true|none|关联考试列表|none|
|difficulty|number|true|none|难度|enum: [3, 2, 4, 1, 5]|
|knowledges|[object]|false|none|知识点|none|
|» id|number|true|none|id|none|
|» name|string|true|none|名称|enum: ["分式化简求值", "辅助角公式", "函数新定义"]|
|» source|string|true|none|来源|enum: ["xkw"]|
|audio|object|false|none|audio|none|
|» name|string|true|none|名称|enum: ["录音文件"]|
|» url|string|true|none|资源地址|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|小学|
|period|初中|
|score|10|
|score|3|
|score|8|
|score|20|
|score|15|
|score|25|
|score|30|
|score|5|
|score|4|
|description||
|description|题干题干|
|comment||
|source|upload|
|source|xkw|
|source_id|upload|
|A|${4}$|
|A|${13}$|
|B|${3}$|
|B|${26}$|
|C|${2}$|
|C|${52}$|
|D|${1}$|
|D|${156}$|
|grade|高一上|
|grade|高二下|
|grade|六年级下|
|grade|高一下|
|grade|七年级下|
|grade|高二上|
|valid|1|
|year|2025|
|difficulty|3|
|difficulty|2|
|difficulty|4|
|difficulty|1|
|difficulty|5|
|name|分式化简求值|
|name|辅助角公式|
|name|函数新定义|
|source|xkw|
|name|录音文件|

<h2 id="tocS_user_tw_specification">user_tw_specification</h2>

<a id="schemauser_tw_specification"></a>
<a id="schema_user_tw_specification"></a>
<a id="tocSuser_tw_specification"></a>
<a id="tocsuser_tw_specification"></a>

```json
{
  "user_id": "string",
  "table_id": "string",
  "ctime": "string",
  "period": "高中",
  "subject": "数学",
  "type": "考后巩固",
  "grade": "高三"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|table_id|string|true|none|table的ID|none|
|ctime|string|true|none|创建时间|none|
|period|string|true|none|学段|enum: ["高中", "初中"]|
|subject|string|true|none|学科|enum: ["数学", "物理", "政治", "地理", "化学", "生物", "语文", "历史"]|
|type|string|true|none|类型|enum: ["考后巩固"]|
|grade|string|false|none|年级|enum: ["高三", "初二", "高二", "高一", "", "初一"]|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|subject|数学|
|subject|物理|
|subject|政治|
|subject|地理|
|subject|化学|
|subject|生物|
|subject|语文|
|subject|历史|
|type|考后巩固|
|grade|高三|
|grade|初二|
|grade|高二|
|grade|高一|
|grade||
|grade|初一|

<h2 id="tocS_user_xkw_relationship">user_xkw_relationship</h2>

<a id="schemauser_xkw_relationship"></a>
<a id="schema_user_xkw_relationship"></a>
<a id="tocSuser_xkw_relationship"></a>
<a id="tocsuser_xkw_relationship"></a>

```json
{
  "user_id": "string",
  "phone": "",
  "open_id": "",
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|phone|string|true|none|phone|enum: ["", "未知"]|
|open_id|string|true|none|open的ID|enum: [""]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|phone||
|phone|未知|
|open_id||

<h2 id="tocS_user_xyz_prep_basket">user_xyz_prep_basket</h2>

<a id="schemauser_xyz_prep_basket"></a>
<a id="schema_user_xyz_prep_basket"></a>
<a id="tocSuser_xyz_prep_basket"></a>
<a id="tocsuser_xyz_prep_basket"></a>

```json
{
  "period": "高中",
  "subject": "俄语",
  "type_id": "string",
  "children": "string",
  "user_id": "string",
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "小语种"]|
|subject|string|true|none|学科|enum: ["俄语", "数学", "日语"]|
|type_id|string|true|none|type的ID|none|
|children|string|true|none|子项|none|
|user_id|string|true|none|用户ID|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|小语种|
|subject|俄语|
|subject|数学|
|subject|日语|
|valid|1|

<h2 id="tocS_user_xyz_prep_catalog">user_xyz_prep_catalog</h2>

<a id="schemauser_xyz_prep_catalog"></a>
<a id="schema_user_xyz_prep_catalog"></a>
<a id="tocSuser_xyz_prep_catalog"></a>
<a id="tocsuser_xyz_prep_catalog"></a>

```json
{
  "user_id": "string",
  "period": "高中",
  "subject": "string",
  "category": 1,
  "children": [
    {
      "id": "string",
      "name": "我的备课"
    }
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小语种", "小学"]|
|subject|string|true|none|学科|none|
|category|number|true|none|分类|enum: [1]|
|children|[object]|true|none|子项|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["我的备课"]|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小语种|
|period|小学|
|category|1|
|name|我的备课|
|valid|1|

<h2 id="tocS_user_xyz_prep_resource">user_xyz_prep_resource</h2>

<a id="schemauser_xyz_prep_resource"></a>
<a id="schema_user_xyz_prep_resource"></a>
<a id="tocSuser_xyz_prep_resource"></a>
<a id="tocsuser_xyz_prep_resource"></a>

```json
{
  "user_id": "string",
  "catalog_id": "string",
  "from": 1,
  "from_id": "",
  "period": "高中",
  "grade": "必修第一册",
  "subject": "俄语",
  "type": "学案",
  "type_id": "image",
  "name": "同步分层作业",
  "children": [
    {
      "id": "string",
      "name": "同步分层作业"
    }
  ],
  "file": {
    "mime": "docx",
    "size": 0,
    "host": "string",
    "url": "string",
    "images": "string"
  },
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|string|true|none|用户ID|none|
|catalog_id|string|true|none|catalog的ID|none|
|from|number|true|none|来源|enum: [1, 2]|
|from_id|string|true|none|from的ID|enum: [""]|
|period|string|true|none|学段|enum: ["高中", "小语种"]|
|grade|string|false|none|年级|enum: ["必修第一册", "第一册"]|
|subject|string|true|none|学科|enum: ["俄语", "日语"]|
|type|string|false|none|类型|enum: ["学案", "教案", "素材", "图片", "课件", "视频"]|
|type_id|string|true|none|type的ID|enum: ["image", "video"]|
|name|string|true|none|名称|enum: ["同步分层作业", "000007", "教学视频课"]|
|children|[object]|true|none|子项|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["同步分层作业"]|
|file|object|false|none|文件信息|none|
|» mime|string|true|none|mime|enum: ["docx", "pdf", "png", "pptx", "jpeg", "mp4"]|
|» size|number|true|none|大小|none|
|» host|string|true|none|域名|none|
|» url|string|true|none|资源地址|none|
|» images|string|false|none|图片|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|from|1|
|from|2|
|from_id||
|period|高中|
|period|小语种|
|grade|必修第一册|
|grade|第一册|
|subject|俄语|
|subject|日语|
|type|学案|
|type|教案|
|type|素材|
|type|图片|
|type|课件|
|type|视频|
|type_id|image|
|type_id|video|
|name|同步分层作业|
|name|000007|
|name|教学视频课|
|name|同步分层作业|
|mime|docx|
|mime|pdf|
|mime|png|
|mime|pptx|
|mime|jpeg|
|mime|mp4|
|valid|1|

<h2 id="tocS_access-key">access-key</h2>

<a id="schemaaccess-key"></a>
<a id="schema_access-key"></a>
<a id="tocSaccess-key"></a>
<a id="tocsaccess-key"></a>

```json
{
  "status": true,
  "name": "kb",
  "key": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|status|boolean|true|none|状态|enum: [true]|
|name|string|true|none|名称|enum: ["kb"]|
|key|string|true|none|键/标识符|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|status|true|
|name|kb|

<h2 id="tocS_apps">apps</h2>

<a id="schemaapps"></a>
<a id="schema_apps"></a>
<a id="tocSapps"></a>
<a id="tocsapps"></a>

```json
{
  "isPreset": true,
  "type": "local",
  "modules": [
    "string"
  ],
  "name": "管理后台",
  "tokenSecret": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|isPreset|boolean|true|none|isPreset|enum: [true]|
|type|string|true|none|类型|enum: ["local"]|
|modules|[string]|true|none|modules列表|none|
|name|string|true|none|名称|enum: ["管理后台"]|
|tokenSecret|string|true|none|tokenSecret|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|isPreset|true|
|type|local|
|name|管理后台|

<h2 id="tocS_book-chapter">book-chapter</h2>

<a id="schemabook-chapter"></a>
<a id="schema_book-chapter"></a>
<a id="tocSbook-chapter"></a>
<a id="tocsbook-chapter"></a>

```json
{
  "source": "sync",
  "knowledges": "string",
  "period": "string",
  "subject": "string",
  "press_version": "string",
  "book": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "name": "string",
  "source_id": 0,
  "path": "string",
  "key": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync", "manua"]|
|knowledges|string|true|none|知识点|none|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|
|press_version|string|true|none|教材版本|none|
|book|string|false|none|对象ID|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|name|string|true|none|名称|none|
|source_id|number|false|none|source的ID|none|
|path|string|true|none|路径|none|
|key|string|false|none|键/标识符|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|
|source|manua|

<h2 id="tocS_book">book</h2>

<a id="schemabook"></a>
<a id="schema_book"></a>
<a id="tocSbook"></a>
<a id="tocsbook"></a>

```json
{
  "source": "sync",
  "name": "string",
  "source_id": 0,
  "press_version": "string",
  "period": "string",
  "subject": "string",
  "key": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "book_chapter": "string",
  "children": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync", "manua"]|
|name|string|true|none|名称|none|
|source_id|number|false|none|source的ID|none|
|press_version|string|true|none|教材版本|none|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|
|key|string|true|none|键/标识符|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|book_chapter|string|true|none|对象ID|none|
|children|string|true|none|子项|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|
|source|manua|

<h2 id="tocS_core_store">core_store</h2>

<a id="schemacore_store"></a>
<a id="schema_core_store"></a>
<a id="tocScore_store"></a>
<a id="tocscore_store"></a>

```json
{
  "key": "string",
  "environment": "",
  "tag": "",
  "type": "object",
  "value": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|true|none|键/标识符|none|
|environment|string|true|none|environment|enum: [""]|
|tag|string|true|none|tag|enum: [""]|
|type|string|true|none|类型|enum: ["object"]|
|value|string|true|none|值|none|

#### 枚举值

|属性|值|
|---|---|
|environment||
|tag||
|type|object|

<h2 id="tocS_disk-file-log">disk-file-log</h2>

<a id="schemadisk-file-log"></a>
<a id="schema_disk-file-log"></a>
<a id="tocSdisk-file-log"></a>
<a id="tocsdisk-file-log"></a>

```json
{
  "deleted": 0,
  "source": "user",
  "name": "发:票.pdf",
  "period": "高中",
  "subject": "数学",
  "operation_type": "upload",
  "createdAt": "string",
  "updatedAt": "string",
  "creator": "string",
  "file": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|deleted|number|true|none|删除状态|enum: [0]|
|source|string|true|none|来源|enum: ["user"]|
|name|string|true|none|名称|enum: ["发:票.pdf", "美食.jpg"]|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|operation_type|string|true|none|operation_type|enum: ["upload"]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|creator|string|true|none|对象ID|none|
|file|string|true|none|文件信息|none|

#### 枚举值

|属性|值|
|---|---|
|deleted|0|
|source|user|
|name|发:票.pdf|
|name|美食.jpg|
|period|高中|
|subject|数学|
|operation_type|upload|

<h2 id="tocS_disk-file">disk-file</h2>

<a id="schemadisk-file"></a>
<a id="schema_disk-file"></a>
<a id="tocSdisk-file"></a>
<a id="tocsdisk-file"></a>

```json
{
  "size": 0,
  "type": "",
  "suffix": "",
  "category": "",
  "hash": "",
  "url": "",
  "is_folder": 1,
  "parent_id": "0",
  "source": "upload",
  "source_id": "upload",
  "download_times": 0,
  "view_times": 0,
  "shared": 0,
  "is_top": 0,
  "deleted": 0,
  "period": "高中",
  "subject": "数学",
  "name": "新建文件夹",
  "school_id": 55553,
  "createdAt": "string",
  "updatedAt": "string",
  "creator": "string",
  "operator": "string",
  "pBranch": "string",
  "parse_task": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|size|number|true|none|大小|none|
|type|string|true|none|类型|enum: [""]|
|suffix|string|true|none|suffix|enum: ["", ".pdf", ".jpg", ".docx", ".pptx", ".svg"]|
|category|string|true|none|分类|enum: ["", "image"]|
|hash|string|true|none|hash|enum: [""]|
|url|string|true|none|资源地址|enum: [""]|
|is_folder|number|true|none|is_folder|enum: [1, 0]|
|parent_id|string|true|none|父级ID|enum: ["0"]|
|source|string|true|none|来源|enum: ["upload"]|
|source_id|string|true|none|source的ID|enum: ["upload"]|
|download_times|number|true|none|下载次数|enum: [0]|
|view_times|number|true|none|view_times|enum: [0]|
|shared|number|true|none|shared|enum: [0, 1]|
|is_top|number|true|none|is_top|enum: [0]|
|deleted|number|true|none|删除状态|enum: [0]|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|name|string|true|none|名称|enum: ["新建文件夹", "发:票.pdf", "美食.jpg", "未命名:文件夹", "作业分层流程切"]|
|school_id|number|true|none|学校的ID|enum: [55553]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|pBranch|string|true|none|对象ID|none|
|parse_task|string|false|none|对象ID|none|

#### 枚举值

|属性|值|
|---|---|
|type||
|suffix||
|suffix|.pdf|
|suffix|.jpg|
|suffix|.docx|
|suffix|.pptx|
|suffix|.svg|
|category||
|category|image|
|hash||
|url||
|is_folder|1|
|is_folder|0|
|parent_id|0|
|source|upload|
|source_id|upload|
|download_times|0|
|view_times|0|
|shared|0|
|shared|1|
|is_top|0|
|deleted|0|
|period|高中|
|subject|数学|
|name|新建文件夹|
|name|发:票.pdf|
|name|美食.jpg|
|name|未命名:文件夹|
|name|作业分层流程切|
|school_id|55553|

<h2 id="tocS_download-record">download-record</h2>

<a id="schemadownload-record"></a>
<a id="schema_download-record"></a>
<a id="tocSdownload-record"></a>
<a id="tocsdownload-record"></a>

```json
{
  "deleted": 0,
  "name": "工作簿1",
  "type": "other",
  "source": "string",
  "source_id": "string",
  "url": "",
  "suffix": "xlsx",
  "createdAt": "string",
  "updatedAt": "string",
  "pBranch": "string",
  "period": "string",
  "subject": "string",
  "user": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|deleted|number|true|none|删除状态|enum: [0, 1]|
|name|string|true|none|名称|enum: ["工作簿1", "演示视频", "答题卡图片", "截图", "项目总结2", "试试-有批注"]|
|type|string|true|none|类型|enum: ["other", "同步练习", "video", "中考模拟", "paper"]|
|source|string|true|none|来源|none|
|source_id|string|true|none|source的ID|none|
|url|string|false|none|资源地址|enum: [""]|
|suffix|string|false|none|suffix|enum: ["xlsx", "", "docx", "mp4", "jpg"]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|pBranch|string|true|none|对象ID|none|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|
|user|string|true|none|对象ID|none|

#### 枚举值

|属性|值|
|---|---|
|deleted|0|
|deleted|1|
|name|工作簿1|
|name|演示视频|
|name|答题卡图片|
|name|截图|
|name|项目总结2|
|name|试试-有批注|
|type|other|
|type|同步练习|
|type|video|
|type|中考模拟|
|type|paper|
|url||
|suffix|xlsx|
|suffix||
|suffix|docx|
|suffix|mp4|
|suffix|jpg|

<h2 id="tocS_edu-plan-chapter">edu-plan-chapter</h2>

<a id="schemaedu-plan-chapter"></a>
<a id="schema_edu-plan-chapter"></a>
<a id="tocSedu-plan-chapter"></a>
<a id="tocsedu-plan-chapter"></a>

```json
{
  "name": "月考",
  "key": "lesson",
  "source_id": "",
  "index": 1,
  "pBranch": "string",
  "creator": "string",
  "operator": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "deleted": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["月考", "121212", "周考", "补集", "描述法"]|
|key|string|true|none|键/标识符|enum: ["lesson", "chapter", "exam"]|
|source_id|string|true|none|source的ID|enum: [""]|
|index|number|true|none|索引|enum: [1, 0, 3, 2]|
|pBranch|string|true|none|对象ID|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|deleted|number|true|none|删除状态|enum: [0, 1]|

#### 枚举值

|属性|值|
|---|---|
|name|月考|
|name|121212|
|name|周考|
|name|补集|
|name|描述法|
|key|lesson|
|key|chapter|
|key|exam|
|source_id||
|index|1|
|index|0|
|index|3|
|index|2|
|deleted|0|
|deleted|1|

<h2 id="tocS_edu-plan">edu-plan</h2>

<a id="schemaedu-plan"></a>
<a id="schema_edu-plan"></a>
<a id="tocSedu-plan"></a>
<a id="tocsedu-plan"></a>

```json
{
  "period": "高中",
  "subject": "数学",
  "grade": "高一",
  "to_year": 2026,
  "semester": "上学期",
  "children": [
    {
      "key": "exam",
      "name": "周考",
      "children": "string",
      "source_id": "",
      "id": "string"
    }
  ],
  "from_year": 2025,
  "school_id": 55553,
  "pBranch": "string",
  "creator": "string",
  "operator": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "deleted": 0,
  "id": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|grade|string|true|none|年级|enum: ["高一", "高一e"]|
|to_year|number|true|none|to_year|enum: [2026, 2025]|
|semester|string|true|none|semester|enum: ["上学期", "下学期"]|
|children|[object]|true|none|子项|none|
|» key|string|true|none|键/标识符|enum: ["exam", "chapter"]|
|» name|string|true|none|名称|enum: ["周考", "月考"]|
|» children|string|true|none|子项|none|
|» source_id|string|true|none|source的ID|enum: [""]|
|» id|string|true|none|id|none|
|from_year|number|true|none|from_year|enum: [2025, 2024]|
|school_id|number|true|none|学校的ID|enum: [55553]|
|pBranch|string|true|none|对象ID|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|deleted|number|true|none|删除状态|enum: [0]|
|id|string|false|none|id|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|subject|数学|
|grade|高一|
|grade|高一e|
|to_year|2026|
|to_year|2025|
|semester|上学期|
|semester|下学期|
|key|exam|
|key|chapter|
|name|周考|
|name|月考|
|source_id||
|from_year|2025|
|from_year|2024|
|school_id|55553|
|deleted|0|

<h2 id="tocS_knowledge-tree-chapter">knowledge-tree-chapter</h2>

<a id="schemaknowledge-tree-chapter"></a>
<a id="schema_knowledge-tree-chapter"></a>
<a id="tocSknowledge-tree-chapter"></a>
<a id="tocsknowledge-tree-chapter"></a>

```json
{
  "source": "sync",
  "knowledges": "string",
  "period": "string",
  "subject": "string",
  "knowledge_tree": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "name": "string",
  "source_id": 0,
  "path": "string",
  "key": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync", "manua"]|
|knowledges|string|true|none|知识点|none|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|
|knowledge_tree|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|name|string|true|none|名称|none|
|source_id|number|false|none|source的ID|none|
|path|string|true|none|路径|none|
|key|string|true|none|键/标识符|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|
|source|manua|

<h2 id="tocS_knowledge-tree">knowledge-tree</h2>

<a id="schemaknowledge-tree"></a>
<a id="schema_knowledge-tree"></a>
<a id="tocSknowledge-tree"></a>
<a id="tocsknowledge-tree"></a>

```json
{
  "source": "manual",
  "name": "默认知识树",
  "key": "string",
  "children": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "creator": "string",
  "operator": "string",
  "pBranch": "string",
  "period": "string",
  "subject": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["manual"]|
|name|string|true|none|名称|enum: ["默认知识树"]|
|key|string|true|none|键/标识符|none|
|children|string|true|none|子项|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|pBranch|string|true|none|对象ID|none|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|

#### 枚举值

|属性|值|
|---|---|
|source|manual|
|name|默认知识树|

<h2 id="tocS_knowledge">knowledge</h2>

<a id="schemaknowledge"></a>
<a id="schema_knowledge"></a>
<a id="tocSknowledge"></a>
<a id="tocsknowledge"></a>

```json
{
  "source": "sync",
  "period": "string",
  "subject": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "name": "string",
  "source_id": 0,
  "key": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync", "manual"]|
|period|string|true|none|学段|none|
|subject|string|true|none|学科|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|name|string|true|none|名称|none|
|source_id|number|false|none|source的ID|none|
|key|string|true|none|键/标识符|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|
|source|manual|

<h2 id="tocS_parse-task">parse-task</h2>

<a id="schemaparse-task"></a>
<a id="schema_parse-task"></a>
<a id="tocSparse-task"></a>
<a id="tocsparse-task"></a>

```json
{
  "status": "edit",
  "task_type": "word",
  "deleted": 0,
  "disk_files": [
    "string"
  ],
  "name": "string",
  "period": "高中",
  "subject": "数学",
  "grade": "高一上",
  "type": "月考试卷",
  "from_year": 2024,
  "to_year": 2025,
  "school_id": 55553,
  "user_id": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "creator": "string",
  "pBranch": "string",
  "error": "",
  "tiku_paper_id": "string",
  "disk_file": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|status|string|true|none|状态|enum: ["edit", "done"]|
|task_type|string|false|none|task_type|enum: ["word"]|
|deleted|number|true|none|删除状态|enum: [0]|
|disk_files|[string]|false|none|disk_files列表|none|
|name|string|true|none|名称|none|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|grade|string|true|none|年级|enum: ["高一上", "高二下"]|
|type|string|true|none|类型|enum: ["月考试卷", "同步练习"]|
|from_year|number|true|none|from_year|enum: [2024, 2023]|
|to_year|number|true|none|to_year|enum: [2025, 2024]|
|school_id|number|true|none|学校的ID|enum: [55553]|
|user_id|string|true|none|用户ID|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|error|string|true|none|error|enum: [""]|
|tiku_paper_id|string|true|none|tiku_paper的ID|none|
|disk_file|string|false|none|对象ID|none|

#### 枚举值

|属性|值|
|---|---|
|status|edit|
|status|done|
|task_type|word|
|deleted|0|
|period|高中|
|subject|数学|
|grade|高一上|
|grade|高二下|
|type|月考试卷|
|type|同步练习|
|from_year|2024|
|from_year|2023|
|to_year|2025|
|to_year|2024|
|school_id|55553|
|error||

<h2 id="tocS_period">period</h2>

<a id="schemaperiod"></a>
<a id="schema_period"></a>
<a id="tocSperiod"></a>
<a id="tocsperiod"></a>

```json
{
  "source": "sync",
  "subjects": [
    "string"
  ],
  "name": "初中",
  "key": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync"]|
|subjects|[string]|true|none|subjects列表|none|
|name|string|true|none|名称|enum: ["初中", "小学", "高中"]|
|key|string|true|none|键/标识符|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|
|name|初中|
|name|小学|
|name|高中|

<h2 id="tocS_press-version">press-version</h2>

<a id="schemapress-version"></a>
<a id="schema_press-version"></a>
<a id="tocSpress-version"></a>
<a id="tocspress-version"></a>

```json
{
  "source": "sync",
  "books": "string",
  "name": "string",
  "subject": "string",
  "period": "string",
  "key": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync"]|
|books|string|true|none|books列表|none|
|name|string|true|none|名称|none|
|subject|string|true|none|学科|none|
|period|string|true|none|学段|none|
|key|string|true|none|键/标识符|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|

<h2 id="tocS_resource">resource</h2>

<a id="schemaresource"></a>
<a id="schema_resource"></a>
<a id="tocSresource"></a>
<a id="tocsresource"></a>

```json
{
  "source": "upload",
  "source_id": "upload",
  "download_times": 0,
  "view_times": 0,
  "shared": 1,
  "deleted": 0,
  "name": "string",
  "type": "other",
  "url": "string",
  "size": 0,
  "suffix": "pptx",
  "createdAt": "string",
  "updatedAt": "string",
  "book": "string",
  "book_chapter": "string",
  "pBranch": "string",
  "period": "string",
  "press_version": "string",
  "subject": "string",
  "user": "string",
  "last_view_time": "string",
  "last_download_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["upload"]|
|source_id|string|true|none|source的ID|enum: ["upload"]|
|download_times|number|true|none|下载次数|enum: [0, 1, 2, 3]|
|view_times|number|true|none|view_times|enum: [0, 1, 3, 2, 6, 4, 5, 18]|
|shared|number|true|none|shared|enum: [1, 0]|
|deleted|number|true|none|删除状态|enum: [0, 1]|
|name|string|true|none|名称|none|
|type|string|true|none|类型|enum: ["other", "paper", "video"]|
|url|string|true|none|资源地址|none|
|size|number|true|none|大小|none|
|suffix|string|true|none|suffix|enum: ["pptx", "xlsx", "pdf", "docx", "png", "jpg", "mp4"]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|book|string|false|none|对象ID|none|
|book_chapter|string|false|none|对象ID|none|
|pBranch|string|false|none|对象ID|none|
|period|string|false|none|学段|none|
|press_version|string|false|none|教材版本|none|
|subject|string|false|none|学科|none|
|user|string|false|none|对象ID|none|
|last_view_time|string|false|none|last_view时间|none|
|last_download_time|string|false|none|last_download时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|upload|
|source_id|upload|
|download_times|0|
|download_times|1|
|download_times|2|
|download_times|3|
|view_times|0|
|view_times|1|
|view_times|3|
|view_times|2|
|view_times|6|
|view_times|4|
|view_times|5|
|view_times|18|
|shared|1|
|shared|0|
|deleted|0|
|deleted|1|
|type|other|
|type|paper|
|type|video|
|suffix|pptx|
|suffix|xlsx|
|suffix|pdf|
|suffix|docx|
|suffix|png|
|suffix|jpg|
|suffix|mp4|

<h2 id="tocS_subject">subject</h2>

<a id="schemasubject"></a>
<a id="schema_subject"></a>
<a id="tocSsubject"></a>
<a id="tocssubject"></a>

```json
{
  "source": "sync",
  "press_versions": [
    "string"
  ],
  "name": "string",
  "period": "string",
  "key": "string",
  "operator": "string",
  "creator": "string",
  "pBranch": "string",
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|source|string|true|none|来源|enum: ["sync"]|
|press_versions|[string]|true|none|press_versions列表|none|
|name|string|true|none|名称|none|
|period|string|true|none|学段|none|
|key|string|true|none|键/标识符|none|
|operator|string|true|none|操作人|none|
|creator|string|true|none|对象ID|none|
|pBranch|string|true|none|对象ID|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|source|sync|

<h2 id="tocS_SystemErrorLog">SystemErrorLog</h2>

<a id="schemasystemerrorlog"></a>
<a id="schema_SystemErrorLog"></a>
<a id="tocSsystemerrorlog"></a>
<a id="tocssystemerrorlog"></a>

```json
{
  "name": "Error",
  "message": "string",
  "stack": "string",
  "ip": "string",
  "userAgent": "string",
  "uaInfo": {
    "ua": "string",
    "browser": {
      "name": "Chrome",
      "version": "17.6",
      "major": "136"
    },
    "engine": {
      "name": "Blink",
      "version": "string"
    },
    "os": {
      "name": "Windows",
      "version": "10"
    },
    "device": {
      "vendor": "Apple",
      "model": "string"
    },
    "cpu": {
      "architecture": "amd64"
    }
  },
  "originalUrl": "string",
  "method": "GET",
  "requestBody": "{}",
  "requestAt": "string",
  "host": "string",
  "referer": "string",
  "responseAt": "string",
  "responseTime": 0,
  "responseBody": {
    "statusCode": 500,
    "error": "string",
    "message": "string"
  },
  "userId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["Error"]|
|message|string|true|none|message|none|
|stack|string|true|none|stack|none|
|ip|string|true|none|ip|none|
|userAgent|string|true|none|userAgent|none|
|uaInfo|object|true|none|uaInfo|none|
|» ua|string|true|none|ua|none|
|» browser|object|true|none|browser|none|
|»» name|string|false|none|名称|enum: ["Chrome", "Safari", "Edge"]|
|»» version|string|false|none|版本|enum: ["17.6", "13.1.3", "18.3.1"]|
|»» major|string|false|none|major|enum: ["136", "17", "13", "48", "134", "18", "92", "128", "133"]|
|» engine|object|true|none|engine|none|
|»» name|string|false|none|名称|enum: ["Blink", "WebKit"]|
|»» version|string|false|none|版本|none|
|» os|object|true|none|os|none|
|»» name|string|false|none|名称|enum: ["Windows", "Mac OS"]|
|»» version|string|false|none|版本|enum: ["10", "10.15.7"]|
|» device|object|true|none|device|none|
|»» vendor|string|false|none|vendor|enum: ["Apple"]|
|»» model|string|false|none|model|none|
|» cpu|object|true|none|cpu|none|
|»» architecture|string|false|none|architecture|enum: ["amd64"]|
|originalUrl|string|true|none|original的URL|none|
|method|string|true|none|method|enum: ["GET", "POST", "PUT", "DELETE"]|
|requestBody|string|true|none|requestBody|enum: ["{}"]|
|requestAt|string|true|none|日期时间|none|
|host|string|true|none|域名|none|
|referer|string|false|none|referer|none|
|responseAt|string|true|none|日期时间|none|
|responseTime|number|true|none|response时间|none|
|responseBody|object|true|none|responseBody|none|
|» statusCode|number|true|none|statusCode|enum: [500, 401, 403, 404]|
|» error|string|true|none|error|none|
|» message|string|true|none|message|none|
|userId|string|false|none|user的ID|none|

#### 枚举值

|属性|值|
|---|---|
|name|Error|
|name|Chrome|
|name|Safari|
|name|Edge|
|version|17.6|
|version|13.1.3|
|version|18.3.1|
|major|136|
|major|17|
|major|13|
|major|48|
|major|134|
|major|18|
|major|92|
|major|128|
|major|133|
|name|Blink|
|name|WebKit|
|name|Windows|
|name|Mac OS|
|version|10|
|version|10.15.7|
|vendor|Apple|
|architecture|amd64|
|method|GET|
|method|POST|
|method|PUT|
|method|DELETE|
|requestBody|{}|
|statusCode|500|
|statusCode|401|
|statusCode|403|
|statusCode|404|

<h2 id="tocS_SystemRequestLog">SystemRequestLog</h2>

<a id="schemasystemrequestlog"></a>
<a id="schema_SystemRequestLog"></a>
<a id="tocSsystemrequestlog"></a>
<a id="tocssystemrequestlog"></a>

```json
{
  "ip": "string",
  "userAgent": "string",
  "uaInfo": {
    "ua": "string",
    "browser": {
      "name": "Chrome",
      "version": "6.2",
      "major": "string"
    },
    "engine": {
      "name": "Blink",
      "version": "43.0"
    },
    "os": {
      "name": "Windows",
      "version": "10"
    },
    "device": {
      "vendor": "Apple",
      "model": "string"
    },
    "cpu": {
      "architecture": "amd64"
    }
  },
  "originalUrl": "/",
  "method": "GET",
  "requestBody": "{}",
  "requestAt": "string",
  "host": "string",
  "referer": "string",
  "responseAt": "string",
  "responseTime": 0,
  "userId": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ip|string|true|none|ip|none|
|userAgent|string|true|none|userAgent|none|
|uaInfo|object|true|none|uaInfo|none|
|» ua|string|true|none|ua|none|
|» browser|object|true|none|browser|none|
|»» name|string|false|none|名称|enum: ["Chrome", "Edge", "WeChat", "Firefox", "Safari", "WebKit"]|
|»» version|string|false|none|版本|enum: ["6.2", "43.0", "18.5", "17.6", "13.1.3", "18.3.1"]|
|»» major|string|false|none|major|none|
|» engine|object|true|none|engine|none|
|»» name|string|false|none|名称|enum: ["Blink", "Gecko", "WebKit"]|
|»» version|string|false|none|版本|enum: ["43.0"]|
|» os|object|true|none|os|none|
|»» name|string|false|none|名称|enum: ["Windows", "Mac OS", "iOS"]|
|»» version|string|false|none|版本|enum: ["10", "10.15.7", " W", "7", "10.14.6"]|
|» device|object|true|none|device|none|
|»» vendor|string|false|none|vendor|enum: ["Apple"]|
|»» model|string|false|none|model|none|
|» cpu|object|true|none|cpu|none|
|»» architecture|string|false|none|architecture|enum: ["amd64"]|
|originalUrl|string|true|none|original的URL|enum: ["/", "/sse"]|
|method|string|true|none|method|enum: ["GET", "POST", "PUT", "DELETE", "HEAD"]|
|requestBody|string|true|none|requestBody|enum: ["{}"]|
|requestAt|string|true|none|日期时间|none|
|host|string|true|none|域名|none|
|referer|string|false|none|referer|none|
|responseAt|string|true|none|日期时间|none|
|responseTime|number|true|none|response时间|none|
|userId|string|false|none|user的ID|none|

#### 枚举值

|属性|值|
|---|---|
|name|Chrome|
|name|Edge|
|name|WeChat|
|name|Firefox|
|name|Safari|
|name|WebKit|
|version|6.2|
|version|43.0|
|version|18.5|
|version|17.6|
|version|13.1.3|
|version|18.3.1|
|name|Blink|
|name|Gecko|
|name|WebKit|
|version|43.0|
|name|Windows|
|name|Mac OS|
|name|iOS|
|version|10|
|version|10.15.7|
|version| W|
|version|7|
|version|10.14.6|
|vendor|Apple|
|architecture|amd64|
|originalUrl|/|
|originalUrl|/sse|
|method|GET|
|method|POST|
|method|PUT|
|method|DELETE|
|method|HEAD|
|requestBody|{}|

<h2 id="tocS_team-prep-data">team-prep-data</h2>

<a id="schemateam-prep-data"></a>
<a id="schema_team-prep-data"></a>
<a id="tocSteam-prep-data"></a>
<a id="tocsteam-prep-data"></a>

```json
{
  "team_prep_id": "string",
  "edu_plan_chapter_id": "string",
  "key": "string",
  "children": [
    {
      "id": 0,
      "source": "sys",
      "source_id": 0
    }
  ],
  "pBranch": "string",
  "creator": "string",
  "operator": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "deleted": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|team_prep_id|string|true|none|team_prep的ID|none|
|edu_plan_chapter_id|string|true|none|edu_plan_chapter的ID|none|
|key|string|true|none|键/标识符|none|
|children|[object]|true|none|子项|none|
|» id|number|true|none|id|none|
|» source|string|true|none|来源|enum: ["sys"]|
|» source_id|number|true|none|source的ID|none|
|pBranch|string|true|none|对象ID|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|deleted|number|true|none|删除状态|enum: [0, 1]|

#### 枚举值

|属性|值|
|---|---|
|source|sys|
|deleted|0|
|deleted|1|

<h2 id="tocS_team-prep">team-prep</h2>

<a id="schemateam-prep"></a>
<a id="schema_team-prep"></a>
<a id="tocSteam-prep"></a>
<a id="tocsteam-prep"></a>

```json
{
  "name": "宣讲演示",
  "end_time": "string",
  "remark": "",
  "edu_plan_id": "string",
  "teachers": [
    "string"
  ],
  "children": [
    {
      "name": "string",
      "key": "chapter",
      "source_id": "string",
      "children": [
        {
          "name": "string",
          "key": "chapter",
          "source_id": "string",
          "children": [
            {}
          ],
          "id": "string"
        }
      ],
      "id": "string"
    }
  ],
  "status": "done",
  "period": "高中",
  "subject": "数学",
  "grade": "高一",
  "from_year": 2024,
  "to_year": 2025,
  "semester": "下学期",
  "school_id": 55553,
  "main_teacher_id": "string",
  "pBranch": "string",
  "creator": "string",
  "operator": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "deleted": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["宣讲演示", "新建集备任务", "测试测试", "创建集备任务", "集备测试", "测试集备任务", "测试集备1"]|
|end_time|string|true|none|end时间|none|
|remark|string|true|none|备注|enum: ["", "谨慎备课", "添加备注", "测试"]|
|edu_plan_id|string|true|none|edu_plan的ID|none|
|teachers|[string]|true|none|teachers列表|none|
|children|[object]|true|none|子项|none|
|» name|string|true|none|名称|none|
|» key|string|true|none|键/标识符|enum: ["chapter"]|
|» source_id|string|true|none|source的ID|none|
|» children|[object]|true|none|子项|none|
|»» name|string|true|none|名称|none|
|»» key|string|true|none|键/标识符|enum: ["chapter"]|
|»» source_id|string|true|none|source的ID|none|
|»» children|[object]|true|none|子项|none|
|»»» key|string|true|none|键/标识符|enum: ["lesson"]|
|»»» name|string|true|none|名称|enum: ["1", "12", "列举法", "描述法"]|
|»»» index|number|true|none|索引|enum: [1, 2, 3]|
|»»» id|string|true|none|id|none|
|»»» items|[string]|true|none|items列表|none|
|»»» items_status|object|false|none|items_status|none|
|»»»» homework|number|true|none|homework|enum: [0]|
|»» id|string|true|none|id|none|
|» id|string|true|none|id|none|
|status|string|true|none|状态|enum: ["done", "doing"]|
|period|string|true|none|学段|enum: ["高中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|grade|string|true|none|年级|enum: ["高一"]|
|from_year|number|true|none|from_year|enum: [2024]|
|to_year|number|true|none|to_year|enum: [2025]|
|semester|string|true|none|semester|enum: ["下学期"]|
|school_id|number|true|none|学校的ID|enum: [55553]|
|main_teacher_id|string|true|none|main_teacher的ID|none|
|pBranch|string|true|none|对象ID|none|
|creator|string|true|none|对象ID|none|
|operator|string|true|none|操作人|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|deleted|number|true|none|删除状态|enum: [0, 1]|

#### 枚举值

|属性|值|
|---|---|
|name|宣讲演示|
|name|新建集备任务|
|name|测试测试|
|name|创建集备任务|
|name|集备测试|
|name|测试集备任务|
|name|测试集备1|
|remark||
|remark|谨慎备课|
|remark|添加备注|
|remark|测试|
|key|chapter|
|key|chapter|
|key|lesson|
|name|1|
|name|12|
|name|列举法|
|name|描述法|
|index|1|
|index|2|
|index|3|
|homework|0|
|status|done|
|status|doing|
|period|高中|
|subject|数学|
|grade|高一|
|from_year|2024|
|to_year|2025|
|semester|下学期|
|school_id|55553|
|deleted|0|
|deleted|1|

<h2 id="tocS_users-permissions_branch">users-permissions_branch</h2>

<a id="schemausers-permissions_branch"></a>
<a id="schema_users-permissions_branch"></a>
<a id="tocSusers-permissions_branch"></a>
<a id="tocsusers-permissions_branch"></a>

```json
{
  "periods": [
    "string"
  ],
  "subjects": [
    "string"
  ],
  "name": "数字化录入学校",
  "shortName": "数字化录入学校",
  "type": "42510",
  "yjSchoolId": 42510,
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|periods|[string]|true|none|periods列表|none|
|subjects|[string]|true|none|subjects列表|none|
|name|string|true|none|名称|enum: ["数字化录入学校", "天台县实验中学", "云校教研学校", "默认租户"]|
|shortName|string|false|none|short名称|enum: ["数字化录入学校", "天台县实验中学", "云校教研学校"]|
|type|string|true|none|类型|enum: ["42510", "19887", "101382", "87023", "31570", "55553", "11732", "default"]|
|yjSchoolId|number|false|none|yjSchool的ID|enum: [42510, 19887, 101382, 87023, 31570, 55553, 11732]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|数字化录入学校|
|name|天台县实验中学|
|name|云校教研学校|
|name|默认租户|
|shortName|数字化录入学校|
|shortName|天台县实验中学|
|shortName|云校教研学校|
|type|42510|
|type|19887|
|type|101382|
|type|87023|
|type|31570|
|type|55553|
|type|11732|
|type|default|
|yjSchoolId|42510|
|yjSchoolId|19887|
|yjSchoolId|101382|
|yjSchoolId|87023|
|yjSchoolId|31570|
|yjSchoolId|55553|
|yjSchoolId|11732|

<h2 id="tocS_users-permissions_group">users-permissions_group</h2>

<a id="schemausers-permissions_group"></a>
<a id="schema_users-permissions_group"></a>
<a id="tocSusers-permissions_group"></a>
<a id="tocsusers-permissions_group"></a>

```json
{
  "isPreset": true,
  "pages": "string",
  "sId": "string",
  "name": "string",
  "apiPermissions": [
    {
      "type": "string",
      "controller": "basket",
      "action": "find",
      "enabled": true,
      "policy": "",
      "path": "string",
      "method": "GET"
    }
  ],
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|isPreset|boolean|true|none|isPreset|enum: [true]|
|pages|string|true|none|pages列表|none|
|sId|string|true|none|s的ID|none|
|name|string|true|none|名称|none|
|apiPermissions|[object]|true|none|apiPermissions列表|none|
|» type|string|true|none|类型|none|
|» controller|string|true|none|controller|enum: ["basket"]|
|» action|string|true|none|action|enum: ["find", "count", "export", "import", "findOne", "create", "update", "delete"]|
|» enabled|boolean|true|none|enabled|enum: [true]|
|» policy|string|true|none|policy|enum: [""]|
|» path|string|true|none|路径|none|
|» method|string|true|none|method|enum: ["GET", "POST", "PUT", "DELETE"]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|isPreset|true|
|controller|basket|
|action|find|
|action|count|
|action|export|
|action|import|
|action|findOne|
|action|create|
|action|update|
|action|delete|
|enabled|true|
|policy||
|method|GET|
|method|POST|
|method|PUT|
|method|DELETE|

<h2 id="tocS_users-permissions_permission">users-permissions_permission</h2>

<a id="schemausers-permissions_permission"></a>
<a id="schema_users-permissions_permission"></a>
<a id="tocSusers-permissions_permission"></a>
<a id="tocsusers-permissions_permission"></a>

```json
{
  "type": "upload",
  "controller": "string",
  "action": "string",
  "enabled": true,
  "policy": "",
  "role": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|string|true|none|类型|enum: ["upload", "log"]|
|controller|string|true|none|controller|none|
|action|string|true|none|action|none|
|enabled|boolean|true|none|enabled|enum: [true, false]|
|policy|string|false|none|policy|enum: [""]|
|role|string|true|none|角色|none|

#### 枚举值

|属性|值|
|---|---|
|type|upload|
|type|log|
|enabled|true|
|enabled|false|
|policy||

<h2 id="tocS_users-permissions_role">users-permissions_role</h2>

<a id="schemausers-permissions_role"></a>
<a id="schema_users-permissions_role"></a>
<a id="tocSusers-permissions_role"></a>
<a id="tocsusers-permissions_role"></a>

```json
{
  "isPreset": true,
  "modules": [
    "string"
  ],
  "name": "阅卷老师",
  "type": "teacher",
  "description": "阅卷老师"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|isPreset|boolean|true|none|isPreset|enum: [true]|
|modules|[string]|true|none|modules列表|none|
|name|string|true|none|名称|enum: ["阅卷老师", "管理员", "未登录", "普通用户", "超级管理员"]|
|type|string|true|none|类型|enum: ["teacher", "admin", "public"]|
|description|string|true|none|描述|enum: ["阅卷老师", "平台管理员"]|

#### 枚举值

|属性|值|
|---|---|
|isPreset|true|
|name|阅卷老师|
|name|管理员|
|name|未登录|
|name|普通用户|
|name|超级管理员|
|type|teacher|
|type|admin|
|type|public|
|description|阅卷老师|
|description|平台管理员|

<h2 id="tocS_users-permissions_user">users-permissions_user</h2>

<a id="schemausers-permissions_user"></a>
<a id="schema_users-permissions_user"></a>
<a id="tocSusers-permissions_user"></a>
<a id="tocsusers-permissions_user"></a>

```json
{
  "confirmed": true,
  "blocked": false,
  "loginCodeTryCount": 0,
  "loginPasswordTryCount": 0,
  "roles": [
    "string"
  ],
  "pBranches": [
    "string"
  ],
  "yjUserId": "string",
  "username": "赵原01",
  "yjUserInfo": {
    "userId": "string",
    "userName": "赵原01",
    "schoolId": 55553,
    "schoolName": "数字化录入学校",
    "phone": "string",
    "isSadmin": false,
    "isAdmin": false,
    "schoolRoleType": "string",
    "disable": false,
    "loginName": "string",
    "isYxZhiXue": 1,
    "phoneCheck": false,
    "vipType": 1,
    "siteType": "hfs",
    "schoolType": "校内",
    "weixinBind": true
  },
  "thirdParties": [
    "string"
  ],
  "createdAt": "string",
  "updatedAt": "string",
  "pBranch": "string",
  "role": "string",
  "curPeriod": "string",
  "curSubject": "string",
  "email": "string",
  "password": "string",
  "provider": "local"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|confirmed|boolean|true|none|confirmed|enum: [true]|
|blocked|boolean|true|none|blocked|enum: [false]|
|loginCodeTryCount|number|true|none|loginCodeTry数量|enum: [0]|
|loginPasswordTryCount|number|true|none|loginPasswordTry数量|enum: [0]|
|roles|[string]|true|none|roles列表|none|
|pBranches|[string]|true|none|pBranches列表|none|
|yjUserId|string|false|none|yjUser的ID|none|
|username|string|true|none|username|enum: ["赵原01", "徐双实", "侯广强", "徐振洲", "戴以泽", "测试3", "韩校长", "陈健"]|
|yjUserInfo|object|false|none|yjUserInfo|none|
|» userId|string|true|none|user的ID|none|
|» userName|string|true|none|user名称|enum: ["赵原01", "徐双实", "侯广强", "徐振洲", "戴以泽", "测试3", "韩校长", "陈健"]|
|» schoolId|number|true|none|school的ID|enum: [55553, 42510, 19887, 101382, 87023, 31570, 11732]|
|» schoolName|string|true|none|school名称|enum: ["数字化录入学校", "天台县实验中学", "云校教研学校"]|
|» phone|string|true|none|phone|none|
|» isSadmin|boolean|true|none|isSadmin|enum: [false]|
|» isAdmin|boolean|true|none|isAdmin|enum: [false, true]|
|» schoolRoleType|string|true|none|schoolRoleType列表|none|
|» disable|boolean|true|none|disable|enum: [false]|
|» loginName|string|true|none|login名称|none|
|» isYxZhiXue|number|false|none|isYxZhiXue|enum: [1, 0]|
|» phoneCheck|boolean|false|none|phoneCheck|enum: [false, true]|
|» vipType|number|false|none|vipType|enum: [1, 0]|
|» siteType|string|false|none|siteType|enum: ["hfs", "normal"]|
|» schoolType|string|true|none|schoolType|enum: ["校内"]|
|» weixinBind|boolean|false|none|weixinBind|enum: [true]|
|thirdParties|[string]|true|none|thirdParties列表|none|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|
|pBranch|string|false|none|对象ID|none|
|role|string|true|none|角色|none|
|curPeriod|string|false|none|对象ID|none|
|curSubject|string|false|none|对象ID|none|
|email|string|false|none|email|none|
|password|string|false|none|password|none|
|provider|string|false|none|provider|enum: ["local"]|

#### 枚举值

|属性|值|
|---|---|
|confirmed|true|
|blocked|false|
|loginCodeTryCount|0|
|loginPasswordTryCount|0|
|username|赵原01|
|username|徐双实|
|username|侯广强|
|username|徐振洲|
|username|戴以泽|
|username|测试3|
|username|韩校长|
|username|陈健|
|userName|赵原01|
|userName|徐双实|
|userName|侯广强|
|userName|徐振洲|
|userName|戴以泽|
|userName|测试3|
|userName|韩校长|
|userName|陈健|
|schoolId|55553|
|schoolId|42510|
|schoolId|19887|
|schoolId|101382|
|schoolId|87023|
|schoolId|31570|
|schoolId|11732|
|schoolName|数字化录入学校|
|schoolName|天台县实验中学|
|schoolName|云校教研学校|
|isSadmin|false|
|isAdmin|false|
|isAdmin|true|
|disable|false|
|isYxZhiXue|1|
|isYxZhiXue|0|
|phoneCheck|false|
|phoneCheck|true|
|vipType|1|
|vipType|0|
|siteType|hfs|
|siteType|normal|
|schoolType|校内|
|weixinBind|true|
|provider|local|

<h2 id="tocS_users-permissions_view">users-permissions_view</h2>

<a id="schemausers-permissions_view"></a>
<a id="schema_users-permissions_view"></a>
<a id="tocSusers-permissions_view"></a>
<a id="tocsusers-permissions_view"></a>

```json
{
  "isPreset": true,
  "isSystem": false,
  "hideMenu": false,
  "sId": "string",
  "parent": "string",
  "name": "string",
  "icon": "vpn_key",
  "meta": {
    "modelId": "basket",
    "modelPath": "baskets"
  },
  "createdAt": "string",
  "updatedAt": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|isPreset|boolean|true|none|isPreset|enum: [true]|
|isSystem|boolean|true|none|isSystem|enum: [false, true]|
|hideMenu|boolean|true|none|hideMenu|enum: [false]|
|sId|string|true|none|s的ID|none|
|parent|string|true|none|parent|none|
|name|string|true|none|名称|none|
|icon|string|true|none|icon|enum: ["vpn_key", "article", "folder", "token", "filter", "menu", "api", "preview", "apps"]|
|meta|object|true|none|meta|none|
|» modelId|string|false|none|model的ID|enum: ["basket", "book", "subject", "period", "file"]|
|» modelPath|string|false|none|modelPath|enum: ["baskets", "books", "periods", "files"]|
|createdAt|string|true|none|创建时间|none|
|updatedAt|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|isPreset|true|
|isSystem|false|
|isSystem|true|
|hideMenu|false|
|icon|vpn_key|
|icon|article|
|icon|folder|
|icon|token|
|icon|filter|
|icon|menu|
|icon|api|
|icon|preview|
|icon|apps|
|modelId|basket|
|modelId|book|
|modelId|subject|
|modelId|period|
|modelId|file|
|modelPath|baskets|
|modelPath|books|
|modelPath|periods|
|modelPath|files|

<h2 id="tocS_exampaper_question_template">exampaper_question_template</h2>

<a id="schemaexampaper_question_template"></a>
<a id="schema_exampaper_question_template"></a>
<a id="tocSexampaper_question_template"></a>
<a id="tocsexampaper_question_template"></a>

```json
{
  "name": "string",
  "period": "初中",
  "subject": "化学",
  "blocks": [
    {
      "type": "选择题",
      "num": 15
    }
  ],
  "user_id": 0,
  "type": "custom",
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|subject|string|true|none|学科|enum: ["数学", "历史", "化学", "物理", "道德与法治", "英语", "政治", "地理", "生物", "语文"]|
|blocks|[object]|true|none|blocks列表|none|
|» type|string|true|none|类型|enum: ["选择题", "多选题", "填空题", "解答题", "判断题", "论述题"]|
|» num|number|true|none|数量|enum: [10, 5, 19, 20, 4, 2, 40]|
|user_id|number|true|none|用户ID|enum: ["0"]|
|type|string|true|none|类型|enum: ["custom", "sys"]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|
|period|中职|
|subject|化学|
|subject|数学|
|subject|物理|
|subject|语文|
|subject|生物|
|subject|政治|
|subject|历史|
|subject|地理|
|subject|英语|
|type|选择题|
|type|填空题|
|type|实验探究题|
|type|解答题|
|type|作图题|
|num|15|
|num|3|
|num|1|
|num|2|
|num|10|
|num|6|
|num|5|
|num|30|
|num|25|
|type|custom|
|type|sys|

<h2 id="tocS_tw_specification">tw_specification</h2>

<a id="schematw_specification"></a>
<a id="schema_tw_specification"></a>
<a id="tocStw_specification"></a>
<a id="tocstw_specification"></a>

```json
{
  "user_id": 0,
  "table_id": "string",
  "ctime": "string",
  "period": "string",
  "subject": "string",
  "type": "string",
  "grade": "string",
  "province": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|table_id|string|true|none|table的ID|none|
|ctime|string|true|none|创建时间|none|
|period|string|true|none|学段|enum: ["高中", "初中"]|
|subject|string|true|none|学科|enum: ["数学"]|
|type|string|false|none|类型|enum: ["高考模拟", "期中试卷", "月考试卷", "期末试卷"]|
|grade|string|false|none|年级|enum: ["高考专题", "高一下", "高二下", "高一上", "八年级下", "九年级上", "高二上"]|
|province|string|false|none|省份|enum: ["广东", "河北", "四川", "新疆", "上海", "北京", "河南", "山西", "广西"]|

<h2 id="tocS_basket">basket</h2>

<a id="schemabasket"></a>
<a id="schema_basket"></a>
<a id="tocSbasket"></a>
<a id="tocsbasket"></a>

```json
{
  "user_id": 0,
  "tiku_ques": [
    "string"
  ],
  "zyk_ques": [
    "string"
  ],
  "period": "高中",
  "subject": "数学",
  "name": "string",
  "subtitle": "string",
  "score": 0,
  "duration": 0,
  "paper_info": "string",
  "cand_info": "",
  "attentions": "<br>",
  "secret_tag": "绝密★启用前",
  "gutter": 0,
  "template": "exam",
  "volumes": [
    {
      "title": "卷I（选择题）",
      "note": "",
      "blocks": "string"
    }
  ],
  "utime": "string",
  "ctime": "string",
  "exampaper_id": "string",
  "partsList": "string",
  "province": "string",
  "city": "string",
  "sch_id": 0,
  "sch_name": "string",
  "grade": "string",
  "type": "string",
  "download_time": "string",
  "invalid_time": "string",
  "view_count": 0,
  "expired_time": "string",
  "exampaper_download_num": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|false|none|用户ID|none|
|tiku_ques|[string]|false|none|tiku_ques列表|none|
|zyk_ques|[string]|false|none|zyk_ques列表|none|
|period|string|false|none|学段|enum: ["高中", "初中", "", "小学"]|
|subject|string|false|none|学科|enum: ["数学", "地理", "政治", "语文", "物理", "", "历史", "英语", "生物", "化学"]|
|name|string|false|none|名称|none|
|subtitle|string|false|none|subtitle|none|
|score|number|false|none|分数|enum: [10, 3]|
|duration|number|false|none|持续时间（分钟）|none|
|paper_info|string|false|none|paper_info|none|
|cand_info|string|false|none|cand_info|enum: ["", " "]|
|attentions|string|false|none|attentions|enum: ["<br>", "", "sse<br>"]|
|secret_tag|string|false|none|secret_tag|enum: ["绝密★启用前", ""]|
|gutter|number|false|none|gutter|enum: [0]|
|template|string|false|none|模板|enum: ["exam", ""]|
|volumes|[object]|false|none|volumes列表|none|
|» title|string|true|none|标题|enum: ["卷I（选择题）"]|
|» note|string|true|none|note|enum: [""]|
|» blocks|string|true|none|blocks列表|none|
|utime|string|false|none|更新时间|none|
|ctime|string|false|none|创建时间|none|
|exampaper_id|string|false|none|试卷ID|none|
|partsList|string|false|none|partsList列表|none|
|province|string|false|none|省份|none|
|city|string|false|none|城市|none|
|sch_id|number|false|none|学校ID|none|
|sch_name|string|false|none|sch名称|none|
|grade|string|false|none|年级|none|
|type|string|false|none|类型|enum: ["填空题", "选择题"]|
|download_time|string|false|none|download时间|none|
|invalid_time|string|false|none|invalid时间|none|
|view_count|number|false|none|浏览次数|none|
|expired_time|string|false|none|过期时间|none|
|exampaper_download_num|number|false|none|exampaper_download_num|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period||
|period|小学|
|subject|数学|
|subject|地理|
|subject|政治|
|subject|语文|
|subject|物理|
|subject||
|subject|历史|
|subject|英语|
|subject|生物|
|subject|化学|
|cand_info||
|cand_info| |
|attentions|<br>|
|attentions||
|attentions|sse<br>|
|secret_tag|绝密★启用前|
|secret_tag||
|gutter|0|
|template|exam|
|template||
|title|卷I（选择题）|
|note||

<h2 id="tocS_exampaper">exampaper</h2>

<a id="schemaexampaper"></a>
<a id="schema_exampaper"></a>
<a id="tocSexampaper"></a>
<a id="tocsexampaper"></a>

```json
{
  "period": "高中",
  "subject": "string",
  "name": "string",
  "subtitle": "",
  "score": 0,
  "duration": 120,
  "paper_info": "",
  "cand_info": "",
  "attentions": "",
  "secret_tag": "绝密★启用前",
  "gutter": 0,
  "template": "exam",
  "partsList": "string",
  "volumes": [
    {
      "title": "卷I（选择题）",
      "note": "",
      "blocks": [
        {
          "title": "string",
          "note": "",
          "type": "选择题",
          "default_score": 3,
          "questions": [
            {}
          ]
        }
      ]
    }
  ],
  "user_id": 0,
  "sch_id": 0,
  "sch_name": "string",
  "province": "string",
  "city": "string",
  "ctime": "string",
  "utime": "string",
  "type": "考后巩固",
  "display": 0,
  "grade": "string",
  "category": 2,
  "view_count": 0,
  "invalid_time": "string",
  "download_time": "string",
  "exampaper_download_num": 1,
  "exampaper_id": "string",
  "press_version": "",
  "knowledge_tree": "",
  "score_info": "",
  "dtk_id": 0,
  "last_sync_time": "string",
  "yj_exams": [
    {
      "subject_name": "高二数学",
      "exam_name": "string",
      "exam_id": 0,
      "subject_id": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "小学", "初中"]|
|subject|string|true|none|学科|none|
|name|string|true|none|名称|none|
|subtitle|string|true|none|subtitle|enum: [""]|
|score|number|true|none|分数|enum: [29, 32, 39, 12, 135, 56, 76, 63]|
|duration|number|true|none|持续时间（分钟）|enum: [120]|
|paper_info|string|true|none|paper_info|enum: [""]|
|cand_info|string|true|none|cand_info|enum: [""]|
|attentions|string|true|none|attentions|enum: [""]|
|secret_tag|string|true|none|secret_tag|enum: ["绝密★启用前"]|
|gutter|number|true|none|gutter|enum: [0]|
|template|string|false|none|模板|enum: ["exam"]|
|partsList|string|false|none|partsList列表|none|
|volumes|[object]|true|none|volumes列表|none|
|» title|string|true|none|标题|enum: ["卷I（选择题）"]|
|» note|string|true|none|note|enum: [""]|
|» blocks|[object]|true|none|blocks列表|none|
|»» title|string|true|none|标题|none|
|»» note|string|true|none|note|enum: [""]|
|»» type|string|true|none|类型|enum: ["选择题", "多选题", "填空题", "解答题", "实验探究题"]|
|»» default_score|number|true|none|default_score|enum: [3, 10, 15]|
|»» questions|[object]|true|none|试题列表|none|
|»»» id|number|true|none|id|none|
|»»» period|string|true|none|学段|enum: ["高中"]|
|»»» subject|string|true|none|学科|enum: ["数学"]|
|»»» score|number|true|none|分数|enum: [3, 10]|
|user_id|number|true|none|用户ID|none|
|sch_id|number|true|none|学校ID|none|
|sch_name|string|true|none|sch名称|none|
|province|string|false|none|省份|none|
|city|string|false|none|城市|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|type|string|false|none|类型|enum: ["同步练习"]|
|display|number|false|none|display|enum: [0]|
|grade|string|false|none|年级|none|
|category|number|false|none|分类|enum: [2, 1, 3, 4, 5]|
|view_count|number|false|none|浏览次数|none|
|invalid_time|string|false|none|invalid时间|none|
|download_time|string|false|none|download时间|none|
|exampaper_download_num|number|false|none|exampaper_download_num|enum: [1, 2, 3, 4, 5, 7]|
|exampaper_id|string|false|none|试卷ID|none|
|press_version|string|false|none|教材版本|enum: [""]|
|knowledge_tree|string|false|none|knowledge_tree|enum: [""]|
|score_info|string|false|none|score_info|enum: [""]|
|dtk_id|number|false|none|dtk的ID|none|
|last_sync_time|string|false|none|last_sync时间|none|
|yj_exams|[object]|false|none|yj_exams列表|none|
|» subject_name|string|true|none|subject名称|enum: ["高二数学"]|
|» exam_name|string|true|none|考试名称|none|
|» exam_id|number|true|none|考试的ID|none|
|» subject_id|number|true|none|subject的ID|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|小学|
|period|初中|
|subtitle||
|subtitle|政治试卷|
|subtitle|大石锅拌饭|
|subtitle|高二数学 |
|duration|120|
|duration|100|
|duration|40|
|duration|75|
|duration|60|
|duration|150|
|duration|90|
|duration|0|
|duration|80|
|paper_info||
|cand_info||
|attentions||
|secret_tag|绝密★启用前|
|secret_tag||
|gutter|0|
|template|exam|
|title|卷I（选择题）|
|note||
|note||
|type|选择题|
|type|多选题|
|type|填空题|
|type|解答题|
|type|实验探究题|
|default_score|3|
|default_score|10|
|default_score|15|
|period|高中|
|subject|数学|
|score|3|
|score|10|
|type|考后巩固|
|type||
|display|0|
|category|2|
|category|1|
|category|3|
|category|4|
|category|5|
|exampaper_download_num|1|
|exampaper_download_num|2|
|exampaper_download_num|3|
|exampaper_download_num|4|
|exampaper_download_num|5|
|exampaper_download_num|7|
|press_version||
|knowledge_tree||
|score_info||
|subject_name|高二数学|

<h2 id="tocS_@Goods">@Goods</h2>

<a id="schema@goods"></a>
<a id="schema_@Goods"></a>
<a id="tocS@goods"></a>
<a id="tocs@goods"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "name": "试题单次下载",
  "type": "member",
  "status": "deleted",
  "original_price": 600,
  "discount_price": 0,
  "final_price": 600,
  "resource_type": "string",
  "month": 12,
  "count": 280
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|name|string|true|none|名称|enum: ["试题单次下载", "试卷单次下载", "组卷单次下载", "题库年度会员", "题库半年会员", "题库单月会员"]|
|type|string|true|none|类型|enum: ["member"]|
|status|string|true|none|状态|enum: ["deleted"]|
|original_price|number|true|none|original_price|enum: [600, 100, 35900, 23900, 6900]|
|discount_price|number|true|none|discount_price|enum: [0, 6000, 4000, 2000, 6899]|
|final_price|number|true|none|final_price|enum: [600, 100, 29900, 19900, 4900, 1]|
|resource_type|string|false|none|资源类型|none|
|month|number|false|none|月份|enum: [12, 6, 1]|
|count|number|false|none|数量|enum: [280, 120, 15]|

#### 枚举值

|属性|值|
|---|---|
|name|试题单次下载|
|name|试卷单次下载|
|name|组卷单次下载|
|name|题库年度会员|
|name|题库半年会员|
|name|题库单月会员|
|type|member|
|status|deleted|
|original_price|600|
|original_price|100|
|original_price|35900|
|original_price|23900|
|original_price|6900|
|discount_price|0|
|discount_price|6000|
|discount_price|4000|
|discount_price|2000|
|discount_price|6899|
|final_price|600|
|final_price|100|
|final_price|29900|
|final_price|19900|
|final_price|4900|
|final_price|1|
|month|12|
|month|6|
|month|1|
|count|280|
|count|120|
|count|15|

<h2 id="tocS_@MessageCentre">@MessageCentre</h2>

<a id="schema@messagecentre"></a>
<a id="schema_@MessageCentre"></a>
<a id="tocS@messagecentre"></a>
<a id="tocs@messagecentre"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "type": "string",
  "situation": "string",
  "unique_key": "string",
  "phone": "2343",
  "send_status": "success",
  "short_msg_content": "string",
  "short_msg_content_config": {
    "platform": "bdy",
    "template_id": "55",
    "content_var": {
      "vCode": "string"
    }
  },
  "send_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|type|string|true|none|类型|none|
|situation|string|true|none|situation|none|
|unique_key|string|true|none|unique_key|none|
|phone|string|true|none|phone|enum: ["2343"]|
|send_status|string|true|none|send_status|enum: ["success"]|
|short_msg_content|string|true|none|short_msg_content|none|
|short_msg_content_config|object|true|none|short_msg_content_config|none|
|» platform|string|true|none|platform|enum: ["bdy"]|
|» template_id|string|true|none|template的ID|enum: ["55"]|
|» content_var|object|true|none|content_var|none|
|»» vCode|string|true|none|vCode|none|
|send_time|string|false|none|send时间|none|

#### 枚举值

|属性|值|
|---|---|
|phone|2343|
|send_status|success|
|platform|bdy|
|template_id|55|

<h2 id="tocS_@NoticeCenter">@NoticeCenter</h2>

<a id="schema@noticecenter"></a>
<a id="schema_@NoticeCenter"></a>
<a id="tocS@noticecenter"></a>
<a id="tocs@noticecenter"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "title": "string",
  "detail": "string",
  "status": "off",
  "push_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|title|string|true|none|标题|none|
|detail|string|true|none|detail|none|
|status|string|true|none|状态|enum: ["off"]|
|push_time|string|true|none|push时间|none|

#### 枚举值

|属性|值|
|---|---|
|status|off|

<h2 id="tocS_@Order">@Order</h2>

<a id="schema@order"></a>
<a id="schema_@Order"></a>
<a id="tocS@order"></a>
<a id="tocs@order"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "no": "string",
  "user_id": 0,
  "uc_id": null,
  "status": "success",
  "type": "member",
  "original_price": 0,
  "discount_price": 0,
  "final_price": 0,
  "transaction_id": "string",
  "pay_through": "string",
  "goods": {
    "spu": {
      "id": "string",
      "name": "Plus会员"
    },
    "sku": {
      "id": "string",
      "name": "12个月",
      "key": "string",
      "specs": [
        {
          "name": "[",
          "key": "[",
          "option": {}
        }
      ],
      "rights": [
        {
          "type": "string",
          "reset_type": "[",
          "limit": "[",
          "value": "[",
          "subject_check": "["
        }
      ]
    },
    "id": "0",
    "name": "资源下载",
    "items": [
      {
        "type": 6,
        "resource_type": "string",
        "resource_id": 766640127,
        "resource_name": "期中家长会",
        "resource_image": "",
        "resource_info": {
          "category": "[",
          "host": "string",
          "url": "string",
          "exam_id": "string",
          "name": "[",
          "category_id": "[",
          "suffix": "[",
          "invalid": "[",
          "user_id": "[",
          "user_name": "[",
          "download_times": "[",
          "view_times": "[",
          "ctime": "string",
          "utime": "string"
        }
      }
    ],
    "resource_type": "string",
    "resource_id": "string",
    "month": 1,
    "count": 15
  },
  "target": {
    "id": 0,
    "type": "string",
    "parent_id": "string"
  },
  "discount": 80,
  "discount_fee": 120,
  "trade_no": "string",
  "out_trade_no": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|no|string|true|none|no|none|
|user_id|number|true|none|用户ID|none|
|uc_id|null|true|none|uc的ID|none|
|status|string|true|none|状态|enum: ["success", "refund", "doing"]|
|type|string|true|none|类型|enum: ["member"]|
|original_price|number|true|none|original_price|none|
|discount_price|number|true|none|discount_price|none|
|final_price|number|true|none|final_price|none|
|transaction_id|string|true|none|transaction的ID|none|
|pay_through|string|true|none|pay_through|none|
|goods|object|true|none|goods|none|
|» spu|object|false|none|spu|none|
|»» id|string|true|none|id|none|
|»» name|string|true|none|名称|enum: ["Plus会员", "用户组卷下载", "尊享会员", "专辑付费下载"]|
|» sku|object|false|none|sku|none|
|»» id|string|true|none|id|none|
|»» name|string|true|none|名称|enum: ["12个月", "用户组卷下载", "专辑付费下载"]|
|»» key|string|true|none|键/标识符|none|
|»» specs|[object]|true|none|specs列表|none|
|»»» name|string|true|none|名称|enum: ["时间", "学段", "科目"]|
|»»» key|string|true|none|键/标识符|enum: ["time", "period", "subject"]|
|»»» option|object|true|none|option|none|
|»»»» name|string|true|none|名称|enum: ["12个月", "小学", "数学", "1个月", "初中"]|
|»»»» value|string|true|none|值|enum: ["12", "小学", "数学", "1", "初中"]|
|»» rights|[object]|true|none|rights列表|none|
|»»» type|string|true|none|类型|none|
|»»» reset_type|string|true|none|reset_type|enum: ["fixed", "month", "day"]|
|»»» limit|number|true|none|limit|enum: [1, -1, 2, 60, 5, 1000, 20]|
|»»» value|number|true|none|值|enum: [0, 80]|
|»»» subject_check|number|true|none|subject_check|enum: [0, 1]|
|» id|string|false|none|id|enum: ["0"]|
|» name|string|false|none|名称|enum: ["资源下载", "组卷单次下载", "题库单月会员", "题库半年会员", "题库年度会员", "试卷单次下载", "试题单次下载"]|
|» items|[object]|false|none|items列表|none|
|»» type|number|true|none|类型|enum: [6, 4, 7]|
|»» resource_type|string|true|none|资源类型|none|
|»» resource_id|number|true|none|资源ID|enum: [766640127, 766705663, 786694143, 786628607, 766836735, 766771199, 2127495167]|
|»» resource_name|string|true|none|resource名称|enum: ["期中家长会"]|
|»» resource_image|string|true|none|resource_image|enum: [""]|
|»» resource_info|object|true|none|resource_info|none|
|»»» category|string|false|none|分类|enum: [1, 2, 3]|
|»»» host|string|false|none|域名|none|
|»»» url|string|false|none|资源地址|none|
|»»» exam_id|string|false|none|考试的ID|none|
|»»» name|string|false|none|名称|enum: ["期中家长会"]|
|»»» category_id|number|false|none|category的ID|enum: [2147090431]|
|»»» suffix|string|false|none|suffix|enum: ["pptx"]|
|»»» invalid|number|false|none|invalid|enum: [0]|
|»»» user_id|number|false|none|用户ID|enum: [5959698677104640]|
|»»» user_name|string|false|none|用户名称|enum: ["张蓓"]|
|»»» download_times|number|false|none|下载次数|enum: [1]|
|»»» view_times|number|false|none|view_times|enum: [43]|
|»»» ctime|string|false|none|创建时间|none|
|»»» utime|string|false|none|更新时间|none|
|» resource_type|string|false|none|资源类型|none|
|» resource_id|string|false|none|资源ID|none|
|» month|number|false|none|月份|enum: [1, 6, 12]|
|» count|number|false|none|数量|enum: [15, 120, 280]|
|target|object|false|none|目标/跳转目标|none|
|» id|number|true|none|id|none|
|» type|string|true|none|类型|none|
|» parent_id|string|false|none|父级ID|none|
|discount|number|false|none|discount|enum: [80, 0, 70]|
|discount_fee|number|false|none|discount_fee|enum: [120, 0, 40, 180, 60]|
|trade_no|string|false|none|trade_no|none|
|out_trade_no|string|false|none|out_trade_no|none|

#### 枚举值

|属性|值|
|---|---|
|status|success|
|status|refund|
|status|doing|
|type|member|
|name|Plus会员|
|name|用户组卷下载|
|name|尊享会员|
|name|专辑付费下载|
|name|12个月|
|name|用户组卷下载|
|name|专辑付费下载|
|name|时间|
|name|学段|
|name|科目|
|key|time|
|key|period|
|key|subject|
|name|12个月|
|name|小学|
|name|数学|
|name|1个月|
|name|初中|
|value|12|
|value|小学|
|value|数学|
|value|1|
|value|初中|
|reset_type|fixed|
|reset_type|month|
|reset_type|day|
|limit|1|
|limit|-1|
|limit|2|
|limit|60|
|limit|5|
|limit|1000|
|limit|20|
|value|0|
|value|80|
|subject_check|0|
|subject_check|1|
|id|0|
|name|资源下载|
|name|组卷单次下载|
|name|题库单月会员|
|name|题库半年会员|
|name|题库年度会员|
|name|试卷单次下载|
|name|试题单次下载|
|type|6|
|type|4|
|type|7|
|resource_id|766640127|
|resource_id|766705663|
|resource_id|786694143|
|resource_id|786628607|
|resource_id|766836735|
|resource_id|766771199|
|resource_id|2127495167|
|resource_name|期中家长会|
|resource_image||
|category|1|
|category|2|
|category|3|
|name|期中家长会|
|category_id|2147090431|
|suffix|pptx|
|invalid|0|
|user_id|5959698677104640|
|user_name|张蓓|
|download_times|1|
|view_times|43|
|month|1|
|month|6|
|month|12|
|count|15|
|count|120|
|count|280|
|discount|80|
|discount|0|
|discount|70|
|discount_fee|120|
|discount_fee|0|
|discount_fee|40|
|discount_fee|180|
|discount_fee|60|

<h2 id="tocS_@RefundRecord">@RefundRecord</h2>

<a id="schema@refundrecord"></a>
<a id="schema_@RefundRecord"></a>
<a id="tocS@refundrecord"></a>
<a id="tocs@refundrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "status": "refund",
  "user_id": ****************,
  "order_id": "string",
  "order": "string",
  "transaction_id": "string",
  "reason": "测试支付退款",
  "amount": 4900,
  "operator": "侯广强"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|status|string|true|none|状态|enum: ["refund"]|
|user_id|number|true|none|用户ID|enum: [****************, 4980443812200448, 4463382107914240, 1955460734337024, 2621962190192640, 4396900420681728, 4405857168359424, 1111614328036847]|
|order_id|string|true|none|order的ID|none|
|order|string|true|none|排序/订单|none|
|transaction_id|string|true|none|transaction的ID|none|
|reason|string|true|none|reason|enum: ["测试支付退款", "测试退款", "研发测试充值"]|
|amount|number|true|none|amount|enum: [4900, 29900, 19900]|
|operator|string|true|none|操作人|enum: ["侯广强"]|

#### 枚举值

|属性|值|
|---|---|
|status|refund|
|user_id|****************|
|user_id|4980443812200448|
|user_id|4463382107914240|
|user_id|1955460734337024|
|user_id|2621962190192640|
|user_id|4396900420681728|
|user_id|4405857168359424|
|user_id|1111614328036847|
|reason|测试支付退款|
|reason|测试退款|
|reason|研发测试充值|
|amount|4900|
|amount|29900|
|amount|19900|
|operator|侯广强|

<h2 id="tocS_@SchoolOrderRecord">@SchoolOrderRecord</h2>

<a id="schema@schoolorderrecord"></a>
<a id="schema_@SchoolOrderRecord"></a>
<a id="tocS@schoolorderrecord"></a>
<a id="tocs@schoolorderrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "key": "string",
  "order_id": "string",
  "contract_id": "string",
  "distributor": "测试专用代理商",
  "status": "normal",
  "remark": "string",
  "boss_fee": 0,
  "stime": "string",
  "etime": "string",
  "order_time": "string",
  "regist_time": "string",
  "sch_ids": [
    0
  ],
  "teacher_count": 0,
  "agent": {
    "id": "string",
    "name": "测试专用代理商",
    "type": "agent"
  },
  "params": "string",
  "vip_type": "school",
  "type": "jicai",
  "operator": "string",
  "source": "xiangmu",
  "records": [
    {
      "ctime": "string",
      "utime": "string",
      "stime": "string",
      "etime": "string",
      "operator": "string",
      "key": "string",
      "status": "normal",
      "type": "jicai",
      "source": "xiangmu",
      "vip_type": "string",
      "sch_ids": [
        0
      ],
      "contract_id": "string",
      "distributor": "直营",
      "regist_time": "string",
      "boss_fee": 0,
      "order_id": "string",
      "remark": "string",
      "rate": 10,
      "tiku_fee": 99900
    }
  ],
  "rate": 10,
  "tiku_fee": 1000
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|key|string|false|none|键/标识符|none|
|order_id|string|false|none|order的ID|none|
|contract_id|string|false|none|contract的ID|none|
|distributor|string|false|none|distributor|enum: ["测试专用代理商", "代理人-孟红远", "绥滨县第一中学", "代理人-孟舜", "代理人-向五洲", "自营"]|
|status|string|true|none|状态|enum: ["normal"]|
|remark|string|false|none|备注|none|
|boss_fee|number|false|none|boss_fee|none|
|stime|string|true|none|开始时间|none|
|etime|string|true|none|结束时间|none|
|order_time|string|false|none|order时间|none|
|regist_time|string|false|none|regist时间|none|
|sch_ids|[number]|true|none|sch_ids列表|none|
|teacher_count|number|false|none|teacher数量|none|
|agent|object|false|none|agent|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["测试专用代理商", "代理人-孟红远", "绥滨县第一中学"]|
|» type|string|true|none|类型|enum: ["agent", "school"]|
|params|string|false|none|params列表|none|
|vip_type|string|true|none|会员类型|enum: ["school"]|
|type|string|false|none|类型|enum: ["jicai"]|
|operator|string|false|none|操作人|none|
|source|string|false|none|来源|enum: ["xiangmu", "qudao", "jzl"]|
|records|[object]|false|none|records列表|none|
|» ctime|string|false|none|创建时间|none|
|» utime|string|true|none|更新时间|none|
|» stime|string|false|none|开始时间|none|
|» etime|string|false|none|结束时间|none|
|» operator|string|false|none|操作人|none|
|» key|string|false|none|键/标识符|none|
|» status|string|false|none|状态|enum: ["normal"]|
|» type|string|false|none|类型|enum: ["jicai"]|
|» source|string|false|none|来源|enum: ["xiangmu", "qudao", "jzl"]|
|» vip_type|string|false|none|会员类型|none|
|» sch_ids|[number]|false|none|sch_ids列表|none|
|» contract_id|string|false|none|contract的ID|none|
|» distributor|string|false|none|distributor|enum: ["直营"]|
|» regist_time|string|false|none|regist时间|none|
|» boss_fee|number|false|none|boss_fee|none|
|» order_id|string|false|none|order的ID|none|
|» remark|string|false|none|备注|none|
|» rate|number|false|none|评分/比例|enum: [10]|
|» tiku_fee|number|false|none|tiku_fee|enum: [99900, 1000]|
|rate|number|false|none|评分/比例|enum: [10]|
|tiku_fee|number|false|none|tiku_fee|enum: [1000]|

#### 枚举值

|属性|值|
|---|---|
|distributor|测试专用代理商|
|distributor|代理人-孟红远|
|distributor|绥滨县第一中学|
|distributor|代理人-孟舜|
|distributor|代理人-向五洲|
|distributor|自营|
|status|normal|
|name|测试专用代理商|
|name|代理人-孟红远|
|name|绥滨县第一中学|
|type|agent|
|type|school|
|vip_type|school|
|type|jicai|
|source|xiangmu|
|source|qudao|
|source|jzl|
|status|normal|
|type|jicai|
|source|xiangmu|
|source|qudao|
|source|jzl|
|distributor|直营|
|rate|10|
|tiku_fee|99900|
|tiku_fee|1000|
|rate|10|
|tiku_fee|1000|

<h2 id="tocS_@SchoolRecord">@SchoolRecord</h2>

<a id="schema@schoolrecord"></a>
<a id="schema_@SchoolRecord"></a>
<a id="tocS@schoolrecord"></a>
<a id="tocs@schoolrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "sch_id": 0,
  "operator": "string",
  "teachers": [
    0
  ],
  "type": "member",
  "vip_type": "string",
  "start_time": "string",
  "expired_time": "string",
  "expire_month": 3,
  "que_download_num": 30,
  "exampaper_download_num": 30,
  "assemble_download_num": 20,
  "que_details_num": 1000,
  "regist_time": "string",
  "teacher_count": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|sch_id|number|false|none|学校ID|none|
|operator|string|false|none|操作人|none|
|teachers|[number]|false|none|teachers列表|none|
|type|string|true|none|类型|enum: ["member", "trial"]|
|vip_type|string|false|none|会员类型|none|
|start_time|string|false|none|开始时间|none|
|expired_time|string|false|none|过期时间|none|
|expire_month|number|false|none|expire_month|enum: [3]|
|que_download_num|number|false|none|que_download_num|enum: [30, 50, 300, 10, 1]|
|exampaper_download_num|number|false|none|exampaper_download_num|enum: [30, 15, 10, 50, 100, 20, 1]|
|assemble_download_num|number|false|none|assemble_download_num|enum: [20, 10, 5, 30, 50, 90, 100, 3, 1, 15]|
|que_details_num|number|false|none|que_details_num|enum: [1000, 300, 100, 500, 30]|
|regist_time|string|false|none|regist时间|none|
|teacher_count|number|false|none|teacher数量|none|

#### 枚举值

|属性|值|
|---|---|
|type|member|
|type|trial|
|expire_month|3|
|que_download_num|30|
|que_download_num|50|
|que_download_num|300|
|que_download_num|10|
|que_download_num|1|
|exampaper_download_num|30|
|exampaper_download_num|15|
|exampaper_download_num|10|
|exampaper_download_num|50|
|exampaper_download_num|100|
|exampaper_download_num|20|
|exampaper_download_num|1|
|assemble_download_num|20|
|assemble_download_num|10|
|assemble_download_num|5|
|assemble_download_num|30|
|assemble_download_num|50|
|assemble_download_num|90|
|assemble_download_num|100|
|assemble_download_num|3|
|assemble_download_num|1|
|assemble_download_num|15|
|que_details_num|1000|
|que_details_num|300|
|que_details_num|100|
|que_details_num|500|
|que_details_num|30|

<h2 id="tocS_@SupportUser">@SupportUser</h2>

<a id="schema@supportuser"></a>
<a id="schema_@SupportUser"></a>
<a id="tocS@supportuser"></a>
<a id="tocs@supportuser"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "account": "liuzhen",
  "password": "123456",
  "name": "string",
  "role": "admin",
  "is_available": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|account|string|true|none|account|enum: ["liuzhen", "liyuan", "admin"]|
|password|string|true|none|password|enum: ["123456", "123465", "332131"]|
|name|string|true|none|名称|none|
|role|string|true|none|角色|enum: ["admin", "cs", "staff"]|
|is_available|boolean|false|none|is_available|enum: [true, false]|

#### 枚举值

|属性|值|
|---|---|
|account|liuzhen|
|account|liyuan|
|account|admin|
|password|123456|
|password|123465|
|password|332131|
|role|admin|
|role|cs|
|role|staff|
|is_available|true|
|is_available|false|

<h2 id="tocS_@SyncYjRecord">@SyncYjRecord</h2>

<a id="schema@syncyjrecord"></a>
<a id="schema_@SyncYjRecord"></a>
<a id="tocS@syncyjrecord"></a>
<a id="tocs@syncyjrecord"></a>

```json
{
  "sync_time": "string",
  "operator": 0,
  "type": "create",
  "exampaper_id": "string",
  "dtk_id": 0,
  "subject_name": "string",
  "exam_name": "string",
  "exam_id": 0,
  "subject_id": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|sync_time|string|true|none|sync时间|none|
|operator|number|true|none|操作人|none|
|type|string|true|none|类型|enum: ["create", "bind"]|
|exampaper_id|string|true|none|试卷ID|none|
|dtk_id|number|true|none|dtk的ID|none|
|subject_name|string|true|none|subject名称|none|
|exam_name|string|true|none|考试名称|none|
|exam_id|number|true|none|考试的ID|none|
|subject_id|number|true|none|subject的ID|none|

#### 枚举值

|属性|值|
|---|---|
|type|create|
|type|bind|

<h2 id="tocS_@TicketItem">@TicketItem</h2>

<a id="schema@ticketitem"></a>
<a id="schema_@TicketItem"></a>
<a id="tocS@ticketitem"></a>
<a id="tocs@ticketitem"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "status": "expire",
  "user_id": 0,
  "uc_id": null,
  "no": 0,
  "order": "operate",
  "update_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|status|string|true|none|状态|enum: ["expire", "used", "refund"]|
|user_id|number|true|none|用户ID|none|
|uc_id|null|false|none|uc的ID|none|
|no|number|true|none|no|none|
|order|string|true|none|排序/订单|enum: ["operate"]|
|update_time|string|false|none|update时间|none|

#### 枚举值

|属性|值|
|---|---|
|status|expire|
|status|used|
|status|refund|
|order|operate|

<h2 id="tocS_@TicketRecord">@TicketRecord</h2>

<a id="schema@ticketrecord"></a>
<a id="schema_@TicketRecord"></a>
<a id="tocS@ticketrecord"></a>
<a id="tocs@ticketrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "user_id": 0,
  "uc_id": "string",
  "type": "string",
  "download_ids": "string",
  "items": [
    "string"
  ],
  "amount": -1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|user_id|number|true|none|用户ID|none|
|uc_id|string|true|none|uc的ID|none|
|type|string|true|none|类型|none|
|download_ids|string|true|none|download_ids列表|none|
|items|[string]|true|none|items列表|none|
|amount|number|true|none|amount|enum: [-1, -7]|

#### 枚举值

|属性|值|
|---|---|
|amount|-1|
|amount|-7|

<h2 id="tocS_@TransactionRecord">@TransactionRecord</h2>

<a id="schema@transactionrecord"></a>
<a id="schema_@TransactionRecord"></a>
<a id="tocS@transactionrecord"></a>
<a id="tocs@transactionrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "spu_id": "string",
  "spu_name": "尊享会员",
  "sku_id": "string",
  "sku_name": "12个月",
  "status": "cancel",
  "amount": 0,
  "pay_through": "string",
  "order": "string",
  "user_id": 0,
  "qr_url": "string",
  "out_time": "string",
  "discount": 0,
  "discount_fee": 0,
  "resource_type": "string",
  "resource_id": "string",
  "resource_parent_id": "string",
  "notify_time": "string",
  "goods_id": "0",
  "goods_name": "资源下载",
  "items": "string",
  "app_params": {
    "prepay_id": "string"
  },
  "h5_url": "string",
  "out_trade_no": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|spu_id|string|false|none|spu的ID|none|
|spu_name|string|false|none|spu名称|enum: ["尊享会员", "Plus会员", "用户组卷下载", "专辑付费下载"]|
|sku_id|string|false|none|sku的ID|none|
|sku_name|string|false|none|sku名称|enum: ["12个月", "用户组卷下载", "专辑付费下载"]|
|status|string|true|none|状态|enum: ["cancel", "success"]|
|amount|number|true|none|amount|none|
|pay_through|string|true|none|pay_through|none|
|order|string|true|none|排序/订单|none|
|user_id|number|true|none|用户ID|none|
|qr_url|string|false|none|qr的URL|none|
|out_time|string|true|none|out时间|none|
|discount|number|true|none|discount|enum: [0, 80, 70]|
|discount_fee|number|true|none|discount_fee|enum: [0, 120, 180, 40, 60]|
|resource_type|string|false|none|资源类型|none|
|resource_id|string|false|none|资源ID|none|
|resource_parent_id|string|false|none|resource_parent的ID|none|
|notify_time|string|false|none|notify时间|none|
|goods_id|string|false|none|goods的ID|enum: ["0"]|
|goods_name|string|false|none|goods名称|enum: ["资源下载"]|
|items|string|false|none|items列表|none|
|app_params|object|false|none|app_params|none|
|» prepay_id|string|true|none|prepay的ID|none|
|h5_url|string|false|none|h5的URL|none|
|out_trade_no|string|false|none|out_trade_no|none|

#### 枚举值

|属性|值|
|---|---|
|spu_name|尊享会员|
|spu_name|Plus会员|
|spu_name|用户组卷下载|
|spu_name|专辑付费下载|
|sku_name|12个月|
|sku_name|用户组卷下载|
|sku_name|专辑付费下载|
|status|cancel|
|status|success|
|discount|0|
|discount|80|
|discount|70|
|discount_fee|0|
|discount_fee|120|
|discount_fee|180|
|discount_fee|40|
|discount_fee|60|
|goods_id|0|
|goods_name|资源下载|

<h2 id="tocS_@TrialRecord">@TrialRecord</h2>

<a id="schema@trialrecord"></a>
<a id="schema_@TrialRecord"></a>
<a id="tocS@trialrecord"></a>
<a id="tocs@trialrecord"></a>

```json
{
  "ctime": "string",
  "utime": "string",
  "status": "normal",
  "sch_id": 0,
  "sch_name": "string",
  "type": "shiyong",
  "vip_type": "string",
  "start_time": "string",
  "expired_time": "string",
  "operator": "string",
  "stime": "string",
  "etime": "string",
  "proposer": "张淑凤",
  "source": "xiangmu"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|status|string|true|none|状态|enum: ["normal"]|
|sch_id|number|true|none|学校ID|none|
|sch_name|string|true|none|sch名称|none|
|type|string|true|none|类型|enum: ["shiyong", "yuejuan"]|
|vip_type|string|true|none|会员类型|none|
|start_time|string|false|none|开始时间|none|
|expired_time|string|false|none|过期时间|none|
|operator|string|false|none|操作人|none|
|stime|string|false|none|开始时间|none|
|etime|string|false|none|结束时间|none|
|proposer|string|false|none|proposer|enum: ["张淑凤", "杨乃仟"]|
|source|string|false|none|来源|enum: ["xiangmu", "jzl"]|

#### 枚举值

|属性|值|
|---|---|
|status|normal|
|type|shiyong|
|type|yuejuan|
|proposer|张淑凤|
|proposer|杨乃仟|
|source|xiangmu|
|source|jzl|

<h2 id="tocS_access_spot">access_spot</h2>

<a id="schemaaccess_spot"></a>
<a id="schema_access_spot"></a>
<a id="tocSaccess_spot"></a>
<a id="tocsaccess_spot"></a>

```json
{
  "attachment": [
    "string"
  ],
  "user_id": 0,
  "role": "teacher",
  "school_id": 0,
  "province": "string",
  "city": "string",
  "ip": "string",
  "timestamp": "string",
  "event_id": "zc_tiku",
  "period": "高中",
  "subject": "英语"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|attachment|[string]|true|none|attachment列表|none|
|user_id|number|true|none|用户ID|none|
|role|string|false|none|角色|enum: ["teacher"]|
|school_id|number|true|none|学校的ID|none|
|province|string|true|none|省份|none|
|city|string|true|none|城市|none|
|ip|string|true|none|ip|none|
|timestamp|string|true|none|日期时间|none|
|event_id|string|true|none|event的ID|enum: ["zc_tiku", "dl_hfs", "dl_tiku"]|
|period|string|false|none|学段|enum: ["高中", "小学", "", "初中"]|
|subject|string|false|none|学科|enum: ["英语", "数学", "", "政治", "生物", "物理"]|

#### 枚举值

|属性|值|
|---|---|
|role|teacher|
|event_id|zc_tiku|
|event_id|dl_hfs|
|event_id|dl_tiku|
|period|高中|
|period|小学|
|period||
|period|初中|
|subject|英语|
|subject|数学|
|subject||
|subject|政治|
|subject|生物|
|subject|物理|

<h2 id="tocS_access_spot_describe">access_spot_describe</h2>

<a id="schemaaccess_spot_describe"></a>
<a id="schema_access_spot_describe"></a>
<a id="tocSaccess_spot_describe"></a>
<a id="tocsaccess_spot_describe"></a>

```json
{
  "type": "string",
  "type_id": "string",
  "event_describe": "组卷记录-删除",
  "event_id": "xzqr_B4",
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|string|true|none|类型|none|
|type_id|string|true|none|type的ID|none|
|event_describe|string|true|none|event_describe|enum: ["组卷记录-删除", "组卷记录-编辑", "组卷记录-下载", "组卷记录-预览", "试卷搜索次数", "试题搜索次数", "直接登录", "首页打开次数"]|
|event_id|string|true|none|event的ID|enum: ["xzqr_B4", "xzqr_A3", "xzqr_A4"]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|event_describe|组卷记录-删除|
|event_describe|组卷记录-编辑|
|event_describe|组卷记录-下载|
|event_describe|组卷记录-预览|
|event_describe|试卷搜索次数|
|event_describe|试题搜索次数|
|event_describe|直接登录|
|event_describe|首页打开次数|
|event_id|xzqr_B4|
|event_id|xzqr_A3|
|event_id|xzqr_A4|

<h2 id="tocS_access_spot_statistic">access_spot_statistic</h2>

<a id="schemaaccess_spot_statistic"></a>
<a id="schema_access_spot_statistic"></a>
<a id="tocSaccess_spot_statistic"></a>
<a id="tocsaccess_spot_statistic"></a>

```json
{
  "event_id": "xzqr_B4",
  "date_str": "string",
  "date_num": 0,
  "pv": 0,
  "uv": 0,
  "ip": 0,
  "click": 0,
  "click_user": 0,
  "vip_click": 0,
  "vip_click_user": 0,
  "timestamp": "string",
  "click_user_set": {},
  "vip_click_user_set": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|event_id|string|true|none|event的ID|enum: ["xzqr_B4", "xzqr_A4", "xzqr_A3", "pv", "xzqr_8K"]|
|date_str|string|true|none|日期|none|
|date_num|number|true|none|日期|none|
|pv|number|true|none|pv|none|
|uv|number|true|none|uv|none|
|ip|number|true|none|ip|none|
|click|number|true|none|click|none|
|click_user|number|true|none|click_user|none|
|vip_click|number|true|none|vip_click|enum: [0]|
|vip_click_user|number|true|none|vip_click_user|enum: [0]|
|timestamp|string|true|none|日期时间|none|
|click_user_set|object|true|none|click_user_set|none|
|vip_click_user_set|object|true|none|vip_click_user_set|none|

#### 枚举值

|属性|值|
|---|---|
|event_id|xzqr_B4|
|event_id|xzqr_A4|
|event_id|xzqr_A3|
|event_id|pv|
|event_id|xzqr_8K|
|vip_click|0|
|vip_click_user|0|

<h2 id="tocS_access_spot_v2">access_spot_v2</h2>

<a id="schemaaccess_spot_v2"></a>
<a id="schema_access_spot_v2"></a>
<a id="tocSaccess_spot_v2"></a>
<a id="tocsaccess_spot_v2"></a>

```json
{
  "period": "初中",
  "subject": "string",
  "fingerprint": "string",
  "event_id": "pv",
  "web_route": "/live",
  "web_from_route": "/",
  "duration": 0,
  "province": "string",
  "user_id": 0,
  "user_name": "string",
  "school_id": 0,
  "school_name": "string",
  "role": "teacher",
  "isVip": null,
  "vipType": null,
  "timestamp": "string",
  "origin": "jsd_app",
  "web_route_name": "string",
  "ip": "string",
  "web_from_route_name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|true|none|学科|none|
|fingerprint|string|true|none|fingerprint|none|
|event_id|string|true|none|event的ID|enum: ["pv", "xzqr_B4", "xzqr_A4", "xzqr_A3"]|
|web_route|string|true|none|web_route|enum: ["/live", "/topic", "/album"]|
|web_from_route|string|true|none|web_from_route|enum: ["/", "/live", "/album"]|
|duration|number|true|none|持续时间（分钟）|none|
|province|string|true|none|省份|none|
|user_id|number|true|none|用户ID|none|
|user_name|string|true|none|用户名称|none|
|school_id|number|true|none|学校的ID|none|
|school_name|string|true|none|school名称|none|
|role|string|true|none|角色|enum: ["teacher", "教师"]|
|isVip|null|true|none|isVip|none|
|vipType|null|true|none|vipType|none|
|timestamp|string|true|none|日期时间|none|
|origin|string|true|none|origin|enum: ["jsd_app", "hfs"]|
|web_route_name|string|false|none|web_route名称|none|
|ip|string|true|none|ip|none|
|web_from_route_name|string|false|none|web_from_route名称|none|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|
|event_id|pv|
|event_id|xzqr_B4|
|event_id|xzqr_A4|
|event_id|xzqr_A3|
|web_route|/live|
|web_route|/topic|
|web_route|/album|
|web_from_route|/|
|web_from_route|/live|
|web_from_route|/album|
|role|teacher|
|role|教师|
|origin|jsd_app|
|origin|hfs|

<h2 id="tocS_album">album</h2>

<a id="schemaalbum"></a>
<a id="schema_album"></a>
<a id="tocSalbum"></a>
<a id="tocsalbum"></a>

```json
{
  "name": "衡水年高考模拟",
  "type": "高考模拟",
  "period": "高中",
  "subject": "生物",
  "is_elite": true,
  "groups": [
    {
      "name": "string",
      "exampapers": [
        {
          "id": 0
        }
      ]
    }
  ],
  "ctime": "string",
  "is_del": false,
  "view_times": 0,
  "school_id": 1,
  "user_id": 2892215089430528,
  "user_name": "曾晨光"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["衡水年高考模拟", "全国Ⅲ高考模拟", "全国Ⅱ高考模拟", "全国Ⅰ高考模拟"]|
|type|string|true|none|类型|enum: ["高考模拟", "中考真卷", "高考真卷"]|
|period|string|true|none|学段|enum: ["高中", "初中"]|
|subject|string|true|none|学科|enum: ["生物", "英语", "历史", "语文", "数学", "政治", "物理", "化学", "地理", "2022"]|
|is_elite|boolean|true|none|is_elite|enum: [true]|
|groups|[object]|true|none|groups列表|none|
|» name|string|true|none|名称|none|
|» exampapers|[object]|true|none|exampapers列表|none|
|»» id|number|true|none|id|none|
|ctime|string|true|none|创建时间|none|
|is_del|boolean|true|none|is_del|enum: [false]|
|view_times|number|true|none|view_times|none|
|school_id|number|false|none|学校的ID|enum: [1, 25]|
|user_id|number|false|none|用户ID|enum: [2892215089430528, 7018]|
|user_name|string|false|none|用户名称|enum: ["曾晨光", "罗明英"]|

#### 枚举值

|属性|值|
|---|---|
|name|衡水年高考模拟|
|name|全国Ⅲ高考模拟|
|name|全国Ⅱ高考模拟|
|name|全国Ⅰ高考模拟|
|type|高考模拟|
|type|中考真卷|
|type|高考真卷|
|period|高中|
|period|初中|
|subject|生物|
|subject|英语|
|subject|历史|
|subject|语文|
|subject|数学|
|subject|政治|
|subject|物理|
|subject|化学|
|subject|地理|
|subject|2022|
|is_elite|true|
|is_del|false|
|school_id|1|
|school_id|25|
|user_id|2892215089430528|
|user_id|7018|
|user_name|曾晨光|
|user_name|罗明英|

<h2 id="tocS_banners">banners</h2>

<a id="schemabanners"></a>
<a id="schema_banners"></a>
<a id="tocSbanners"></a>
<a id="tocsbanners"></a>

```json
{
  "name": "string",
  "img_name": "教师节.jpg",
  "img_url": "string",
  "href": "无",
  "target": "_self",
  "interval_time": 3,
  "valid_scope": "all",
  "desc": "string",
  "size": "banner",
  "limit_school": [
    "string"
  ],
  "limit_period": "string",
  "limit_subject": "string",
  "valid_from": "string",
  "valid_to": "string",
  "shelf_status": 2,
  "del_status": 0,
  "ctime": "string",
  "utime": "string",
  "create_user": {
    "id": "string",
    "name": "赵原"
  },
  "update_user": {
    "id": "string",
    "name": "admin"
  },
  "sort_num": 0,
  "del_user": {
    "id": "string",
    "name": "赵原"
  },
  "dtime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|none|
|img_name|string|true|none|img名称|enum: ["教师节.jpg", "zk.png", "qm.jpg", "gk.jpg"]|
|img_url|string|true|none|img的URL|none|
|href|string|true|none|href|enum: ["无"]|
|target|string|true|none|目标/跳转目标|enum: ["_self", "_blank"]|
|interval_time|number|true|none|interval时间|enum: [3, 2, 5, 10, 20]|
|valid_scope|string|true|none|valid_scope|enum: ["all"]|
|desc|string|true|none|desc|none|
|size|string|true|none|大小|enum: ["banner", "notice", "large", "small"]|
|limit_school|[string]|true|none|limit_school列表|none|
|limit_period|string|true|none|limit_period列表|none|
|limit_subject|string|true|none|limit_subject列表|none|
|valid_from|string|true|none|日期时间|none|
|valid_to|string|true|none|日期时间|none|
|shelf_status|number|true|none|shelf_status|enum: [2, 3, 1]|
|del_status|number|true|none|del_status|enum: [0, 1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|create_user|object|true|none|create_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["赵原", "董素霞", "admin", "陈健"]|
|update_user|object|true|none|日期|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["admin", "赵原", "董素霞"]|
|sort_num|number|true|none|sort_num|none|
|del_user|object|false|none|del_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["赵原", "陈健", "董素霞", "admin"]|
|dtime|string|false|none|日期时间|none|

#### 枚举值

|属性|值|
|---|---|
|img_name|教师节.jpg|
|img_name|zk.png|
|img_name|qm.jpg|
|img_name|gk.jpg|
|href|无|
|target|_self|
|target|_blank|
|interval_time|3|
|interval_time|2|
|interval_time|5|
|interval_time|10|
|interval_time|20|
|valid_scope|all|
|size|banner|
|size|notice|
|size|large|
|size|small|
|shelf_status|2|
|shelf_status|3|
|shelf_status|1|
|del_status|0|
|del_status|1|
|name|赵原|
|name|董素霞|
|name|admin|
|name|陈健|
|name|admin|
|name|赵原|
|name|董素霞|
|name|赵原|
|name|陈健|
|name|董素霞|
|name|admin|

<h2 id="tocS_basket_question">basket_question</h2>

<a id="schemabasket_question"></a>
<a id="schema_basket_question"></a>
<a id="tocSbasket_question"></a>
<a id="tocsbasket_question"></a>

```json
{
  "id": 0,
  "type": "string",
  "period": "高中",
  "subject": "string",
  "user_id": 0,
  "ctime": "string",
  "utime": "string",
  "score": 0,
  "exampaper_type": "同步练习"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|id|none|
|type|string|false|none|类型|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学", "中职"]|
|subject|string|true|none|学科|none|
|user_id|number|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|score|number|false|none|分数|none|
|exampaper_type|string|false|none|exampaper_type|enum: ["同步练习", "期中", "期末", "月考", "高考模拟", "中考模拟", "其他"]|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小学|
|period|中职|
|exampaper_type|同步练习|
|exampaper_type|期中|
|exampaper_type|期末|
|exampaper_type|月考|
|exampaper_type|高考模拟|
|exampaper_type|中考模拟|
|exampaper_type|其他|

<h2 id="tocS_coupons">coupons</h2>

<a id="schemacoupons"></a>
<a id="schema_coupons"></a>
<a id="tocScoupons"></a>
<a id="tocscoupons"></a>

```json
{
  "name": "0504测试",
  "discount_type": 1,
  "discount_count": 3000,
  "target_goods": [
    {
      "id": "string",
      "name": "题库年度会员"
    }
  ],
  "valid_type": 1,
  "valid_from": "string",
  "valid_to": "string",
  "send_type": 1,
  "send_rules": [
    {
      "key": "schools",
      "key_desc": "限定学校",
      "val_type": "number",
      "operator": {
        "key": "包含",
        "val": "in"
      },
      "val": [
        {
          "key": "爱云校广州04",
          "val": "55553"
        }
      ]
    }
  ],
  "send_from": "string",
  "send_to": "string",
  "desc": "234",
  "shelf_status": 2,
  "del_status": 0,
  "send_count": 1,
  "usage_count": 0,
  "ctime": "string",
  "utime": "string",
  "create_user": {
    "id": "string",
    "name": "admin"
  },
  "update_user": {
    "id": "string",
    "name": "admin"
  },
  "valid_day": 10
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["0504测试", "测试11", "测试1"]|
|discount_type|number|true|none|discount_type|enum: [1]|
|discount_count|number|true|none|discount数量|enum: [3000, 5000, 10000, 9900]|
|target_goods|[object]|true|none|target_goods列表|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["题库年度会员"]|
|valid_type|number|true|none|valid_type|enum: [1, 2]|
|valid_from|string|false|none|日期时间|none|
|valid_to|string|false|none|日期时间|none|
|send_type|number|true|none|send_type|enum: [1, 2]|
|send_rules|[object]|true|none|send_rules列表|none|
|» key|string|true|none|键/标识符|enum: ["schools"]|
|» key_desc|string|true|none|key_desc|enum: ["限定学校"]|
|» val_type|string|true|none|val_type|enum: ["number"]|
|» operator|object|true|none|操作人|none|
|»» key|string|true|none|键/标识符|enum: ["包含"]|
|»» val|string|true|none|val|enum: ["in"]|
|» val|[object]|true|none|val列表|none|
|»» key|string|true|none|键/标识符|enum: ["爱云校广州04", "云校教研学校"]|
|»» val|string|true|none|val|enum: ["55553", "11732"]|
|send_from|string|true|none|日期时间|none|
|send_to|string|true|none|日期时间|none|
|desc|string|true|none|desc|enum: ["234", "未覆盖", "121", "11", "赠送年度优惠券"]|
|shelf_status|number|true|none|shelf_status|enum: [2, 3]|
|del_status|number|true|none|del_status|enum: [0]|
|send_count|number|true|none|send数量|enum: [1, 0]|
|usage_count|number|true|none|usage数量|enum: [0]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|create_user|object|true|none|create_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["admin"]|
|update_user|object|true|none|日期|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["admin"]|
|valid_day|number|false|none|valid_day|enum: [10, 30]|

#### 枚举值

|属性|值|
|---|---|
|name|0504测试|
|name|测试11|
|name|测试1|
|discount_type|1|
|discount_count|3000|
|discount_count|5000|
|discount_count|10000|
|discount_count|9900|
|name|题库年度会员|
|valid_type|1|
|valid_type|2|
|send_type|1|
|send_type|2|
|key|schools|
|key_desc|限定学校|
|val_type|number|
|key|包含|
|val|in|
|key|爱云校广州04|
|key|云校教研学校|
|val|55553|
|val|11732|
|desc|234|
|desc|未覆盖|
|desc|121|
|desc|11|
|desc|赠送年度优惠券|
|shelf_status|2|
|shelf_status|3|
|del_status|0|
|send_count|1|
|send_count|0|
|usage_count|0|
|name|admin|
|name|admin|
|valid_day|10|
|valid_day|30|

<h2 id="tocS_exampaper_draft">exampaper_draft</h2>

<a id="schemaexampaper_draft"></a>
<a id="schema_exampaper_draft"></a>
<a id="tocSexampaper_draft"></a>
<a id="tocsexampaper_draft"></a>

```json
{
  "period": "初中",
  "subject": "数学",
  "name": "string",
  "subtitle": "",
  "score": 0,
  "duration": 120,
  "paper_info": "string",
  "cand_info": "string",
  "attentions": "string",
  "secret_tag": "绝密★启用前",
  "gutter": 0,
  "template": "string",
  "partsList": [
    "string"
  ],
  "volumes": [
    {
      "title": "卷I（选择题）",
      "note": "",
      "blocks": [
        {
          "title": "string",
          "note": "",
          "type": "选择题",
          "default_score": 3,
          "questions": [
            {}
          ]
        }
      ]
    }
  ],
  "user_id": 0,
  "sch_id": 0,
  "sch_name": "string",
  "province": "string",
  "city": "string",
  "ctime": "string",
  "utime": "string",
  "type": "考后巩固",
  "display": 1,
  "view_count": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|true|none|学科|enum: ["数学", "政治", "语文", "化学", "物理", "英语", "历史"]|
|name|string|true|none|名称|none|
|subtitle|string|true|none|subtitle|enum: [""]|
|score|number|true|none|分数|none|
|duration|number|true|none|持续时间（分钟）|enum: [120]|
|paper_info|string|true|none|paper_info|none|
|cand_info|string|true|none|cand_info|none|
|attentions|string|true|none|attentions|none|
|secret_tag|string|true|none|secret_tag|enum: ["绝密★启用前"]|
|gutter|number|true|none|gutter|enum: [0]|
|template|string|true|none|模板|none|
|partsList|[string]|true|none|partsList列表|none|
|volumes|[object]|true|none|volumes列表|none|
|» title|string|true|none|标题|enum: ["卷I（选择题）"]|
|» note|string|true|none|note|enum: [""]|
|» blocks|[object]|true|none|blocks列表|none|
|»» title|string|true|none|标题|none|
|»» note|string|true|none|note|enum: [""]|
|»» type|string|true|none|类型|enum: ["选择题", "填空题", "解答题", "材料分析题"]|
|»» default_score|number|true|none|default_score|enum: [3, 10, 15]|
|»» questions|[object]|true|none|试题列表|none|
|»»» id|number|true|none|id|none|
|»»» period|string|true|none|学段|enum: ["初中"]|
|»»» subject|string|true|none|学科|enum: ["数学"]|
|»»» score|number|true|none|分数|enum: [3, 10]|
|user_id|number|true|none|用户ID|none|
|sch_id|number|true|none|学校ID|none|
|sch_name|string|true|none|sch名称|none|
|province|string|true|none|省份|none|
|city|string|true|none|城市|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|type|string|true|none|类型|enum: ["考后巩固", "周考试卷", "月考试卷", "期中试卷", "期末试卷"]|
|display|number|true|none|display|enum: [1]|
|view_count|number|false|none|浏览次数|enum: [1, 2, 4, 5]|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|
|subject|数学|
|subject|政治|
|subject|语文|
|subject|化学|
|subject|物理|
|subject|英语|
|subject|历史|
|subtitle||
|duration|120|
|secret_tag|绝密★启用前|
|gutter|0|
|title|卷I（选择题）|
|note||
|note||
|type|选择题|
|type|填空题|
|type|解答题|
|type|材料分析题|
|default_score|3|
|default_score|10|
|default_score|15|
|period|初中|
|subject|数学|
|score|3|
|score|10|
|type|考后巩固|
|type|周考试卷|
|type|月考试卷|
|type|期中试卷|
|type|期末试卷|
|display|1|
|view_count|1|
|view_count|2|
|view_count|4|
|view_count|5|

<h2 id="tocS_exam_area">exam_area</h2>

<a id="schemaexam_area"></a>
<a id="schema_exam_area"></a>
<a id="tocSexam_area"></a>
<a id="tocsexam_area"></a>

```json
{
  "name": "全部地区",
  "period": "小学",
  "subjects": "string",
  "regions": [
    "string"
  ],
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["全部地区", "自主命题", "新高考Ⅱ卷", "新高考Ⅰ卷", "全国乙卷", "全国甲卷"]|
|period|string|true|none|学段|enum: ["小学", "初中", "高中"]|
|subjects|string|true|none|subjects列表|none|
|regions|[string]|true|none|regions列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|全部地区|
|name|自主命题|
|name|新高考Ⅱ卷|
|name|新高考Ⅰ卷|
|name|全国乙卷|
|name|全国甲卷|
|period|小学|
|period|初中|
|period|高中|

<h2 id="tocS_exam_teacher_paper">exam_teacher_paper</h2>

<a id="schemaexam_teacher_paper"></a>
<a id="schema_exam_teacher_paper"></a>
<a id="tocSexam_teacher_paper"></a>
<a id="tocsexam_teacher_paper"></a>

```json
{
  "user_id": 0,
  "exam_id": 2387190,
  "paper_id": "string",
  "grade": "string",
  "period": "高中",
  "subject": "string",
  "papers": "string",
  "class_papers": "string",
  "ctime": "string",
  "utime": "string",
  "questions": "string",
  "table_id": "string",
  "user_paper_id": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|exam_id|string|true|none|考试的ID|enum: [2387190]|
|paper_id|string|true|none|试卷ID|none|
|grade|string|true|none|年级|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|subject|string|true|none|学科|none|
|papers|string|true|none|papers列表|none|
|class_papers|string|true|none|class_papers列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|questions|string|false|none|试题列表|none|
|table_id|string|false|none|table的ID|none|
|user_paper_id|string|false|none|user_paper的ID|none|

#### 枚举值

|属性|值|
|---|---|
|exam_id|2387190|
|period|高中|
|period|初中|
|period|小学|

<h2 id="tocS_favorite">favorite</h2>

<a id="schemafavorite"></a>
<a id="schema_favorite"></a>
<a id="tocSfavorite"></a>
<a id="tocsfavorite"></a>

```json
{
  "user_id": 0,
  "questions": "string",
  "exampapers": "string",
  "edu_files": "string",
  "micro_courses": "string",
  "tw_specifications": "string",
  "edu_tools": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|questions|string|false|none|试题列表|none|
|exampapers|string|false|none|exampapers列表|none|
|edu_files|string|false|none|edu_files列表|none|
|micro_courses|string|false|none|micro_courses列表|none|
|tw_specifications|string|false|none|tw_specifications列表|none|
|edu_tools|string|false|none|edu_tools列表|none|

<h2 id="tocS_feedback">feedback</h2>

<a id="schemafeedback"></a>
<a id="schema_feedback"></a>
<a id="tocSfeedback"></a>
<a id="tocsfeedback"></a>

```json
{
  "user_id": 0,
  "name": "string",
  "phone": "未知",
  "is_vip": false,
  "content": "string",
  "images": "string",
  "status": 0,
  "ctime": "string",
  "utime": "string",
  "read_status": 2,
  "handle_record": [
    {
      "time": 0,
      "user": {
        "id": "string",
        "name": "admin"
      },
      "status": 1,
      "content": "   "
    }
  ],
  "handle_time": "string",
  "handle_user": {
    "id": "string",
    "name": "admin"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|name|string|true|none|名称|none|
|phone|string|true|none|phone|enum: ["未知"]|
|is_vip|boolean|true|none|是否会员|enum: [false, true]|
|content|string|true|none|内容|none|
|images|string|true|none|图片|none|
|status|number|true|none|状态|enum: [0, 1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|read_status|number|true|none|read_status|enum: [2, 1]|
|handle_record|[object]|false|none|handle_record列表|none|
|» time|number|true|none|time|none|
|» user|object|true|none|user|none|
|»» id|string|true|none|id|none|
|»» name|string|true|none|名称|enum: ["admin", "赵原"]|
|» status|number|true|none|状态|enum: [1]|
|» content|string|true|none|内容|enum: ["   "]|
|handle_time|string|false|none|handle时间|none|
|handle_user|object|false|none|handle_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["admin", "赵原"]|

#### 枚举值

|属性|值|
|---|---|
|phone|未知|
|is_vip|false|
|is_vip|true|
|status|0|
|status|1|
|read_status|2|
|read_status|1|
|name|admin|
|name|赵原|
|status|1|
|content|   |
|name|admin|
|name|赵原|

<h2 id="tocS_logger">logger</h2>

<a id="schemalogger"></a>
<a id="schema_logger"></a>
<a id="tocSlogger"></a>
<a id="tocslogger"></a>

```json
{
  "user_id": 0,
  "name": "string",
  "sch_id": 16633,
  "time": "string",
  "method": "PUT",
  "body": {},
  "url": "string",
  "path": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|name|string|true|none|名称|none|
|sch_id|number|true|none|学校ID|enum: [16633]|
|time|string|true|none|日期时间|none|
|method|string|true|none|method|enum: ["PUT", "POST", "DELETE"]|
|body|object|true|none|body|none|
|url|string|true|none|资源地址|none|
|path|string|true|none|路径|none|

#### 枚举值

|属性|值|
|---|---|
|sch_id|16633|
|method|PUT|
|method|POST|
|method|DELETE|

<h2 id="tocS_member">member</h2>

<a id="schemamember"></a>
<a id="schema_member"></a>
<a id="tocSmember"></a>
<a id="tocsmember"></a>

```json
{
  "level_name": "济宁市实验中学",
  "que_download_num": {
    "day": 250,
    "month": 5000
  },
  "exampaper_download_num": {
    "day": 100,
    "month": 2000
  },
  "intelligence_volume_num": {
    "day": 25,
    "month": 250
  },
  "specification_volume_num": {
    "day": 25,
    "month": 250
  },
  "volume_exampaper_num": {
    "question": 30
  },
  "que_details_num": {
    "day": 50,
    "month": 1000
  },
  "exampaper_details_num": {
    "day": 25,
    "month": 500
  },
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|level_name|string|true|none|level名称|enum: ["济宁市实验中学", "终止合作校V1", "付费V1", "虚拟V1", "体验V1"]|
|que_download_num|object|true|none|que_download_num|none|
|» day|number|true|none|day|enum: [250, 5, 500, 5000, 100]|
|» month|number|true|none|月份|enum: [5000, 5, 10000, 100000, 2000]|
|exampaper_download_num|object|true|none|exampaper_download_num|none|
|» day|number|true|none|day|enum: [100, 5, 200, 2000, 40]|
|» month|number|true|none|月份|enum: [2000, 5, 4000, 40000, 800]|
|intelligence_volume_num|object|true|none|intelligence_volume_num|none|
|» day|number|true|none|day|enum: [25, 5, 50, 500, 10]|
|» month|number|true|none|月份|enum: [250, 5, 500, 5000, 100]|
|specification_volume_num|object|true|none|specification_volume_num|none|
|» day|number|true|none|day|enum: [25, 5, 50, 500, 10]|
|» month|number|true|none|月份|enum: [250, 5, 500, 5000, 100]|
|volume_exampaper_num|object|true|none|volume_exampaper_num|none|
|» question|number|true|none|question|enum: [30, 5, 50, 200]|
|que_details_num|object|true|none|que_details_num|none|
|» day|number|true|none|day|enum: [50, 5, 500, 1000, 100]|
|» month|number|true|none|月份|enum: [1000, 5, 20000]|
|exampaper_details_num|object|true|none|exampaper_details_num|none|
|» day|number|true|none|day|enum: [25, 5, 50, 500]|
|» month|number|true|none|月份|enum: [500, 5, 1000, 10000]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|level_name|济宁市实验中学|
|level_name|终止合作校V1|
|level_name|付费V1|
|level_name|虚拟V1|
|level_name|体验V1|
|day|250|
|day|5|
|day|500|
|day|5000|
|day|100|
|month|5000|
|month|5|
|month|10000|
|month|100000|
|month|2000|
|day|100|
|day|5|
|day|200|
|day|2000|
|day|40|
|month|2000|
|month|5|
|month|4000|
|month|40000|
|month|800|
|day|25|
|day|5|
|day|50|
|day|500|
|day|10|
|month|250|
|month|5|
|month|500|
|month|5000|
|month|100|
|day|25|
|day|5|
|day|50|
|day|500|
|day|10|
|month|250|
|month|5|
|month|500|
|month|5000|
|month|100|
|question|30|
|question|5|
|question|50|
|question|200|
|day|50|
|day|5|
|day|500|
|day|1000|
|day|100|
|month|1000|
|month|5|
|month|20000|
|day|25|
|day|5|
|day|50|
|day|500|
|month|500|
|month|5|
|month|1000|
|month|10000|

<h2 id="tocS_notice_message">notice_message</h2>

<a id="schemanotice_message"></a>
<a id="schema_notice_message"></a>
<a id="tocSnotice_message"></a>
<a id="tocsnotice_message"></a>

```json
{
  "type": 10,
  "content": "string",
  "deleted": 0,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|number|true|none|类型|enum: [10]|
|content|string|true|none|内容|none|
|deleted|number|true|none|删除状态|enum: [0]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|type|10|
|deleted|0|

<h2 id="tocS_pms_sku">pms_sku</h2>

<a id="schemapms_sku"></a>
<a id="schema_pms_sku"></a>
<a id="tocSpms_sku"></a>
<a id="tocspms_sku"></a>

```json
{
  "name": "专辑付费下载",
  "key": "string",
  "status": 1,
  "tag": 0,
  "original_price": 600,
  "discount_price": 0,
  "final_price": 600,
  "desc": "",
  "specs": [
    {
      "name": "专辑付费下载",
      "key": "time",
      "option": {
        "name": "专辑付费下载",
        "value": "12"
      }
    }
  ],
  "spu_id": "string",
  "rights": [
    {
      "type": "string",
      "reset_type": "fixed",
      "limit": 1,
      "value": 0,
      "subject_check": 0
    }
  ],
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["专辑付费下载", "用户组卷下载", "12个月"]|
|key|string|true|none|键/标识符|none|
|status|number|true|none|状态|enum: [1, 0]|
|tag|number|true|none|tag|enum: [0, 1]|
|original_price|number|true|none|original_price|enum: [600, 200, 4900, 39900, 25900, 15900, 7900]|
|discount_price|number|true|none|discount_price|enum: [0]|
|final_price|number|true|none|final_price|enum: [600, 200, 4900, 39900, 25900, 15900, 5900]|
|desc|string|true|none|desc|enum: ["", "每月仅需43元", "每月只需53元"]|
|specs|[object]|true|none|specs列表|none|
|» name|string|true|none|名称|enum: ["专辑付费下载", "资源类型", "时间", "学段", "科目"]|
|» key|string|true|none|键/标识符|enum: ["time", "period", "subject"]|
|» option|object|true|none|option|none|
|»» name|string|true|none|名称|enum: ["专辑付费下载", "12个月", "高中", "地理", "6个月", "3个月", "1个月", "历史"]|
|»» value|string|true|none|值|enum: ["12", "高中", "地理", "6", "3", "1", "历史"]|
|spu_id|string|true|none|spu的ID|none|
|rights|[object]|true|none|rights列表|none|
|» type|string|true|none|类型|none|
|» reset_type|string|true|none|reset_type|enum: ["fixed", "month", "day"]|
|» limit|number|true|none|limit|enum: [1, -1, 2, 60, 5, 1000, 20, 100]|
|» value|number|true|none|值|enum: [0, 80]|
|» subject_check|number|true|none|subject_check|enum: [0, 1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|专辑付费下载|
|name|用户组卷下载|
|name|12个月|
|status|1|
|status|0|
|tag|0|
|tag|1|
|original_price|600|
|original_price|200|
|original_price|4900|
|original_price|39900|
|original_price|25900|
|original_price|15900|
|original_price|7900|
|discount_price|0|
|final_price|600|
|final_price|200|
|final_price|4900|
|final_price|39900|
|final_price|25900|
|final_price|15900|
|final_price|5900|
|desc||
|desc|每月仅需43元|
|desc|每月只需53元|
|name|专辑付费下载|
|name|资源类型|
|name|时间|
|name|学段|
|name|科目|
|key|time|
|key|period|
|key|subject|
|name|专辑付费下载|
|name|12个月|
|name|高中|
|name|地理|
|name|6个月|
|name|3个月|
|name|1个月|
|name|历史|
|value|12|
|value|高中|
|value|地理|
|value|6|
|value|3|
|value|1|
|value|历史|
|reset_type|fixed|
|reset_type|month|
|reset_type|day|
|limit|1|
|limit|-1|
|limit|2|
|limit|60|
|limit|5|
|limit|1000|
|limit|20|
|limit|100|
|value|0|
|value|80|
|subject_check|0|
|subject_check|1|

<h2 id="tocS_pms_spu">pms_spu</h2>

<a id="schemapms_spu"></a>
<a id="schema_pms_spu"></a>
<a id="tocSpms_spu"></a>
<a id="tocspms_spu"></a>

```json
{
  "name": "专辑付费下载",
  "code": "string",
  "pub_specs": [
    "string"
  ],
  "prv_specs": [
    {
      "name": "专辑付费下载",
      "key": "time",
      "options": [
        {
          "name": "string",
          "value": "string"
        }
      ]
    }
  ],
  "status": 1,
  "deleted": 0,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["专辑付费下载", "用户组卷下载", "Plus会员", "尊享会员"]|
|code|string|true|none|编码|none|
|pub_specs|[string]|true|none|pub_specs列表|none|
|prv_specs|[object]|true|none|prv_specs列表|none|
|» name|string|true|none|名称|enum: ["专辑付费下载", "用户组卷下载", "时长", "学段", "科目"]|
|» key|string|true|none|键/标识符|enum: ["time", "period", "subject"]|
|» options|[object]|true|none|options列表|none|
|»» name|string|true|none|名称|none|
|»» value|string|true|none|值|none|
|status|number|true|none|状态|enum: [1]|
|deleted|number|true|none|删除状态|enum: [0]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|专辑付费下载|
|name|用户组卷下载|
|name|Plus会员|
|name|尊享会员|
|name|专辑付费下载|
|name|用户组卷下载|
|name|时长|
|name|学段|
|name|科目|
|key|time|
|key|period|
|key|subject|
|status|1|
|deleted|0|

<h2 id="tocS_question_num">question_num</h2>

<a id="schemaquestion_num"></a>
<a id="schema_question_num"></a>
<a id="tocSquestion_num"></a>
<a id="tocsquestion_num"></a>

```json
{
  "questions": {
    "选择题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 1
    },
    "填空题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "9": 1
    },
    "解答题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "多选题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "实验探究题": {
      "1": 0,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "全部": {
      "1": 1,
      "4": 1,
      "5": 1
    },
    "材料分析题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "作图题": {
      "1": 1,
      "2": 5,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "选择填空题": {
      "1": 4,
      "2": 1,
      "3": 1,
      "4": 1,
      "5": 1
    },
    "判断题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "连线题": {
      "1": 7,
      "2": 7,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "读图分析题": {
      "3": 1,
      "4": 1,
      "5": 0
    },
    "综合读写": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "写作": {
      "1": 0,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "单选题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "现代文阅读": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "默写题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "古诗词鉴赏": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "文言文阅读": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "文言文翻译": {
      "1": 1,
      "2": 1,
      "3": 3,
      "4": 0,
      "5": 0
    },
    "短文改错": {
      "1": 4,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "完形填空": {
      "1": 0,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "语法填空": {
      "1": 0,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "改错题": {
      "1": 1,
      "2": 1,
      "3": 1,
      "4": 2,
      "5": 0
    },
    "阅读理解": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "句型转换": {
      "2": 5
    },
    "书面表达": {
      "1": 0,
      "2": 2,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "名著阅读": {
      "1": 1,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "听力题": {
      "1": 1,
      "3": 1
    },
    "七选五": {
      "1": 1,
      "2": 2,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "翻译": {
      "5": 1
    },
    "简答题": {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "10": 1
    },
    "辨析题": {
      "1": 0,
      "2": 1,
      "3": 0,
      "4": 0,
      "5": 0
    },
    "论述题": {
      "1": 1,
      "2": 1,
      "3": 1,
      "4": 1,
      "5": 0
    },
    "问答题": {
      "3": 1
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questions|object|true|none|试题列表|none|
|» 选择题|object|false|none|选择题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|»» 6|number|false|none|6|enum: [1]|
|» 填空题|object|false|none|填空题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|»» 9|number|false|none|9|enum: [1]|
|» 解答题|object|false|none|解答题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 多选题|object|false|none|多选题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 实验探究题|object|false|none|实验探究题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [1, 7, 2, 3, 4, 5, 10, 6, 8, 14]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 全部|object|false|none|全部|none|
|»» 1|number|false|none|1|enum: [1]|
|»» 4|number|false|none|4|enum: [1]|
|»» 5|number|false|none|5|enum: [1]|
|» 材料分析题|object|false|none|材料分析题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 作图题|object|false|none|作图题|none|
|»» 1|number|false|none|1|enum: [1, 4, 3, 2, 15, 8, 5, 6]|
|»» 2|number|false|none|2|enum: [5, 2, 4, 12, 1, 3]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 选择填空题|object|false|none|选择填空题|none|
|»» 1|number|false|none|1|enum: [4, 1, 2]|
|»» 2|number|false|none|2|enum: [1]|
|»» 3|number|false|none|3|enum: [1, 3, 2, 4, 7, 6]|
|»» 4|number|false|none|4|enum: [1, 2, 3, 4]|
|»» 5|number|false|none|5|enum: [1, 2, 3, 6, 5, 4, 7, 8]|
|» 判断题|object|false|none|判断题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 连线题|object|false|none|连线题|none|
|»» 1|number|false|none|1|enum: [7, 1, 2, 9, 5, 4, 6, 8]|
|»» 2|number|false|none|2|enum: [7, 1, 2, 3, 4, 5]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 读图分析题|object|false|none|读图分析题|none|
|»» 3|number|false|none|3|enum: [1]|
|»» 4|number|false|none|4|enum: [1]|
|»» 5|number|false|none|5|none|
|» 综合读写|object|false|none|综合读写|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 写作|object|false|none|写作|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [1, 2, 21]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 单选题|object|false|none|单选题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 现代文阅读|object|false|none|现代文阅读|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 默写题|object|false|none|默写题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 古诗词鉴赏|object|false|none|古诗词鉴赏|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 文言文阅读|object|false|none|文言文阅读|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 文言文翻译|object|false|none|文言文翻译|none|
|»» 1|number|false|none|1|enum: [1, 2, 92, 33, 3, 6, 4]|
|»» 2|number|false|none|2|enum: [1, 4]|
|»» 3|number|false|none|3|enum: [3, 2, 1, 4, 240, 225]|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 短文改错|object|false|none|短文改错|none|
|»» 1|number|false|none|1|enum: [4, 1, 2, 3, 5]|
|»» 2|number|false|none|2|enum: [1]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 完形填空|object|false|none|完形填空|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [1, 2, 5, 4, 7, 3, 8]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 语法填空|object|false|none|语法填空|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [1, 2, 4, 5, 6, 3]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 改错题|object|false|none|改错题|none|
|»» 1|number|false|none|1|enum: [1, 3]|
|»» 2|number|false|none|2|enum: [1, 2]|
|»» 3|number|false|none|3|enum: [1, 3, 2, 1027, 4]|
|»» 4|number|false|none|4|enum: [2, 1, 6]|
|»» 5|number|false|none|5|none|
|» 阅读理解|object|false|none|阅读理解|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 句型转换|object|false|none|句型转换|none|
|»» 2|number|true|none|2|enum: [5]|
|» 书面表达|object|false|none|书面表达|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [2, 1]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 名著阅读|object|false|none|名著阅读|none|
|»» 1|number|false|none|1|enum: [1, 10, 11, 3, 12, 2]|
|»» 2|number|false|none|2|enum: [1, 4, 7, 9, 6, 2]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 听力题|object|false|none|听力题|none|
|»» 1|number|false|none|1|enum: [1]|
|»» 3|number|false|none|3|enum: [1]|
|» 七选五|object|false|none|七选五|none|
|»» 1|number|false|none|1|enum: [1, 16, 2, 5, 4, 3, 6]|
|»» 2|number|false|none|2|enum: [2, 1, 3]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 翻译|object|false|none|翻译|none|
|»» 5|number|true|none|5|enum: [1]|
|» 简答题|object|false|none|简答题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|none|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|»» 10|number|false|none|10|enum: [1]|
|» 辨析题|object|false|none|辨析题|none|
|»» 1|number|false|none|1|none|
|»» 2|number|false|none|2|enum: [1, 3, 4, 2]|
|»» 3|number|false|none|3|none|
|»» 4|number|false|none|4|none|
|»» 5|number|false|none|5|none|
|» 论述题|object|false|none|论述题|none|
|»» 1|number|false|none|1|enum: [1, 2, 4, 3, 5]|
|»» 2|number|false|none|2|enum: [1]|
|»» 3|number|false|none|3|enum: [1, 3, 2, 5, 4, 23, 6, 8, 7]|
|»» 4|number|false|none|4|enum: [1, 3, 2, 11, 5, 7, 6, 4]|
|»» 5|number|false|none|5|none|
|» 问答题|object|false|none|问答题|none|
|»» 3|number|true|none|3|enum: [1]|

#### 枚举值

|属性|值|
|---|---|
|6|1|
|9|1|
|2|1|
|2|7|
|2|2|
|2|3|
|2|4|
|2|5|
|2|10|
|2|6|
|2|8|
|2|14|
|1|1|
|4|1|
|5|1|
|1|1|
|1|4|
|1|3|
|1|2|
|1|15|
|1|8|
|1|5|
|1|6|
|2|5|
|2|2|
|2|4|
|2|12|
|2|1|
|2|3|
|1|4|
|1|1|
|1|2|
|2|1|
|3|1|
|3|3|
|3|2|
|3|4|
|3|7|
|3|6|
|4|1|
|4|2|
|4|3|
|4|4|
|5|1|
|5|2|
|5|3|
|5|6|
|5|5|
|5|4|
|5|7|
|5|8|
|1|7|
|1|1|
|1|2|
|1|9|
|1|5|
|1|4|
|1|6|
|1|8|
|2|7|
|2|1|
|2|2|
|2|3|
|2|4|
|2|5|
|3|1|
|4|1|
|2|1|
|2|2|
|2|21|
|1|1|
|1|2|
|1|92|
|1|33|
|1|3|
|1|6|
|1|4|
|2|1|
|2|4|
|3|3|
|3|2|
|3|1|
|3|4|
|3|240|
|3|225|
|1|4|
|1|1|
|1|2|
|1|3|
|1|5|
|2|1|
|2|1|
|2|2|
|2|5|
|2|4|
|2|7|
|2|3|
|2|8|
|2|1|
|2|2|
|2|4|
|2|5|
|2|6|
|2|3|
|1|1|
|1|3|
|2|1|
|2|2|
|3|1|
|3|3|
|3|2|
|3|1027|
|3|4|
|4|2|
|4|1|
|4|6|
|2|5|
|2|2|
|2|1|
|1|1|
|1|10|
|1|11|
|1|3|
|1|12|
|1|2|
|2|1|
|2|4|
|2|7|
|2|9|
|2|6|
|2|2|
|1|1|
|3|1|
|1|1|
|1|16|
|1|2|
|1|5|
|1|4|
|1|3|
|1|6|
|2|2|
|2|1|
|2|3|
|5|1|
|10|1|
|2|1|
|2|3|
|2|4|
|2|2|
|1|1|
|1|2|
|1|4|
|1|3|
|1|5|
|2|1|
|3|1|
|3|3|
|3|2|
|3|5|
|3|4|
|3|23|
|3|6|
|3|8|
|3|7|
|4|1|
|4|3|
|4|2|
|4|11|
|4|5|
|4|7|
|4|6|
|4|4|
|3|1|

<h2 id="tocS_resource_album">resource_album</h2>

<a id="schemaresource_album"></a>
<a id="schema_resource_album"></a>
<a id="tocSresource_album"></a>
<a id="tocsresource_album"></a>

```json
{
  "subtitle": "",
  "period": "初中",
  "subject": "英语",
  "icon": "string",
  "name": "string",
  "title": "string",
  "description": "string",
  "status": 3,
  "view_times": 0,
  "download_times": 0,
  "children_title": [
    {
      "title": "专辑列表",
      "style": {
        "albums_list_title": "string"
      }
    }
  ],
  "children": [
    {
      "id": "",
      "type": "root",
      "is_query": 0,
      "name": "七年级上",
      "children": [
        {
          "id": "",
          "is_query": 1,
          "name": "string",
          "children": [
            {}
          ]
        }
      ]
    }
  ],
  "resource_count": 0,
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|subtitle|string|true|none|subtitle|enum: [""]|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|true|none|学科|enum: ["英语", "数学", "地理", "历史", "政治", "生物", "化学", "物理", "语文"]|
|icon|string|true|none|icon|none|
|name|string|true|none|名称|none|
|title|string|true|none|标题|none|
|description|string|true|none|描述|none|
|status|number|true|none|状态|enum: [3]|
|view_times|number|true|none|view_times|none|
|download_times|number|true|none|下载次数|none|
|children_title|[object]|true|none|children_title列表|none|
|» title|string|true|none|标题|enum: ["专辑列表"]|
|» style|object|true|none|style|none|
|»» albums_list_title|string|true|none|albums_list_title|none|
|children|[object]|true|none|子项|none|
|» id|string|true|none|id|enum: [""]|
|» type|string|true|none|类型|enum: ["root"]|
|» is_query|number|true|none|is_query|enum: [0]|
|» name|string|true|none|名称|enum: ["七年级上", "八年级上", "九年级上", "高一上", "高二上", "高三上"]|
|» children|[object]|true|none|子项|none|
|»» id|string|true|none|id|enum: [""]|
|»» is_query|number|true|none|is_query|enum: [1]|
|»» name|string|true|none|名称|none|
|»» children|[object]|true|none|子项|none|
|»»» id|string|true|none|id|none|
|»»» name|string|true|none|名称|none|
|»»» type|string|true|none|类型|enum: ["album"]|
|»»» is_query|number|true|none|is_query|enum: [0]|
|resource_count|number|true|none|resource数量|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|subtitle||
|period|初中|
|period|高中|
|period|小学|
|subject|英语|
|subject|数学|
|subject|地理|
|subject|历史|
|subject|政治|
|subject|生物|
|subject|化学|
|subject|物理|
|subject|语文|
|status|3|
|title|专辑列表|
|id||
|type|root|
|is_query|0|
|name|七年级上|
|name|八年级上|
|name|九年级上|
|name|高一上|
|name|高二上|
|name|高三上|
|id||
|is_query|1|
|type|album|
|is_query|0|
|valid|1|

<h2 id="tocS_resource_album_data">resource_album_data</h2>

<a id="schemaresource_album_data"></a>
<a id="schema_resource_album_data"></a>
<a id="tocSresource_album_data"></a>
<a id="tocsresource_album_data"></a>

```json
{
  "subtitle": "",
  "description": "",
  "view_times": 0,
  "download_times": 0,
  "children": [
    {
      "type": 38,
      "title": "一、阅读理解",
      "style": [
        "string"
      ],
      "children": [
        {
          "type": 15,
          "label": [
            "string"
          ],
          "content": [
            {}
          ],
          "style": [
            "string"
          ]
        }
      ]
    }
  ],
  "name": "string",
  "title": "string",
  "resource_count": 0,
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|subtitle|string|true|none|subtitle|enum: [""]|
|description|string|true|none|描述|enum: [""]|
|view_times|number|true|none|view_times|none|
|download_times|number|true|none|下载次数|none|
|children|[object]|true|none|子项|none|
|» type|number|true|none|类型|enum: [38]|
|» title|string|true|none|标题|enum: ["一、阅读理解", "二、完形填空", "三、语法填空", "四、书面表达", "一、完形填空", "二、阅读理解", "三、七选五", "四、语法填空", "五、书面表达"]|
|» style|[string]|true|none|style列表|none|
|» children|[object]|true|none|子项|none|
|»» type|number|true|none|类型|enum: [15]|
|»» label|[string]|true|none|标签|none|
|»» content|[object]|true|none|内容|none|
|»»» id|number|true|none|id|none|
|»» style|[string]|true|none|style列表|none|
|name|string|true|none|名称|none|
|title|string|true|none|标题|none|
|resource_count|number|true|none|resource数量|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|subtitle||
|description||
|type|38|
|title|一、阅读理解|
|title|二、完形填空|
|title|三、语法填空|
|title|四、书面表达|
|title|一、完形填空|
|title|二、阅读理解|
|title|三、七选五|
|title|四、语法填空|
|title|五、书面表达|
|type|15|
|valid|1|

<h2 id="tocS_resource_album_summary">resource_album_summary</h2>

<a id="schemaresource_album_summary"></a>
<a id="schema_resource_album_summary"></a>
<a id="tocSresource_album_summary"></a>
<a id="tocsresource_album_summary"></a>

```json
{
  "title": "",
  "subtitle": "",
  "period": "初中",
  "name": "string",
  "description": "string",
  "view_times": 0,
  "template": "",
  "template_style": {
    "banner": "string",
    "description": "string",
    "background": "string"
  },
  "template_data": [
    "string"
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string",
  "albums": [
    {
      "subtitle": "",
      "title": "八省统考科目",
      "albums": [
        {
          "album_id": "string",
          "album_style": {
            "albums": null,
            "album": null,
            "album_title": null
          },
          "album_data": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|title|string|true|none|标题|enum: [""]|
|subtitle|string|true|none|subtitle|enum: [""]|
|period|string|false|none|学段|enum: ["初中", "高中"]|
|name|string|true|none|名称|none|
|description|string|true|none|描述|none|
|view_times|number|true|none|view_times|none|
|template|string|true|none|模板|enum: [""]|
|template_style|object|true|none|template_style|none|
|» banner|string|true|none|banner|none|
|» description|string|true|none|描述|none|
|» background|string|true|none|background|none|
|template_data|[string]|true|none|template_data列表|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|albums|[object]|true|none|albums列表|none|
|» subtitle|string|true|none|subtitle|enum: [""]|
|» title|string|true|none|标题|enum: ["八省统考科目", "内蒙古", "河南", "四川", "云南"]|
|» albums|[object]|true|none|albums列表|none|
|»» album_id|string|true|none|album的ID|none|
|»» album_style|object|true|none|album_style|none|
|»»» albums|string|true|none|albums|none|
|»»» album|string|true|none|album|none|
|»»» album_title|string|true|none|album_title|none|
|»» album_data|[object]|true|none|album_data列表|none|
|»»» name|string|true|none|名称|enum: ["name", "title", "subject", "period"]|
|»»» value|string|true|none|值|enum: ["月考试卷", "数学", "初中", "英语", "语文", "高中"]|

#### 枚举值

|属性|值|
|---|---|
|title||
|subtitle||
|period|初中|
|period|高中|
|template||
|valid|1|
|subtitle||
|title|八省统考科目|
|title|内蒙古|
|title|河南|
|title|四川|
|title|云南|
|name|name|
|name|title|
|name|subject|
|name|period|
|value|月考试卷|
|value|数学|
|value|初中|
|value|英语|
|value|语文|
|value|高中|

<h2 id="tocS_resource_download_category">resource_download_category</h2>

<a id="schemaresource_download_category"></a>
<a id="schema_resource_download_category"></a>
<a id="tocSresource_download_category"></a>
<a id="tocsresource_download_category"></a>

```json
{
  "type": 8,
  "name": "期末统考",
  "topic_id": "",
  "view_times": 38732,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|number|true|none|类型|enum: [8, 6, 7, 4, 5, 1]|
|name|string|true|none|名称|enum: ["期末统考", "课件", "教学工具", "考后讲评", "最新月考", "期中真卷"]|
|topic_id|string|true|none|topic的ID|enum: [""]|
|view_times|number|true|none|view_times|enum: [38732, 18193, 9914, 43619, 16099, 18794]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|type|8|
|type|6|
|type|7|
|type|4|
|type|5|
|type|1|
|name|期末统考|
|name|课件|
|name|教学工具|
|name|考后讲评|
|name|最新月考|
|name|期中真卷|
|topic_id||
|view_times|38732|
|view_times|18193|
|view_times|9914|
|view_times|43619|
|view_times|16099|
|view_times|18794|

<h2 id="tocS_resource_erratum">resource_erratum</h2>

<a id="schemaresource_erratum"></a>
<a id="schema_resource_erratum"></a>
<a id="tocSresource_erratum"></a>
<a id="tocsresource_erratum"></a>

```json
{
  "id": 0,
  "type": "string",
  "comment": {
    "err_msg": "string",
    "err_fields": "string"
  },
  "status": "string",
  "user_id": 0,
  "ctime": "string",
  "utime": "string",
  "period": "初中",
  "subject": "语文"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number|true|none|id|none|
|type|string|true|none|类型|none|
|comment|object|true|none|点评/注释|none|
|» err_msg|string|true|none|err_msg|none|
|» err_fields|string|true|none|err_fields列表|none|
|status|string|true|none|状态|none|
|user_id|number|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|period|string|false|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|false|none|学科|enum: ["语文", "生物", "英语", "数学", "政治", "化学", "物理", "历史", "地理"]|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|
|subject|语文|
|subject|生物|
|subject|英语|
|subject|数学|
|subject|政治|
|subject|化学|
|subject|物理|
|subject|历史|
|subject|地理|

<h2 id="tocS_school_crm_action">school_crm_action</h2>

<a id="schemaschool_crm_action"></a>
<a id="schema_school_crm_action"></a>
<a id="tocSschool_crm_action"></a>
<a id="tocsschool_crm_action"></a>

```json
{
  "name": "string",
  "action_log": [
    {
      "id": "string",
      "schoolId": 0,
      "customerId": "string",
      "customerName": "仁怀市万达小学",
      "verName": "基础版",
      "beginDate": "string",
      "endDate": "string",
      "isTrial": 0,
      "enabled": 1,
      "params": [
        "string"
      ],
      "handleTime": "string",
      "handlerName": "张东茹",
      "remark": "string"
    }
  ],
  "unhandled": [
    "string"
  ],
  "handled": [
    {
      "time": 0,
      "user": {
        "id": "string",
        "name": "张东茹"
      },
      "action": [
        {
          "id": "string",
          "before": null,
          "after": {
            "type": null,
            "order_id": null,
            "contract_id": null,
            "id": null,
            "verName": null,
            "beginDate": null,
            "endDate": null,
            "isTrial": null,
            "enabled": null,
            "params": null,
            "remark": null,
            "handleTime": null,
            "handlerName": null
          }
        }
      ]
    }
  ],
  "ctime": "string",
  "utime": "string",
  "last_sync_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|none|
|action_log|[object]|true|none|action_log列表|none|
|» id|string|true|none|id|none|
|» schoolId|number|true|none|school的ID|none|
|» customerId|string|true|none|customer的ID|none|
|» customerName|string|true|none|customer名称|enum: ["仁怀市万达小学", "仁怀六中", "仁怀市中枢二小", "仁怀市中枢一小", "仁怀市周林高中", "仁怀市盐津一小", "仁怀市实验小学"]|
|» verName|string|true|none|ver名称|enum: ["基础版"]|
|» beginDate|string|true|none|日期|none|
|» endDate|string|true|none|日期|none|
|» isTrial|number|true|none|isTrial|enum: [0]|
|» enabled|number|true|none|enabled|enum: [1]|
|» params|[string]|true|none|params列表|none|
|» handleTime|string|true|none|handle时间|none|
|» handlerName|string|true|none|handler名称|enum: ["张东茹", "朱倩"]|
|» remark|string|true|none|备注|none|
|unhandled|[string]|true|none|unhandled列表|none|
|handled|[object]|true|none|handled列表|none|
|» time|number|true|none|time|none|
|» user|object|true|none|user|none|
|»» id|string|true|none|id|none|
|»» name|string|true|none|名称|enum: ["张东茹", "王清洁"]|
|» action|[object]|true|none|action列表|none|
|»» id|string|true|none|id|none|
|»» before|null|true|none|before|none|
|»» after|object|true|none|after|none|
|»»» type|string|true|none|类型|none|
|»»» order_id|null|true|none|order的ID|none|
|»»» contract_id|string|true|none|contract的ID|enum: [""]|
|»»» id|string|true|none|id|none|
|»»» verName|string|true|none|ver名称|enum: ["基础版"]|
|»»» beginDate|string|true|none|日期|none|
|»»» endDate|string|true|none|日期|none|
|»»» isTrial|number|true|none|isTrial|enum: [0]|
|»»» enabled|number|true|none|enabled|enum: [1]|
|»»» params|[string]|true|none|params列表|none|
|»»» remark|string|true|none|备注|none|
|»»» handleTime|string|true|none|handle时间|none|
|»»» handlerName|string|true|none|handler名称|enum: ["张东茹", "朱倩"]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|last_sync_time|string|true|none|last_sync时间|none|

#### 枚举值

|属性|值|
|---|---|
|customerName|仁怀市万达小学|
|customerName|仁怀六中|
|customerName|仁怀市中枢二小|
|customerName|仁怀市中枢一小|
|customerName|仁怀市周林高中|
|customerName|仁怀市盐津一小|
|customerName|仁怀市实验小学|
|verName|基础版|
|isTrial|0|
|enabled|1|
|handlerName|张东茹|
|handlerName|朱倩|
|name|张东茹|
|name|王清洁|
|contract_id||
|verName|基础版|
|isTrial|0|
|enabled|1|
|handlerName|张东茹|
|handlerName|朱倩|

<h2 id="tocS_school_ids">school_ids</h2>

<a id="schemaschool_ids"></a>
<a id="schema_school_ids"></a>
<a id="tocSschool_ids"></a>
<a id="tocsschool_ids"></a>

```json
{
  "ids": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ids|[number]|true|none|ids列表|none|

<h2 id="tocS_school_info">school_info</h2>

<a id="schemaschool_info"></a>
<a id="schema_school_info"></a>
<a id="tocSschool_info"></a>
<a id="tocsschool_info"></a>

```json
{
  "name": "string",
  "type": "体验校",
  "member_id": "",
  "is_edit_type": true,
  "valid": true,
  "ctime": "string",
  "utime": "string",
  "vip_type": "school",
  "start_time": "string",
  "expired_time": "string",
  "expire_month": 3,
  "que_download_num": 30,
  "exampaper_download_num": 10,
  "assemble_download_num": 5,
  "que_details_num": 100,
  "operator": "string",
  "province": "string",
  "city": "string",
  "country": "string",
  "system": "公立",
  "edu_system": "完全中学",
  "size": "500人以下",
  "teachers": "string",
  "vip_num": 0,
  "vip_open_time": "string",
  "vip_open_user": {
    "id": "string",
    "name": "张东茹"
  },
  "create_user": {
    "id": "string",
    "name": "张东茹"
  },
  "download_num": 20,
  "vip_period": [
    "string"
  ],
  "regist_time": "string",
  "teacher_count": 0,
  "probation_time": "string",
  "exampaper_details_num": 5
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|false|none|名称|none|
|type|string|false|none|类型|enum: ["体验校", "", "付费校", "终止合作校"]|
|member_id|string|false|none|member的ID|enum: [""]|
|is_edit_type|boolean|false|none|is_edit_type|enum: [true, false]|
|valid|boolean|false|none|是否有效|enum: [true]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|vip_type|string|false|none|会员类型|enum: ["school"]|
|start_time|string|false|none|开始时间|none|
|expired_time|string|false|none|过期时间|none|
|expire_month|number|false|none|expire_month|enum: [3]|
|que_download_num|number|false|none|que_download_num|enum: [30, 50, 10]|
|exampaper_download_num|number|false|none|exampaper_download_num|enum: [10, 30, 15]|
|assemble_download_num|number|false|none|assemble_download_num|enum: [5, 20, 10]|
|que_details_num|number|false|none|que_details_num|enum: [100, 1000, 300, 30]|
|operator|string|false|none|操作人|none|
|province|string|false|none|省份|none|
|city|string|false|none|城市|none|
|country|string|false|none|country|none|
|system|string|false|none|system|enum: ["公立", "私立", ""]|
|edu_system|string|false|none|edu_system|enum: ["完全中学", "九年一贯制", "高中", "小学", "初中", "十二年一贯制"]|
|size|string|false|none|大小|enum: ["500人以下", "", "5001人以上"]|
|teachers|string|false|none|teachers列表|none|
|vip_num|number|false|none|vip_num|none|
|vip_open_time|string|false|none|vip_open时间|none|
|vip_open_user|object|false|none|vip_open_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["张东茹", "王清洁", "admin", "陈健", "赵原", "朱文娟", "杨乃仟"]|
|create_user|object|false|none|create_user|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["张东茹", "王清洁", "陈健", "赵原", "admin", "朱文娟", "杨乃仟"]|
|download_num|number|false|none|download_num|enum: [20]|
|vip_period|[string]|false|none|vip_period列表|none|
|regist_time|string|false|none|regist时间|none|
|teacher_count|number|false|none|teacher数量|none|
|probation_time|string|false|none|probation时间|none|
|exampaper_details_num|number|false|none|exampaper_details_num|enum: [5]|

#### 枚举值

|属性|值|
|---|---|
|type|体验校|
|type||
|type|付费校|
|type|终止合作校|
|member_id||
|is_edit_type|true|
|is_edit_type|false|
|valid|true|
|vip_type|school|
|expire_month|3|
|que_download_num|30|
|que_download_num|50|
|que_download_num|10|
|exampaper_download_num|10|
|exampaper_download_num|30|
|exampaper_download_num|15|
|assemble_download_num|5|
|assemble_download_num|20|
|assemble_download_num|10|
|que_details_num|100|
|que_details_num|1000|
|que_details_num|300|
|que_details_num|30|
|system|公立|
|system|私立|
|system||
|edu_system|完全中学|
|edu_system|九年一贯制|
|edu_system|高中|
|edu_system|小学|
|edu_system|初中|
|edu_system|十二年一贯制|
|size|500人以下|
|size||
|size|5001人以上|
|name|张东茹|
|name|王清洁|
|name|admin|
|name|陈健|
|name|赵原|
|name|朱文娟|
|name|杨乃仟|
|name|张东茹|
|name|王清洁|
|name|陈健|
|name|赵原|
|name|admin|
|name|朱文娟|
|name|杨乃仟|
|download_num|20|
|exampaper_details_num|5|

<h2 id="tocS_school_info_log">school_info_log</h2>

<a id="schemaschool_info_log"></a>
<a id="schema_school_info_log"></a>
<a id="tocSschool_info_log"></a>
<a id="tocsschool_info_log"></a>

```json
{
  "school_id": 0,
  "type": "string",
  "content": {
    "items": "string",
    "order_id": "",
    "vip_name": "校园版Plus",
    "vip_stime": 0,
    "vip_etime": 0,
    "contract_id": "",
    "vip_type": "school",
    "params": "string",
    "agent": {
      "id": "string",
      "name": "绥滨县第一中学",
      "type": "agent"
    },
    "add": "string",
    "del": "string",
    "remark": "暂停使用",
    "type": "manual",
    "content": {
      "remark": ""
    }
  },
  "time": "string",
  "ctime": "string",
  "utime": "string",
  "operator": {
    "id": "string",
    "name": "张东茹"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|school_id|number|true|none|学校的ID|none|
|type|string|true|none|类型|none|
|content|object|true|none|内容|none|
|» items|string|false|none|items列表|none|
|» order_id|string|false|none|order的ID|enum: [""]|
|» vip_name|string|false|none|vip名称|enum: ["校园版Plus", "校园版", "学校专业版", "基础版"]|
|» vip_stime|number|false|none|vip_stime|none|
|» vip_etime|number|false|none|vip_etime|none|
|» contract_id|string|false|none|contract的ID|enum: [""]|
|» vip_type|string|false|none|会员类型|enum: ["school"]|
|» params|string|false|none|params列表|none|
|» agent|object|false|none|agent|none|
|»» id|string|true|none|id|none|
|»» name|string|true|none|名称|enum: ["绥滨县第一中学"]|
|»» type|string|true|none|类型|enum: ["agent", "school"]|
|» add|string|false|none|add列表|none|
|» del|string|false|none|del列表|none|
|» remark|string|false|none|备注|enum: ["暂停使用", "测试修改", "", "先关闭", "取消", "广强测试", "测试调整", "test", "演示账号", "关闭题库"]|
|» type|string|false|none|类型|enum: ["manual"]|
|» content|object|false|none|内容|none|
|»» remark|string|true|none|备注|enum: [""]|
|time|string|true|none|日期时间|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|operator|object|true|none|操作人|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["张东茹", "王清洁", "王珊珊", "admin", "卞聪慧", "陈健", "赵原", "杨乃仟", "朱文娟"]|

#### 枚举值

|属性|值|
|---|---|
|order_id||
|vip_name|校园版Plus|
|vip_name|校园版|
|vip_name|学校专业版|
|vip_name|基础版|
|contract_id||
|vip_type|school|
|name|绥滨县第一中学|
|type|agent|
|type|school|
|remark|暂停使用|
|remark|测试修改|
|remark||
|remark|先关闭|
|remark|取消|
|remark|广强测试|
|remark|测试调整|
|remark|test|
|remark|演示账号|
|remark|关闭题库|
|type|manual|
|remark||
|name|张东茹|
|name|王清洁|
|name|王珊珊|
|name|admin|
|name|卞聪慧|
|name|陈健|
|name|赵原|
|name|杨乃仟|
|name|朱文娟|

<h2 id="tocS_topic">topic</h2>

<a id="schematopic"></a>
<a id="schema_topic"></a>
<a id="tocStopic"></a>
<a id="tocstopic"></a>

```json
{
  "name": "教师日常工具",
  "title": "教师日常工具",
  "subtitle": "",
  "description": "",
  "search_items": [
    {
      "label": "学段",
      "key": "period",
      "value_type": "string",
      "value": [
        {
          "key": "string",
          "parent_value": [
            "string"
          ]
        }
      ],
      "parent_key": "period"
    }
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["教师日常工具", "最新月考试卷", "上学期期中考试", "开学摸底考试"]|
|title|string|true|none|标题|enum: ["教师日常工具", "期中真卷速递", "最新月考", "上学期期中考试", "开学摸底考试"]|
|subtitle|string|true|none|subtitle|enum: ["", "上学期期中考试", "开学摸底考试"]|
|description|string|true|none|描述|enum: ["", "上学期期中考试", "开学摸底考试"]|
|search_items|[object]|true|none|search_items列表|none|
|» label|string|true|none|标签|enum: ["学段", "年级", "学科", "地区", "年份", "资源分类"]|
|» key|string|true|none|键/标识符|enum: ["period", "grade", "subject", "to_year"]|
|» value_type|string|true|none|value_type|enum: ["string", "number", "tree"]|
|» value|[object]|true|none|值|none|
|»» key|string|true|none|键/标识符|none|
|»» parent_value|[string]|false|none|parent_value列表|none|
|» parent_key|string|false|none|parent_key|enum: ["period", "grade"]|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|教师日常工具|
|name|最新月考试卷|
|name|上学期期中考试|
|name|开学摸底考试|
|title|教师日常工具|
|title|期中真卷速递|
|title|最新月考|
|title|上学期期中考试|
|title|开学摸底考试|
|subtitle||
|subtitle|上学期期中考试|
|subtitle|开学摸底考试|
|description||
|description|上学期期中考试|
|description|开学摸底考试|
|label|学段|
|label|年级|
|label|学科|
|label|地区|
|label|年份|
|label|资源分类|
|key|period|
|key|grade|
|key|subject|
|key|to_year|
|value_type|string|
|value_type|number|
|value_type|tree|
|parent_key|period|
|parent_key|grade|
|valid|1|

<h2 id="tocS_topic_data">topic_data</h2>

<a id="schematopic_data"></a>
<a id="schema_topic_data"></a>
<a id="tocStopic_data"></a>
<a id="tocstopic_data"></a>

```json
{
  "resource_id": 0,
  "topic_id": "string",
  "ctime": "string",
  "resource_image": "",
  "resource_info": {
    "name": "string",
    "vague_name": "",
    "period": "小学",
    "subject": "英语",
    "grade": "string",
    "press_version": "string",
    "province": "string",
    "city": "string",
    "from_year": 2023,
    "to_year": 2024,
    "big_ques_num": 0,
    "small_ques_num": 0,
    "download_times": 0,
    "view_times": 0,
    "ctime": "string",
    "utime": "string",
    "from": "zwst",
    "sch_name": "string"
  },
  "resource_name": "string",
  "resource_type": "string",
  "utime": "string",
  "valid": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|resource_id|number|true|none|资源ID|none|
|topic_id|string|true|none|topic的ID|none|
|ctime|string|true|none|创建时间|none|
|resource_image|string|true|none|resource_image|enum: [""]|
|resource_info|object|true|none|resource_info|none|
|» name|string|true|none|名称|none|
|» vague_name|string|true|none|vague名称|enum: [""]|
|» period|string|true|none|学段|enum: ["小学", "高中", "初中"]|
|» subject|string|true|none|学科|enum: ["英语", "化学", "物理", "语文", "生物", "地理", "历史", "数学", "政治"]|
|» grade|string|true|none|年级|none|
|» press_version|string|true|none|教材版本|none|
|» province|string|true|none|省份|none|
|» city|string|true|none|城市|none|
|» from_year|number|true|none|from_year|enum: [2023, 2024, 2022]|
|» to_year|number|true|none|to_year|enum: [2024, 2025, 2023]|
|» big_ques_num|number|true|none|big_ques_num|none|
|» small_ques_num|number|true|none|small_ques_num|none|
|» download_times|number|true|none|下载次数|none|
|» view_times|number|true|none|view_times|none|
|» ctime|string|true|none|创建时间|none|
|» utime|string|true|none|更新时间|none|
|» from|string|true|none|来源|enum: ["zwst", "drm", "jyy", "mkp"]|
|» sch_name|string|true|none|sch名称|none|
|resource_name|string|true|none|resource名称|none|
|resource_type|string|true|none|资源类型|none|
|utime|string|true|none|更新时间|none|
|valid|number|true|none|是否有效|enum: [1, 0]|

#### 枚举值

|属性|值|
|---|---|
|resource_image||
|vague_name||
|period|小学|
|period|高中|
|period|初中|
|subject|英语|
|subject|化学|
|subject|物理|
|subject|语文|
|subject|生物|
|subject|地理|
|subject|历史|
|subject|数学|
|subject|政治|
|from_year|2023|
|from_year|2024|
|from_year|2022|
|to_year|2024|
|to_year|2025|
|to_year|2023|
|from|zwst|
|from|drm|
|from|jyy|
|from|mkp|
|valid|1|
|valid|0|

<h2 id="tocS_topic_page">topic_page</h2>

<a id="schematopic_page"></a>
<a id="schema_topic_page"></a>
<a id="tocStopic_page"></a>
<a id="tocstopic_page"></a>

```json
{
  "name": "教师日常工具",
  "description": "教师日常工具",
  "template": "教师日常工具",
  "template_data": {
    "banner": "string",
    "background": "#8437f3",
    "color": "#0D51F0",
    "menu_bg": "#E3EBFF"
  },
  "topics": [
    "string"
  ],
  "valid": 1,
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none|名称|enum: ["教师日常工具", "最新月考试卷", "上学期期中考试", "开学摸底考试"]|
|description|string|true|none|描述|enum: ["教师日常工具", "最新月考试卷", "上学期期中考试", "开学摸底考试"]|
|template|string|true|none|模板|enum: ["教师日常工具", "最新月考试卷", "上学期期中考试", "开学摸底考试"]|
|template_data|object|true|none|template_data|none|
|» banner|string|true|none|banner|none|
|» background|string|true|none|background|enum: ["#8437f3", "#0D51F0", "#fa8d29", "#FC8F0C", "#32BD5A"]|
|» color|string|true|none|color|enum: ["#0D51F0", "#8437f3", "#fdab2b", "#FD4513", "#32BD5A"]|
|» menu_bg|string|true|none|menu_bg|enum: ["#E3EBFF", "#fef7ed", "#FEE0D4", "#D3F1DB"]|
|topics|[string]|true|none|topics列表|none|
|valid|number|true|none|是否有效|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|name|教师日常工具|
|name|最新月考试卷|
|name|上学期期中考试|
|name|开学摸底考试|
|description|教师日常工具|
|description|最新月考试卷|
|description|上学期期中考试|
|description|开学摸底考试|
|template|教师日常工具|
|template|最新月考试卷|
|template|上学期期中考试|
|template|开学摸底考试|
|background|#8437f3|
|background|#0D51F0|
|background|#fa8d29|
|background|#FC8F0C|
|background|#32BD5A|
|color|#0D51F0|
|color|#8437f3|
|color|#fdab2b|
|color|#FD4513|
|color|#32BD5A|
|menu_bg|#E3EBFF|
|menu_bg|#fef7ed|
|menu_bg|#FEE0D4|
|menu_bg|#D3F1DB|
|valid|1|

<h2 id="tocS_upload_exampaper">upload_exampaper</h2>

<a id="schemaupload_exampaper"></a>
<a id="schema_upload_exampaper"></a>
<a id="tocSupload_exampaper"></a>
<a id="tocsupload_exampaper"></a>

```json
{
  "user_id": 0,
  "word_url": "string",
  "name": "string",
  "period": "初中",
  "subject": "数学",
  "grade": "string",
  "type": "string",
  "subtitle": "",
  "score": 0,
  "duration": 0,
  "paper_info": "",
  "cand_info": "",
  "score_info": "",
  "attentions": "",
  "secret_tag": "",
  "gutter": 0,
  "volumes": [
    {
      "blocks": "string",
      "note": "",
      "title": "卷I（选择题）"
    }
  ],
  "status": "success",
  "partsList": [
    "string"
  ],
  "ctime": "string",
  "utime": "string",
  "err_msg": "",
  "is_delete": true,
  "view_count": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|word_url|string|true|none|word的URL|none|
|name|string|true|none|名称|none|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|true|none|学科|enum: ["数学", "地理", "语文", "英语", "政治", "物理", "历史", "化学", "生物"]|
|grade|string|true|none|年级|none|
|type|string|true|none|类型|none|
|subtitle|string|true|none|subtitle|enum: [""]|
|score|number|true|none|分数|none|
|duration|number|true|none|持续时间（分钟）|enum: [0]|
|paper_info|string|true|none|paper_info|enum: [""]|
|cand_info|string|true|none|cand_info|enum: [""]|
|score_info|string|true|none|score_info|enum: [""]|
|attentions|string|true|none|attentions|enum: [""]|
|secret_tag|string|true|none|secret_tag|enum: [""]|
|gutter|number|true|none|gutter|enum: [0]|
|volumes|[object]|true|none|volumes列表|none|
|» blocks|string|true|none|blocks列表|none|
|» note|string|true|none|note|enum: [""]|
|» title|string|true|none|标题|enum: ["卷I（选择题）"]|
|status|string|true|none|状态|enum: ["success", "failed"]|
|partsList|[string]|true|none|partsList列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|err_msg|string|false|none|err_msg|enum: ["", "服务错误", "试题解析错误", "组合资源错误"]|
|is_delete|boolean|false|none|is_delete|enum: [true]|
|view_count|number|false|none|浏览次数|enum: [1, 5, 3, 2, 6, 4, 33]|

#### 枚举值

|属性|值|
|---|---|
|period|初中|
|period|高中|
|period|小学|
|subject|数学|
|subject|地理|
|subject|语文|
|subject|英语|
|subject|政治|
|subject|物理|
|subject|历史|
|subject|化学|
|subject|生物|
|subtitle||
|duration|0|
|paper_info||
|cand_info||
|score_info||
|attentions||
|secret_tag||
|gutter|0|
|note||
|title|卷I（选择题）|
|status|success|
|status|failed|
|err_msg||
|err_msg|服务错误|
|err_msg|试题解析错误|
|err_msg|组合资源错误|
|is_delete|true|
|view_count|1|
|view_count|5|
|view_count|3|
|view_count|2|
|view_count|6|
|view_count|4|
|view_count|33|

<h2 id="tocS_upload_questions">upload_questions</h2>

<a id="schemaupload_questions"></a>
<a id="schema_upload_questions"></a>
<a id="tocSupload_questions"></a>
<a id="tocsupload_questions"></a>

```json
{
  "subject": "数学",
  "period": "初中",
  "description": "string",
  "comment": "",
  "blocks": {
    "types": [
      "string"
    ],
    "explanations": [
      "string"
    ],
    "solutions": [
      "string"
    ],
    "answers": "",
    "stems": "string",
    "knowledges": "string",
    "core_knowledges": [
      "string"
    ],
    "elements": [
      "string"
    ],
    "difficulty": 3,
    "knowledgeAll": ""
  },
  "knowledges": "",
  "difficulty": 3,
  "type": "string",
  "from": "tiku",
  "user_id": 0,
  "exampaper_id": "string",
  "id": "string",
  "is_delete": true,
  "utime": "string",
  "answer": "",
  "core_knowledges": [
    "string"
  ],
  "elements": [
    "string"
  ],
  "elite": -1,
  "score": 0,
  "tags": {
    "count_sub_ques": 1,
    "count_options": [
      0
    ],
    "grades": [
      "string"
    ]
  },
  "type_tags": [
    "string"
  ],
  "ctime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|subject|string|true|none|学科|enum: ["数学", "语文", "英语", "政治", "物理", "生物", "历史", "地理", "化学"]|
|period|string|true|none|学段|enum: ["初中", "高中", "小学"]|
|description|string|true|none|描述|none|
|comment|string|true|none|点评/注释|enum: [""]|
|blocks|object|true|none|blocks列表|none|
|» types|[string]|true|none|types列表|none|
|» explanations|[string]|false|none|explanations列表|none|
|» solutions|[string]|true|none|solutions列表|none|
|» answers|string|true|none|answers列表|enum: [""]|
|» stems|string|true|none|stems列表|none|
|» knowledges|string|false|none|知识点|none|
|» core_knowledges|[string]|false|none|core_knowledges列表|none|
|» elements|[string]|false|none|elements列表|none|
|» difficulty|number|false|none|难度|enum: [3, 0.94]|
|» knowledgeAll|string|false|none|knowledgeAll列表|enum: [""]|
|knowledges|string|true|none|知识点|enum: [""]|
|difficulty|number|true|none|难度|enum: [3, 0.65, 0.4, 0.94, 0.85, 0]|
|type|string|true|none|类型|none|
|from|string|true|none|来源|enum: ["tiku"]|
|user_id|number|true|none|用户ID|none|
|exampaper_id|string|true|none|试卷ID|none|
|id|string|true|none|id|none|
|is_delete|boolean|false|none|is_delete|enum: [true]|
|utime|string|false|none|更新时间|none|
|answer|string|false|none|answer|enum: [""]|
|core_knowledges|[string]|false|none|core_knowledges列表|none|
|elements|[string]|false|none|elements列表|none|
|elite|number|false|none|elite|enum: [-1]|
|score|number|false|none|分数|none|
|tags|object|false|none|标签|none|
|» count_sub_ques|number|true|none|count_sub_ques|enum: [1, 3, 2, 5, 4, 20, 10, 13, 8, 6]|
|» count_options|[number]|true|none|count_options列表|none|
|» grades|[string]|true|none|grades列表|none|
|type_tags|[string]|false|none|type_tags列表|none|
|ctime|string|false|none|创建时间|none|

#### 枚举值

|属性|值|
|---|---|
|subject|数学|
|subject|语文|
|subject|英语|
|subject|政治|
|subject|物理|
|subject|生物|
|subject|历史|
|subject|地理|
|subject|化学|
|period|初中|
|period|高中|
|period|小学|
|comment||
|answers||
|difficulty|3|
|difficulty|0.94|
|knowledgeAll||
|knowledges||
|difficulty|3|
|difficulty|0.65|
|difficulty|0.4|
|difficulty|0.94|
|difficulty|0.85|
|difficulty|0|
|from|tiku|
|is_delete|true|
|answer||
|elite|-1|
|count_sub_ques|1|
|count_sub_ques|3|
|count_sub_ques|2|
|count_sub_ques|5|
|count_sub_ques|4|
|count_sub_ques|20|
|count_sub_ques|10|
|count_sub_ques|13|
|count_sub_ques|8|
|count_sub_ques|6|

<h2 id="tocS_user">user</h2>

<a id="schemauser"></a>
<a id="schema_user"></a>
<a id="tocSuser"></a>
<a id="tocsuser"></a>

```json
{
  "is_vip": false,
  "role": "teacher",
  "last_time": "string",
  "finished": 1,
  "ctime": "string",
  "utime": "string",
  "source": "tiku",
  "name": "string",
  "sch_id": 0,
  "sch_name": "string",
  "phone": "string",
  "login_name": "string",
  "grade": "string",
  "curr": {
    "period": "高中",
    "subject": "string",
    "grade": "string"
  },
  "trace": {
    "period": "高中",
    "subject": "string",
    "grade": "string",
    "press_version": "人教版"
  },
  "init_right": false,
  "book_profile": "string",
  "exampaper_profile": "string",
  "assemble_download_num": 0,
  "exampaper_download_num": 0,
  "que_details_num": 0,
  "que_download_num": 0,
  "chieng_book_profile": [
    {
      "id": **********,
      "type": "book",
      "period": "初中",
      "subject": "英语",
      "press_version": "北京课改版",
      "grade": "七年级上",
      "trace": [
        "string"
      ]
    }
  ],
  "city": "string",
  "district": "string",
  "province": "string",
  "filters": [
    {
      "id": 0,
      "period": "初中",
      "subject": "数学",
      "type": "zsdxt",
      "knowledge": **********
    }
  ],
  "use_time": "string",
  "edu_file_download_num": 0,
  "hfs": 0,
  "period": "初中",
  "subjects": [
    {
      "subject": "数学",
      "press_version": "北师大版",
      "teaching_materials": "七年级下",
      "book_id": **********
    }
  ],
  "vip_open_user": {
    "account": "liuzhen",
    "id": "string",
    "name": "string",
    "role": "cs"
  },
  "vip_open_time": "string",
  "subject": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|is_vip|boolean|false|none|是否会员|enum: [false]|
|role|string|false|none|角色|enum: ["teacher"]|
|last_time|string|false|none|last时间|none|
|finished|number|false|none|finished|enum: [1, 0]|
|ctime|string|false|none|创建时间|none|
|utime|string|false|none|更新时间|none|
|source|string|false|none|来源|enum: ["tiku", "yj", "unify"]|
|name|string|false|none|名称|none|
|sch_id|number|false|none|学校ID|none|
|sch_name|string|false|none|sch名称|none|
|phone|string|false|none|phone|none|
|login_name|string|false|none|login名称|none|
|grade|string|false|none|年级|none|
|curr|object|false|none|curr|none|
|» period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|» subject|string|true|none|学科|none|
|» grade|string|false|none|年级|none|
|trace|object|false|none|trace|none|
|» period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|» subject|string|true|none|学科|none|
|» grade|string|false|none|年级|none|
|» press_version|string|false|none|教材版本|enum: ["人教版", "新人教版", "人教A版"]|
|init_right|boolean|false|none|init_right|enum: [false, true]|
|book_profile|string|false|none|book_profile列表|none|
|exampaper_profile|string|false|none|exampaper_profile列表|none|
|assemble_download_num|number|false|none|assemble_download_num|enum: [0]|
|exampaper_download_num|number|false|none|exampaper_download_num|enum: [0]|
|que_details_num|number|false|none|que_details_num|enum: [0]|
|que_download_num|number|false|none|que_download_num|enum: [0]|
|chieng_book_profile|[object]|false|none|chieng_book_profile列表|none|
|» id|number|true|none|id|enum: [**********, **********, **********, **********, **********, **********, **********, **********, **********]|
|» type|string|true|none|类型|enum: ["book"]|
|» period|string|true|none|学段|enum: ["初中"]|
|» subject|string|true|none|学科|enum: ["英语"]|
|» press_version|string|true|none|教材版本|enum: ["北京课改版", "人教版", "外研版", "沪教牛津版", "人教版五四制", "牛津译林版", "仁爱版"]|
|» grade|string|true|none|年级|enum: ["七年级上", "九年级全一册", "七年级下", "八年级上"]|
|» trace|[string]|false|none|trace列表|none|
|city|string|false|none|城市|none|
|district|string|false|none|district|none|
|province|string|false|none|省份|none|
|filters|[object]|false|none|filters列表|none|
|» id|number|true|none|id|none|
|» period|string|true|none|学段|enum: ["初中", "小学", "高中"]|
|» subject|string|true|none|学科|enum: ["数学", "英语", "化学", "物理", "历史", "语文", "政治", "科学"]|
|» type|string|true|none|类型|enum: ["zsdxt", "jcxt"]|
|» knowledge|string|true|none|knowledge|enum: [**********, 3140878335, **********, **********, **********, "", **********, **********]|
|use_time|string|false|none|use时间|none|
|edu_file_download_num|number|false|none|edu_file_download_num|enum: [0]|
|hfs|number|false|none|hfs|enum: [0, 1]|
|period|string|false|none|学段|enum: ["初中", "高中"]|
|subjects|[object]|false|none|subjects列表|none|
|» subject|string|false|none|学科|enum: ["数学", "物理", "历史", "政治", "英语"]|
|» press_version|string|false|none|教材版本|enum: ["北师大版", "部编版", "人教版"]|
|» teaching_materials|string|false|none|teaching_materials|enum: ["七年级下", "八年级下"]|
|» book_id|number|false|none|教材ID|enum: [**********, **********, **********, **********]|
|vip_open_user|object|false|none|vip_open_user|none|
|» account|string|false|none|account|enum: ["liuzhen"]|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|none|
|» role|string|false|none|角色|enum: ["cs"]|
|vip_open_time|string|false|none|vip_open时间|none|
|subject|string|false|none|学科|none|

#### 枚举值

|属性|值|
|---|---|
|is_vip|false|
|role|teacher|
|finished|1|
|finished|0|
|source|tiku|
|source|yj|
|source|unify|
|period|高中|
|period|初中|
|period|小学|
|period|高中|
|period|初中|
|period|小学|
|press_version|人教版|
|press_version|新人教版|
|press_version|人教A版|
|init_right|false|
|init_right|true|
|assemble_download_num|0|
|exampaper_download_num|0|
|que_details_num|0|
|que_download_num|0|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|id|**********|
|type|book|
|period|初中|
|subject|英语|
|press_version|北京课改版|
|press_version|人教版|
|press_version|外研版|
|press_version|沪教牛津版|
|press_version|人教版五四制|
|press_version|牛津译林版|
|press_version|仁爱版|
|grade|七年级上|
|grade|九年级全一册|
|grade|七年级下|
|grade|八年级上|
|period|初中|
|period|小学|
|period|高中|
|subject|数学|
|subject|英语|
|subject|化学|
|subject|物理|
|subject|历史|
|subject|语文|
|subject|政治|
|subject|科学|
|type|zsdxt|
|type|jcxt|
|knowledge|**********|
|knowledge|3140878335|
|knowledge|**********|
|knowledge|**********|
|knowledge|**********|
|knowledge||
|knowledge|**********|
|knowledge|**********|
|edu_file_download_num|0|
|hfs|0|
|hfs|1|
|period|初中|
|period|高中|
|subject|数学|
|subject|物理|
|subject|历史|
|subject|政治|
|subject|英语|
|press_version|北师大版|
|press_version|部编版|
|press_version|人教版|
|teaching_materials|七年级下|
|teaching_materials|八年级下|
|book_id|**********|
|book_id|**********|
|book_id|**********|
|book_id|**********|
|account|liuzhen|
|role|cs|

<h2 id="tocS_user_coupons">user_coupons</h2>

<a id="schemauser_coupons"></a>
<a id="schema_user_coupons"></a>
<a id="tocSuser_coupons"></a>
<a id="tocsuser_coupons"></a>

```json
{
  "user_id": ****************,
  "coupon_id": "string",
  "coupon_name": "0504测试",
  "discount_type": 1,
  "discount_count": 3000,
  "target_goods": [
    {
      "id": "string",
      "name": "题库年度会员"
    }
  ],
  "valid_from": "string",
  "valid_to": "string",
  "desc": "234",
  "usage_status": 0,
  "ctime": "string",
  "utime": "string",
  "coupon_fee": 0,
  "transaction_id": "",
  "usage_time": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|enum: [****************, ****************, ****************]|
|coupon_id|string|true|none|coupon的ID|none|
|coupon_name|string|true|none|coupon名称|enum: ["0504测试", "测试1"]|
|discount_type|number|true|none|discount_type|enum: [1]|
|discount_count|number|true|none|discount数量|enum: [3000, 5000, 10000, 9900]|
|target_goods|[object]|true|none|target_goods列表|none|
|» id|string|true|none|id|none|
|» name|string|true|none|名称|enum: ["题库年度会员"]|
|valid_from|string|true|none|日期时间|none|
|valid_to|string|true|none|日期时间|none|
|desc|string|true|none|desc|enum: ["234", "未覆盖", "11", "赠送年度优惠券"]|
|usage_status|number|true|none|usage_status|enum: [0]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|coupon_fee|number|false|none|coupon_fee|enum: [0]|
|transaction_id|string|false|none|transaction的ID|enum: [""]|
|usage_time|null|false|none|usage时间|none|

#### 枚举值

|属性|值|
|---|---|
|user_id|****************|
|user_id|****************|
|user_id|****************|
|coupon_name|0504测试|
|coupon_name|测试1|
|discount_type|1|
|discount_count|3000|
|discount_count|5000|
|discount_count|10000|
|discount_count|9900|
|name|题库年度会员|
|desc|234|
|desc|未覆盖|
|desc|11|
|desc|赠送年度优惠券|
|usage_status|0|
|coupon_fee|0|
|transaction_id||

<h2 id="tocS_user_download">user_download</h2>

<a id="schemauser_download"></a>
<a id="schema_user_download"></a>
<a id="tocSuser_download"></a>
<a id="tocsuser_download"></a>

```json
{
  "user_id": 0,
  "sch_id": 0,
  "resource_type": "string",
  "download_times": 1,
  "ctime": "string",
  "utime": "string",
  "category": 2,
  "period": "初中",
  "subject": "string",
  "resource_id": "string",
  "type": "string",
  "press_version": "string",
  "grade": "string",
  "resource_name": "string",
  "book_id": 0,
  "chapter_id": 0,
  "resource_info": {
    "size": null,
    "suffix": "pptx",
    "album_name": "string",
    "album_id": "string",
    "album_index": "0"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|sch_id|number|true|none|学校ID|none|
|resource_type|string|true|none|资源类型|none|
|download_times|number|true|none|下载次数|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|category|number|false|none|分类|enum: [2, 1]|
|period|string|false|none|学段|enum: ["初中", "高中", "小学"]|
|subject|string|false|none|学科|none|
|resource_id|string|true|none|资源ID|none|
|type|string|false|none|类型|none|
|press_version|string|false|none|教材版本|none|
|grade|string|false|none|年级|none|
|resource_name|string|false|none|resource名称|none|
|book_id|number|false|none|教材ID|none|
|chapter_id|number|false|none|章节ID|none|
|resource_info|object|false|none|resource_info|none|
|» size|null|false|none|大小|none|
|» suffix|string|false|none|suffix|enum: ["pptx", "docx"]|
|» album_name|string|false|none|album名称|none|
|» album_id|string|false|none|album的ID|none|
|» album_index|string|false|none|album_index|enum: ["0", -1, "1", "2"]|

#### 枚举值

|属性|值|
|---|---|
|download_times|1|
|category|2|
|category|1|
|period|初中|
|period|高中|
|period|小学|
|suffix|pptx|
|suffix|docx|
|album_index|0|
|album_index|-1|
|album_index|1|
|album_index|2|

<h2 id="tocS_user_log">user_log</h2>

<a id="schemauser_log"></a>
<a id="schema_user_log"></a>
<a id="tocSuser_log"></a>
<a id="tocsuser_log"></a>

```json
{
  "user_id": 0,
  "type": "u_login",
  "content": "",
  "time": "string",
  "ctime": "string",
  "utime": "string",
  "operator": {
    "id": 0,
    "name": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|type|string|true|none|类型|enum: ["u_login"]|
|content|object|true|none|内容|enum: [""]|
|» id|number|false|none|id|none|
|» type|string|false|none|类型|enum: ["manual"]|
|» vip_name|string|false|none|vip名称|enum: ["尊享会员卡", "校园版", "学校专业版"]|
|» vip_stime|number|false|none|vip_stime|none|
|» vip_etime|number|false|none|vip_etime|none|
|» remark|string|false|none|备注|enum: [""]|
|time|string|true|none|日期时间|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|operator|object|true|none|操作人|none|
|» id|number|true|none|id|none|
|» name|string|true|none|名称|none|

#### 枚举值

|属性|值|
|---|---|
|type|u_login|
|content||
|type|manual|
|vip_name|尊享会员卡|
|vip_name|校园版|
|vip_name|学校专业版|
|remark||

<h2 id="tocS_user_payment_resource">user_payment_resource</h2>

<a id="schemauser_payment_resource"></a>
<a id="schema_user_payment_resource"></a>
<a id="tocSuser_payment_resource"></a>
<a id="tocsuser_payment_resource"></a>

```json
{
  "user_id": 0,
  "transaction_id": "string",
  "order_id": "string",
  "type": 6,
  "resource_type": "string",
  "resource_id": 0,
  "resource_name": "string",
  "resource_image": "",
  "resource_info": {
    "category": 3,
    "host": "string",
    "url": "string",
    "exam_id": "string",
    "name": "string",
    "category_id": 0,
    "suffix": "pptx",
    "invalid": 0,
    "user_id": 5959698677104640,
    "user_name": "张蓓",
    "download_times": 0,
    "view_times": 0,
    "ctime": "string",
    "utime": "string",
    "vague_name": "",
    "period": "高中",
    "subject": "物理",
    "grade": "string",
    "press_version": "string",
    "province": "string",
    "city": "string",
    "from_year": 2024,
    "to_year": 2025,
    "big_ques_num": 0,
    "small_ques_num": 0,
    "from": "zwst",
    "sch_name": "string"
  },
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|transaction_id|string|true|none|transaction的ID|none|
|order_id|string|true|none|order的ID|none|
|type|number|true|none|类型|enum: [6, 4, 7, 8, 1, 5]|
|resource_type|string|true|none|资源类型|none|
|resource_id|number|true|none|资源ID|none|
|resource_name|string|true|none|resource名称|none|
|resource_image|string|true|none|resource_image|enum: [""]|
|resource_info|object|true|none|resource_info|none|
|» category|string|false|none|分类|enum: [3, 2, 1, 5, 6]|
|» host|string|false|none|域名|none|
|» url|string|false|none|资源地址|none|
|» exam_id|string|false|none|考试的ID|none|
|» name|string|false|none|名称|none|
|» category_id|number|false|none|category的ID|none|
|» suffix|string|false|none|suffix|enum: ["pptx", "docx", "doc", "xls"]|
|» invalid|number|false|none|invalid|enum: [0]|
|» user_id|number|false|none|用户ID|enum: [5959698677104640]|
|» user_name|string|false|none|用户名称|enum: ["张蓓"]|
|» download_times|number|false|none|下载次数|none|
|» view_times|number|false|none|view_times|none|
|» ctime|string|false|none|创建时间|none|
|» utime|string|false|none|更新时间|none|
|» vague_name|string|false|none|vague名称|enum: [""]|
|» period|string|false|none|学段|enum: ["高中", "小学", "初中"]|
|» subject|string|false|none|学科|enum: ["物理", "数学", "生物", "化学", "英语", "语文", "历史", "政治", "地理"]|
|» grade|string|false|none|年级|none|
|» press_version|string|false|none|教材版本|none|
|» province|string|false|none|省份|none|
|» city|string|false|none|城市|none|
|» from_year|number|false|none|from_year|enum: [2024, 2023, 2022]|
|» to_year|number|false|none|to_year|enum: [2025, 2024, 2023]|
|» big_ques_num|number|false|none|big_ques_num|none|
|» small_ques_num|number|false|none|small_ques_num|none|
|» from|string|false|none|来源|enum: ["zwst", "mkp", "tiku", "drm"]|
|» sch_name|string|false|none|sch名称|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

#### 枚举值

|属性|值|
|---|---|
|type|6|
|type|4|
|type|7|
|type|8|
|type|1|
|type|5|
|resource_image||
|category|3|
|category|2|
|category|1|
|category|5|
|category|6|
|suffix|pptx|
|suffix|docx|
|suffix|doc|
|suffix|xls|
|invalid|0|
|user_id|5959698677104640|
|user_name|张蓓|
|vague_name||
|period|高中|
|period|小学|
|period|初中|
|subject|物理|
|subject|数学|
|subject|生物|
|subject|化学|
|subject|英语|
|subject|语文|
|subject|历史|
|subject|政治|
|subject|地理|
|from_year|2024|
|from_year|2023|
|from_year|2022|
|to_year|2025|
|to_year|2024|
|to_year|2023|
|from|zwst|
|from|mkp|
|from|tiku|
|from|drm|

<h2 id="tocS_user_resource_cart">user_resource_cart</h2>

<a id="schemauser_resource_cart"></a>
<a id="schema_user_resource_cart"></a>
<a id="tocSuser_resource_cart"></a>
<a id="tocsuser_resource_cart"></a>

```json
{
  "user_id": 0,
  "items": "string",
  "ctime": "string",
  "utime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|items|string|true|none|items列表|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|

<h2 id="tocS_user_right">user_right</h2>

<a id="schemauser_right"></a>
<a id="schema_user_right"></a>
<a id="tocSuser_right"></a>
<a id="tocsuser_right"></a>

```json
{
  "user_id": 0,
  "order_id": "",
  "sku_id": "normal",
  "period": "all",
  "subject": "all",
  "subject_check": 0,
  "last_time": "string",
  "status": 1,
  "ctime": "string",
  "utime": "string",
  "group": "normal",
  "type": "string",
  "reset_type": "day",
  "limit": 0,
  "count": 0,
  "stime": "string",
  "etime": "string",
  "target": {
    "id": 4129251012,
    "type": "string",
    "parent_id": null
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user_id|number|true|none|用户ID|none|
|order_id|string|true|none|order的ID|enum: ["", "free"]|
|sku_id|string|true|none|sku的ID|enum: ["normal", "school"]|
|period|string|true|none|学段|enum: ["all", "高中", "初中", "小学"]|
|subject|string|true|none|学科|enum: ["all", "物理", "数学", "化学", "地理", "历史", "政治", "生物", "英语", "语文"]|
|subject_check|number|true|none|subject_check|enum: [0, 1]|
|last_time|string|true|none|last时间|none|
|status|number|true|none|状态|enum: [1]|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|group|string|true|none|group|enum: ["normal", "school"]|
|type|string|true|none|类型|none|
|reset_type|string|true|none|reset_type|enum: ["day", "fixed", "month"]|
|limit|number|true|none|limit|none|
|count|number|true|none|数量|none|
|stime|string|true|none|开始时间|none|
|etime|string|true|none|结束时间|none|
|target|object|false|none|目标/跳转目标|none|
|» id|number|true|none|id|enum: [4129251012, 2155699396, 701780932, 3050265540]|
|» type|string|true|none|类型|none|
|» parent_id|null|true|none|父级ID|none|

#### 枚举值

|属性|值|
|---|---|
|order_id||
|order_id|free|
|sku_id|normal|
|sku_id|school|
|period|all|
|period|高中|
|period|初中|
|period|小学|
|subject|all|
|subject|物理|
|subject|数学|
|subject|化学|
|subject|地理|
|subject|历史|
|subject|政治|
|subject|生物|
|subject|英语|
|subject|语文|
|subject_check|0|
|subject_check|1|
|status|1|
|group|normal|
|group|school|
|reset_type|day|
|reset_type|fixed|
|reset_type|month|
|id|4129251012|
|id|2155699396|
|id|701780932|
|id|3050265540|

<h2 id="tocS_user_teach_plan">user_teach_plan</h2>

<a id="schemauser_teach_plan"></a>
<a id="schema_user_teach_plan"></a>
<a id="tocSuser_teach_plan"></a>
<a id="tocsuser_teach_plan"></a>

```json
{
  "period": "高中",
  "name": "教学任务计划1",
  "subject": "生物",
  "grade": "高一",
  "book_version": "string",
  "stime": 0,
  "etime": 0,
  "init_week_num": 0,
  "weeks": [
    {
      "stime": 0,
      "etime": 0,
      "child": [
        {
          "name": "基因在染色体上",
          "category": "teach",
          "id": 0,
          "parent": {
            "id": null,
            "book_id": null,
            "book_name": null,
            "name": null
          },
          "papers": [
            "string"
          ],
          "reco_papers": [
            "string"
          ],
          "type": "月考"
        }
      ]
    }
  ],
  "deleted": 0,
  "user_id": 0,
  "ctime": "string",
  "utime": "string",
  "knowledge_tree_id": 2142371839,
  "knowledge_tree_name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|
|name|string|true|none|名称|enum: ["教学任务计划1"]|
|subject|string|true|none|学科|enum: ["生物", "数学", "物理", "语文", "化学", "政治", "地理", "历史", "英语"]|
|grade|string|true|none|年级|enum: ["高一", "七年级", "高二", "九年级", "八年级", "高三", "一年级", "六年级", "三年级", "7年级"]|
|book_version|string|true|none|book_version|none|
|stime|number|true|none|开始时间|none|
|etime|number|true|none|结束时间|none|
|init_week_num|number|true|none|init_week_num|none|
|weeks|[object]|true|none|weeks列表|none|
|» stime|number|true|none|开始时间|none|
|» etime|number|true|none|结束时间|none|
|» child|[object]|true|none|child列表|none|
|»» name|string|true|none|名称|enum: ["基因在染色体上", "伴性遗传", "DNA的结构", "月考", "DNA的复制", "染色体变异", "人类遗传病"]|
|»» category|string|true|none|分类|enum: ["teach", "exam"]|
|»» id|number|false|none|id|none|
|»» parent|object|true|none|parent|none|
|»»» id|number|true|none|id|enum: [3616866303, 3616800767, 3703767039, 3616735231, 0, 3616669695, 3616604159]|
|»»» book_id|number|false|none|教材ID|enum: [2006384639, 2009268223]|
|»»» book_name|string|false|none|book名称|enum: ["必修2", "必修1"]|
|»»» name|string|true|none|名称|enum: ["考试任务"]|
|»» papers|[string]|true|none|papers列表|none|
|»» reco_papers|[string]|true|none|reco_papers列表|none|
|»» type|string|false|none|类型|enum: ["月考"]|
|deleted|number|true|none|删除状态|enum: [0, 1]|
|user_id|number|true|none|用户ID|none|
|ctime|string|true|none|创建时间|none|
|utime|string|true|none|更新时间|none|
|knowledge_tree_id|number|false|none|knowledge_tree的ID|enum: [2142371839, 2142306303]|
|knowledge_tree_name|string|false|none|knowledge_tree名称|none|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小学|
|name|教学任务计划1|
|subject|生物|
|subject|数学|
|subject|物理|
|subject|语文|
|subject|化学|
|subject|政治|
|subject|地理|
|subject|历史|
|subject|英语|
|grade|高一|
|grade|七年级|
|grade|高二|
|grade|九年级|
|grade|八年级|
|grade|高三|
|grade|一年级|
|grade|六年级|
|grade|三年级|
|grade|7年级|
|name|基因在染色体上|
|name|伴性遗传|
|name|DNA的结构|
|name|月考|
|name|DNA的复制|
|name|染色体变异|
|name|人类遗传病|
|category|teach|
|category|exam|
|id|3616866303|
|id|3616800767|
|id|3703767039|
|id|3616735231|
|id|0|
|id|3616669695|
|id|3616604159|
|book_id|2006384639|
|book_id|2009268223|
|book_name|必修2|
|book_name|必修1|
|name|考试任务|
|type|月考|
|deleted|0|
|deleted|1|
|knowledge_tree_id|2142371839|
|knowledge_tree_id|2142306303|

<h2 id="tocS_yj_grade_mapping">yj_grade_mapping</h2>

<a id="schemayj_grade_mapping"></a>
<a id="schema_yj_grade_mapping"></a>
<a id="tocSyj_grade_mapping"></a>
<a id="tocsyj_grade_mapping"></a>

```json
{
  "yj_grade": "string",
  "grade": "string",
  "period": "高中"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|yj_grade|string|true|none|yj_grade|none|
|grade|string|true|none|年级|none|
|period|string|true|none|学段|enum: ["高中", "初中", "小学"]|

#### 枚举值

|属性|值|
|---|---|
|period|高中|
|period|初中|
|period|小学|

