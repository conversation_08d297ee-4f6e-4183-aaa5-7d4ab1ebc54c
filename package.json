{"name": "open_tiku_serv", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "********************:dnr-platform/open_tiku_serv.git"}, "author": "", "license": "ISC", "dependencies": {"@hapi/joi": "17.1.1", "axios": "^0.19.2", "body-parser": "^1.19.0", "compression": "^1.7.3", "config": "^3.3.1", "cookie-parser": "^1.4.3", "decompress": "^4.2.0", "decompress-unzip": "^4.0.1", "enfscopy": "^1.0.1", "express": "^4.17.1", "http-proxy-middleware": "^0.19.1", "ioredis": "^3.2.2", "jsdom": "^16.7.0", "jsonwebtoken": "^8.4.0", "lodash": "^4.17.21", "log4js": "^6.3.0", "marked": "^4.3.0", "mime": "^2.6.0", "moment": "^2.29.4", "mongodb": "^3.6.0", "node-xlsx": "^0.12.1", "qs": "^6.5.0", "rimraf": "^2.6.1", "underscore": "^1.8.3", "url": "^0.11.0", "uuid": "7.0.1", "xlsx": "^0.12.10", "xml2js": "^0.4.19", "zip-dir": "^1.0.2"}}