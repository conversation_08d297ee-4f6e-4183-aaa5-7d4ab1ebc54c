const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const client = require('../../../common/client');
const schema = require('../../../common/enums/schema');
const { TYPES, DIGIT_MAP_CHINESE } = require('../../../common/enums/template');
const enums = require('../../../common/enums/enums');
const BussError = require('../../../common/exceptions/BussError');
const subject_utils = require('../../../common/utils/subject_utils');
const question_utils = require('../../../common/utils/question_utils');
const paper_utils = require('../../../common/utils/paper_utils');
const exam_utils = require('../exam/exam_utils');
const logger = require('../../../common/lib/logger');

module.exports = {
    getExamPaperClasses,
    getExamPaperQuestions,
    putExamPaperComment,
    putExamPaperQuestionSim,
    searchExamPaperQuestion,
    putExamPaperQuestionAnswerMark,
    putExamPaperStatus,
    getCommentList,
    getClassCommentDetail,
    getExamPaperCategoryPaper,
}

async function getExamPaperClasses(params, user) {
    const { exam_id, paper_id } = params;
    const list = await client.hfsTeacherV3.getExamPaperClasses(user.id, exam_id, paper_id);
    return list.filter(e => e.relation === 1);
}

async function getExamPaperQuestions(params, user) {
    const user_id = user.id;
    const { exam_id, paper_id } = params;
    const rankExam = await client.rank.getExamById(exam_id);
    if (_.isEmpty(rankExam)) throw new BussError('考试不存在或者未同步!');
    const rankPaper = rankExam['[papers]'].find(e => e.id === paper_id);
    const zujuanId = rankPaper.zujuanId || '';
    const subject = subject_utils.regularSubject(rankPaper.subject) || rankPaper.subject;
    const { grade, period } = enums.PeriodGradeMapping.find(e => e.yj_grade === rankPaper.grade || e.grade === rankPaper.grade);
    const ypPaper = await client.yuanpei.getPaperQuestions(paper_id);
    if (_.isEmpty(ypPaper)) throw new BussError('未上传原卷');
    // if (!zujuanId) {
    //     ypPaper = await client.yuanpei.getPaperQuestions(paper_id);
    //     if (_.isEmpty(ypPaper)) throw new BussError('未上传原卷');
    // }
    let classes = await client.hfsTeacherV3.getExamPaperClasses(user.id, exam_id, paper_id);
    classes = classes.filter(e => e.relation === 1);
    if (!_.size(classes)) throw new BussError('不存在任课班级');
    const hfsQuestions = await client.hfsTeacherV3.getExamPaperQuestionDetailV2(classes[0].id, exam_id, paper_id, user.unify_sid);
    if (!_.size(hfsQuestions)) throw new BussError('未获取到试题信息');
    let exam = await getExam(user_id, exam_id, paper_id);
    if (_.isEmpty(exam)) { // 初始化
        exam = await initExam(user.id, exam_id, rankExam.name, paper_id,
            period, subject, grade || '', zujuanId || '',
            classes, hfsQuestions, ypPaper);
    }
    const questionIdSet = new Set();
    for (const ques of exam.draft.questions) {
        ques.sim.forEach(id => questionIdSet.add(id));
    }
    let kbQuestions = [];
    let questionMap = {};
    if (questionIdSet.size) { // 初始化相似题
        kbQuestions = await client.kb.getQuestionByIds([...questionIdSet]);
        questionMap = _.keyBy(kbQuestions, 'id');
    }

    const result = {
        id: exam._id.toString(),
        class_info: exam.draft.class_info,
        questions: [],
    }
    for (const hfsQuestion of hfsQuestions) {
        const question = _.assign({}, hfsQuestion);
        const exam_ques = exam.draft.questions.find(e => e.key === question.key);
        // 相似题
        if (_.size(kbQuestions)) {
            buildQuestionSim(question, exam_ques, hfsQuestion, questionMap);
        }
        // 教师标注信息
        buildQuestionMarkInfo(question, exam_ques, hfsQuestion);
        result.questions.push(question);
    }
    return result;
}

async function initExam(user_id, exam_id, exam_name, paper_id, period, subject, grade,
                        zujuanId, classes, hfsQuestions, ypPaper) {
    const date = new Date();
    const exam = {
        user_id,
        exam_id,
        exam_name: exam_name,
        paper_id,
        user_paper_id: zujuanId || '', // 组卷ID
        period,
        grade,
        subject,
        papers: [],
        draft: { // 草稿
            class_info: [],
            questions: []
        },
        formal: { // 正式
            class_info: [],
            questions: []
        },
        status: 'init',
        ctime: date,
        utime: date,
        valid: enums.BOOL.YES
    };
    // 初始化班级列表
    for (const cls of classes) {
        let courseware_name = (cls.name.endsWith('班') ? cls.name : `${cls.name}班`) + exam_name + '讲评课件';
        exam.draft.class_info.push({
            id: cls.id,
            name: cls.name,
            courseware_name,
            questions: []
        });
    }
    // 初始化试题数据
    for (const hfs_question of hfsQuestions) {
        const ques = {
            key: hfs_question.key,
            sim: [],
            excellent: [],
            mediocre: []
        };
        // 不初始化优秀作答
        // if (hfs_question.type === 1 && _.size(hfs_question['excellentAnswers'])) {
        //     ques.excellent.push(...(hfs_question['excellentAnswers'] || []).map(e => e.id));
        // }
        exam.draft.questions.push(ques);
        const scoreRate = _.get(hfs_question, 'scoringRate', {});
        for (const cls of exam.draft.class_info || []) {
            try {
                const clsRate = scoreRate[cls.name];
                if (clsRate && Number(clsRate) < hfs_question.gradeScoreRate) cls.questions.push(hfs_question.key);
            } catch (e) {

            }
        }
    }
    const exam_questions = await buildPaperRecommendQuestions(exam, ypPaper, hfsQuestions);
    if (_.size(exam_questions)) {
        exam.draft.questions = exam_questions;
        // await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: {questions: exam_questions}});
    }
    // 保存数据
    const insertResult = await db.collection(schema.user_exam_comment).insertOne(exam);
    exam._id = insertResult.insertedId;
    return exam;
}

async function putExamPaperComment(examParams, params, user) {
    const {exam_id, paper_id} = examParams;
    const exam = await getExam(user.id, exam_id, paper_id);
    if (_.isEmpty(exam)) throw new BussError('数据不存在');
    await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: {'draft.class_info': params}});
    return {id: exam._id.toString()};
}

async function putExamPaperQuestionSim(params, user) {
    const {exam_id, paper_id, key, ids} = params;
    const exam = await getExam(user.id, exam_id, paper_id);
    if (_.isEmpty(exam)) throw new BussError('数据不存在');
    exam.draft.questions.forEach(e => {
        if (e.key === key) {
            e.sim = ids;
        }
    });
    await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: {'draft.questions': exam.questions}});
    return await client.kb.getQuestionByIds(ids);
}

async function searchExamPaperQuestion(params, user) {
    const { exam_id, paper_id, offset, limit } = params;
    const exam = await getExam(user.id, exam_id, paper_id);
    const algo_params = {
        period: exam.period,
        subject: exam.subject,
        grade: exam.grade,
    };
    if (params.knowledges) algo_params.knowledges = params.knowledges;
    if (params.question_id) {
        algo_params.ques_id = String(params.question_id);
        algo_params.ques_from = 'kb';
    }
    const algo_result = await client.algo.recommendQuestions(algo_params);
    const result = {
        total: 0,
        list: []
    };
    if (_.isEmpty(algo_result)) return result;
    result.total = algo_result.total;
    const ids = algo_result.questions.slice(offset, offset + limit).map(e => e.id);
    result.list = await client.kb.getQuestionByIds(ids);
    question_utils.addShowTags(result.list);
    // for (const question of result.list) {
    //     const blocks = question['blocks'];
    //     delete blocks['solutions'];
    //     delete blocks['answers'];
    //     delete blocks['explanations'];
    // }
    return result;
}

async function putExamPaperQuestionAnswerMark(params, user) {
    const {exam_id, paper_id, key, type, student_id} = params;
    const exam = await getExam(user.id, exam_id, paper_id);
    if (_.isEmpty(exam)) throw new BussError('数据不存在');
    exam.draft.questions.forEach(e => {
        if (e.key === key) {
            const markInfo = e[type] || [];
            if (markInfo.includes(student_id)) {
                e[type] = markInfo.filter(m => m !== student_id);
            } else {
                markInfo.push(student_id);
                e[type] = markInfo;
            }
        }
    });
    await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: {'draft.questions': exam.draft.questions}});
    return {id: exam._id.toString()};
}

async function putExamPaperStatus(params, user) {
    const { exam_id, paper_id, status } = params;
    const exam = await getExam(user.id, exam_id, paper_id);
    const doc = {
        formal: exam.draft,
        status: 'done',
        utime: new Date(),
    }
    await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: doc});
    return {id: exam._id.toString()};
}


async function getCommentList(params, user) {
    const {offset, limit} = params;
    const query = {
        user_id: user.id,
        status: 'done'
    };
    const result = {
        total: 0,
        list: []
    };
    result.total = await db.collection(schema.user_exam_comment).find(query).count();
    if (!result.total) return result;
    const list = await db.collection(schema.user_exam_comment).find(query).sort({utime: -1}).skip(offset).limit(limit).toArray();
    for (const exam of list) {
        const data = _.pick(exam, ['exam_id', 'exam_name', 'paper_id', 'period', 'subject', 'grade', 'formal']);
        data.id = exam._id.toString();
        const classes = _.get(data, 'formal.class_info', []);
        data.class_info = [];
        (classes).forEach(e => {
            e['question_num'] = _.size(e.questions);
            delete e['questions'];
            if (e.question_num) data.class_info.push(e);
        });
        data.ctime = exam.ctime.getTime();
        data.utime = exam.utime.getTime();
        result.list.push(data);
    }
    return result;
}

async function getClassCommentDetail(params, user) {
    const {id, class_id, type} = params;
    const exam = await db.collection(schema.user_exam_comment).findOne({_id: new ObjectId(id), user_id: user.id});
    if (_.isEmpty(exam)) throw new BussError('数据不存在');
    const result = {
        class_info: null,
        questions: []
    };
    const classes = _.get(exam, `${type}.class_info`, []);
    const class_info = classes.find(e => e.id === class_id);
    if (!class_info || !class_info.questions || !_.size(class_info.questions)) return result;
    result.class_info = class_info;
    const hfsQuestions = await client.hfsTeacherV3.getExamPaperQuestionDetailV2(class_id, exam.exam_id, exam.paper_id, user.unify_sid);

    const questionIdSet = new Set();
    const questions = _.get(exam, `${type}.questions`, []);
    for (const question of questions) {
        (question.sim || []).forEach(e => {
            questionIdSet.add(e);
        });
    }
    const kbQuestions = await client.kb.getQuestionByIds([...questionIdSet]);
    const questionMap = _.keyBy(kbQuestions, 'id');
    for (const key of class_info.questions) {
        const hfsQuestion = hfsQuestions.find(e => e.key === key);
        const question = _.assign({}, hfsQuestion);
        const exam_ques = questions.find(e => e.key === key);
        // 相似题
        buildQuestionSim(question, exam_ques, hfsQuestion, questionMap);
        // 教师标注信息
        buildQuestionMarkInfo(question, exam_ques, hfsQuestion);
        result.questions.push(question);
    }
    return result;
}

async function getExamPaperCategoryPaper(params, user) {
    const {exam_id, paper_id, category} = params;
    let exam = await getExam(user.id, exam_id, paper_id);
    if (!_.isEmpty(exam) && _.size(exam.papers)) {
        const paper = await db.collection(schema.user_paper).findOne({_id: new ObjectId(exam.papers[0].id)});
        return [transformPaper(paper)];
    }

    if (_.isEmpty(exam)) {
        const rankExam = await client.rank.getExamById(exam_id);
        if (_.isEmpty(rankExam)) throw new BussError('考试不存在或者未同步!');
        const rankPaper = rankExam['[papers]'].find(e => e.id === paper_id);
        const zujuanId = rankPaper.zujuanId || '';
        const subject = subject_utils.regularSubject(rankPaper.subject) || rankPaper.subject;
        let classes = await client.hfsTeacherV3.getExamPaperClasses(user.id, exam_id, paper_id);
        classes = classes.filter(e => e.relation === 1);
        if (!_.size(classes)) throw new BussError('不存在任课班级');
        const hfsQuestions = await client.hfsTeacherV3.getExamPaperQuestionDetailV2(classes[0].id, exam_id, paper_id, user.unify_sid);
        if (!_.size(hfsQuestions)) throw new BussError('未获取到试题信息');
        const ypPaper = await client.yuanpei.getPaperQuestions(paper_id);
        if (_.isEmpty(ypPaper)) throw new BussError('未上传原卷');
        // let ypPaper = await client.yuanpei.getPaperQuestions(paper_id);
        // if (!zujuanId) {
        //     ypPaper = await client.yuanpei.getPaperQuestions(paper_id);
        //     if (_.isEmpty(ypPaper)) throw new BussError('未上传原卷');
        // }
        const { grade, period } = enums.PeriodGradeMapping.find(e => e.yj_grade === rankPaper.grade || e.grade === rankPaper.grade);
        exam = await initExam(user.id, exam_id, rankExam.name, paper_id,
            period, subject, grade || '', zujuanId || '',
            classes, hfsQuestions, ypPaper);
    }
    const paper = await buildExampaperByCategory(user, exam, category);
    if (!_.isEmpty(paper)) {
        exam.papers.push({
            id: paper._id.toString(),
            category,
            time: Date.now()
        });
        await db.collection(schema.user_exam_comment).updateOne({_id: exam._id}, {$set: {papers: exam.papers}});
    }
    return [transformPaper(paper)];
}


async function buildExampaperByCategory(user, exam, category) {
    // const yp_data = await client.yuanpei.getPaperQuestions(exam.paper_id);
    let algo_paper = null;
    if (!exam.user_paper_id) {
        const algo_params = {
            period: exam.period,
            subject: exam.subject,
            type: "平行组卷",
            difficulty: "平行卷",
            paper_id: exam.paper_id.split('-')[0]
        }
        // const algo_params = exam_utils.build_level_paper_params(exam, category);
        algo_paper = await client.algo.levelPaper(algo_params);
    } else { //
        // const tiku_paper = await _get_exampaper_by_id_async(exam.user_paper_id);
        // const algo_params = _build_create_paper_params(user, exam, category, tiku_paper);
        // algo_paper = await client.algo.detailTablePaper(algo_params);
    }
    if (_.isEmpty(algo_paper)) throw new BussError('平行组卷失败');
    const paper = await paper_utils.build_algo_paper(user.id, algo_paper);
    paper.type = '考后巩固';
    paper.period = exam.period;
    paper.grade = exam.grade;
    paper.subject = exam.subject;
    paper.category = category;
    paper.name = '平行追踪试卷';
    paper.source_type = enums.PaperSourceType.EXAM;
    paper.source = enums.PaperSourceType.EXAM;
    paper.source_id = exam.paper_id;
    paper.status = enums.PaperStatus.DONE;
    paper.exam_status = enums.ExamStatus.EDITABLE;
    paper.valid = enums.BOOL.YES;
    const insert_result = await db.collection(schema.user_paper).insertOne(paper);
    paper._id = insert_result.insertedId;
    return paper;
}


function _build_level_paper_params(user, exam, category) {
    const difficulty_text = {
        1: '简单卷',
        2: '中等卷',
        3: '培优卷',
    }
    const set = new Set();
    for (const q of exam.questions || []) {
        (q.same || []).forEach(id => set.add(id));
    }
    const params = {
        period: exam.period,                        // 必填，学段
        grade: exam.grade,                          // 必填，学段
        subject: exam.subject,                      // 必填，科目
        paper_id: exam.paper_id.split('-')[0],      // 必填，试卷ID
        difficulty: difficulty_text[category],      // 必填，简单卷，中等卷，培优卷
        filtered_ques: [...set],               // 必填，过滤试题ID，默认为[]
        // school_id: +(exam.paper_id.split('-')[1]),  // 选填，学校ID
        user_id: user.id.toString(),                           // 选填，用户ID
        // class_name: str,                         // 选填，班级名
        // min_year: int,                              // 选填，最小试题年份限制
    }
    return params;
}

function transformPaper(paper) {
    return {
        id: paper._id.toString(),
        name: paper.name,
        question_num: paper_utils.get_question_num(paper),
        grade: paper.grade || '',
        period: paper.period,
        subject: paper.subject,
        source: paper.source,
        type: paper.type,
        from_year: paper.from_year,
        to_year: paper.to_year,
        status: paper.status,
        exam_status: paper.exam_status,
        ctime: paper.ctime.getTime(),
        utime: paper.utime.getTime(),
        error: '',
        from_enum: enums.PaperFrom.GROUP
    };
}


async function getExam(user_id, exam_id, paper_id) {
    return await db.collection(schema.user_exam_comment).findOne({user_id, exam_id, paper_id});
}

function buildQuestionSim(question, examQuestion, hfsQuestion, questionMap) {
    question.sim = [];
    for (const id of examQuestion.sim) {
        const ques = questionMap[id];
        if (_.isEmpty(ques)) continue;
        question.sim.push(ques);
    }
}

function buildQuestionMarkInfo(question, examQuestion, hfsQuestion) {
    if (question.type !== 1) return;
    question.mark_info = {
        excellent: [],
        mediocre: [],
    };
    const answerSheet = hfsQuestion['answerSheet'] || [];
    for (const stuId of examQuestion.excellent) {
        const excellentAnswers  = hfsQuestion['excellentAnswers'] || [];
        let data = excellentAnswers.find(e => e.id === stuId);
        if (_.isEmpty(data)) {
            data = answerSheet.find(e => e.id === stuId);
        }
        if (_.isEmpty(data)) continue;
        question.mark_info.excellent.push(data);
    }
    for (const stuId of examQuestion.mediocre) {
        const data = answerSheet.find(e => e.id === stuId);
        if (_.isEmpty(data)) continue;
        question.mark_info.mediocre.push(data);
    }
}

async function buildPaperRecommendQuestions(exam, yp_paper, hfs_questions) {
    // const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    let kb_questions = [];
    // if (exam.user_paper_id) {
    //     const tiku_paper = await client.tiku.getPaperById(exam.user_paper_id);
    //     kb_questions = getTikuPaperQuestions(tiku_paper);
    // }
    const exam_questions = exam.draft.questions || [];
    if (!_.size(exam_questions)) return exam_questions;
    const params = [];
    const quest_map = new Map();
    for (const hfs_question of hfs_questions) {
        const param = {
            period: exam.period,
            subject: exam.subject,
            grade: exam.grade,
            // ques_id: yp_question.id, // kb question id
            // ques_from: 'yuanpei', // kb
            // ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
            recom_num: 2
        };
        if (exam.user_paper_id) {
            const index = hfs_questions.findIndex(e => e.id === hfs_question.id);
            const kb_question = kb_questions[index];
            if (_.isEmpty(kb_question) || quest_map.has(kb_question.id)) continue;
            param.ques_id = kb_question.id.toString();
            param.ques_from = 'kb';
            // param.ques_type = kb_question.type;
            quest_map.set(kb_question.id, []);
        } else {
            const yp_question = findYpQuestion(hfs_question, yp_paper);
            if (_.isEmpty(yp_question) || quest_map.has(yp_question.id)) continue;
            quest_map.set(yp_question.id, []);
            param.ques_id = yp_question.id;
            param.ques_from = 'yuanpei';
            param.ques_type = _get_hfs_question_type(exam.subject, hfs_question, yp_question);
        }
        params.push(param);
    }
    let algo_result = [];
    if (_.size(params)) {
        try {
            for (const arr of _.chunk(params, 10)) {
                const res = await Promise.all(arr.map(e => client.algo.recommendQuestions(e)));
                algo_result.push(...res);
            }
        } catch (e) {
            logger.error(e);
            throw new BussError('获取变式练习失败，请稍后重试!')
        }
        for (let i = 0; i < params.length; i++) {
            const param = params[i];
            quest_map.set(param.ques_id, (algo_result[i].questions || []).map(e => e.id));
        }
    }

    for (const hfs_question of hfs_questions) {
        let sim = [];
        if (exam.user_paper_id) {
            const index = hfs_questions.findIndex(e => e.id === hfs_question.id);
            const kb_question = kb_questions[index];
            sim = quest_map.get(kb_question.id.toString()) || [];
        } else {
            const yp_question = findYpQuestion(hfs_question, yp_paper);
            sim = quest_map.get(yp_question.id) || [];
        }
        const ques = exam_questions.find(e => e.key === hfs_question.key);
        if (!_.isEmpty(ques)) ques.sim = sim || [];
    }
    return exam_questions;
}

function findYpQuestion(hfs_question, yp_paper) {
    let result = null;
    if (_.isEmpty(yp_paper)) return result;
    for (const yp_question of yp_paper.questions || []) {
        const point = (yp_question.points || []).find(e => e.id.split('-')[1] === hfs_question.questionId.toString());
        if (!_.isEmpty(point)) {
            result = yp_question;
            break;
        }
    }
    return result;
}


function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}


