const response = require('../lib/response');
const code = require('../enums/code');
const jwt = require('jsonwebtoken');
const _ = require('lodash');
const logger = require('../lib/logger');

module.exports = {
    verify,
}

async function verify(req, res, next) {
    try {
        // const user_id = req.headers['user_id'] || req.headers['user-id'];
        // let user = {};
        // if (user_id) {
        //     user.id = _.padStart(user_id, 24, '0');
        //     user.school_id = 55553; // 临时写死
        // }
        // if (_.isEmpty(user)) {
        //     const unifyInfo = await decodeUnifyId(req.cookies['unify_sid']);
        //     if (!_.isEmpty(unifyInfo)) {
        //         user = _.assign(user, unifyInfo);
        //         user.id = _.padStart(user.userId, 24, '0');
        //         user.school_id = user.schoolId;
        //         user.unify_sid = req.cookies['unify_sid'];
        //     }
        // }
        let user = {};
        const unifyInfo = await decodeUnifyId(req.cookies['unify_sid']);
        if (!_.isEmpty(unifyInfo)) {
            user = _.assign(user, unifyInfo);
            user.id = _.padStart(user.userId, 24, '0');
            user.school_id = user.schoolId;
            user.unify_sid = req.cookies['unify_sid'];
        }
        if (_.isEmpty(user)) {
            return res.json(response.error(code.AUTH_ERROR, 'auth error'));
        }
        user.user_id = user.id;
        req.user = user;
        next();
    } catch (e) {
        logger.error(e);
        return res.json(response.error(code.AUTH_ERROR, 'auth error'));
    }
}

/**
 *
 * @param unify_sid
 * @returns {Promise<null|*>}
 */
async function decodeUnifyId(unify_sid) {
    if (!unify_sid) return null;
    try {
        // {
        //     loginWay: 'account',
        //     schoolId: 55553,
        //     userId: '****************',
        //     switch: false,
        //     unifyAuth: '11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2',
        //     key: '****************:*************',
        //     iat: **********,
        //     exp: **********,
        //     jti: '****************'
        // }
        // app_id: 11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2
        // public_key: 7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D
        // return jwt.verify(unify_sid, Buffer.from(Buffer.from('7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D', 'base64')));
        return jwt.decode(unify_sid, Buffer.from(Buffer.from('7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D', 'base64')));
    } catch (e) { // 过期或者错误数据
        logger.error(e);
        return null;
    }
}
