/**
 * For OPs to check the service is ok!
 */
'use strict';
const express = require('express');
const router = express.Router();
const service = require('../service/paper');
const { handler } = require('../../common/middlewares/request');

// 获取试卷分类
router.get('/categories', handler(1, service.getCategories));
// 试卷搜索
router.post('/by_search', handler(1, service.search));
// 获取筛选条件
router.get('/filters', handler(1, service.getFilters));
// 获取试卷详细
router.get('/:id', handler(1, service.getDetail));
// 根据ID下载
router.post('/:id/download', handler(1, service.download));
// 根据内容下载
router.post('/content_download', handler(1, service.downloadByContent));

module.exports = router;
