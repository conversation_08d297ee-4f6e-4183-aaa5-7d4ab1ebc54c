const express = require('express');
const router = express.Router();

const service = require('../../service/learning_analysis');
const { handler } = require('../../../common/middlewares/request');

// 获取知识点分析
router.post('/knowledge', handler(1, service.knowledge));
// 获取知识点原题
router.get('/questions', handler(1, service.knowledgeQuestions));
// 组卷
router.post('/paper', handler(1, service.knowledgePaper));

module.exports = router;
