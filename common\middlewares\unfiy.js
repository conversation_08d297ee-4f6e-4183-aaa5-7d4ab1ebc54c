const response = require('../lib/response');
const code = require('../enums/code');
const jwt = require('jsonwebtoken');

module.exports = {
    verify,
}

async function verify(req, res, next) {
    const cookie = req.cookies['unify_sid'];
    let user = null;
    if (cookie) {
        try {
            user = jwt.verify(cookie,Buffer.from(Buffer.from('11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2', 'base64')));
        } catch (e) { //
        }
    }
    if (!user) {
        return res.json(response.error(code.AUTH_ERROR, 'auth error'));
    }
    // 适配用户信息
    req.user = {
        user_id: user
    };
    next();
}

const str = 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.iLZR3qtcuPKpHiaweczf3s-qCoHEmPmOEWjt2EbZ5DRqco_gS9cuH4rRzBND-ltfuUkVXETc6hvnpIrnK4wbGA';

const u = jwt.verify(str,Buffer.from(Buffer.from('7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D', 'base64')));
console.log(u);

