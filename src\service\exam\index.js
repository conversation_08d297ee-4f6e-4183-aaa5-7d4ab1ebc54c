const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/exam');


module.exports = {
    get_list,
    get_exam_detail,
    get_class_detail,
    post_paper,
    getExamList,
    getExamPaperClasses,
    getExamPaperQuestionDetail,
    getExamPaperQuestionSame,
    getExamPaperQuestionAnswerImage,
    getExamPaperStudentQuestionAnswer,
    putExamPaperQuestionAnswerTag,
}



async function get_list(req, res) {
    const params = {
        user_id: req.user.id
    };
    return await model.get_list(params);
}

const JOI_GET_DETAIL = Joi.object({
    // user_id: Joi.string().required(),
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    user_paper_id: Joi.string().optional().allow(''),
});

async function get_exam_detail(req, res) {
    const params = await JOI_GET_DETAIL.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_exam_detail(params);
}

const JOI_GET_CLASS_DETAIL = Joi.object({
    // user_id: Joi.string().required(),
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().required(),
    user_paper_id: Joi.string().optional().allow(''),
});

async function get_class_detail(req, res) {
    const params = await JOI_GET_CLASS_DETAIL.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.get_class_detail(params);
}


const JOI_CREATE_PAPER = Joi.object({
    // user_id: Joi.string().required(),
    exam_id: Joi.string().required(),
    exam_name: Joi.string().required(),
    paper_id: Joi.string().required(),
    user_paper_id: Joi.string().optional(),
    class_id: Joi.string().optional().allow(''),
    class_name: Joi.string().optional().allow(''),
    category: Joi.array().items(Joi.number()).required().min(1),
    table: Joi.object({ // 细目表
        name: Joi.string().required(), // 名称
        period: Joi.string().required(), // 学段
        grade: Joi.string().optional().allow(''), // 年级
        subject: Joi.string().required(), // 学科
        blocks: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().required(),
            questions: Joi.array().items(Joi.object({
                type: Joi.string().required(),
                difficulty: Joi.string().required(),
                score: Joi.number().required(),
                period: Joi.string().required(), // 学段
                subject: Joi.string().required(), // 学科
                knowledges: Joi.array().items(Joi.object({
                    id: Joi.number().required(), // 知识点ID
                    name: Joi.string().required(), // 知识点名称
                })).required().min(1)
            })).required().min(1)
        })).required().min(1)
    }).required().unknown(true)
});


async function post_paper(req, res) {
    const params = await JOI_CREATE_PAPER.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.post_paper(params);
}

const JOI_EXAM_LIST = Joi.object({
    offset: Joi.string().required(),
    limit: Joi.string().required(),
    type: Joi.number().optional(),
});

async function getExamList(req, res) {
    const params = await JOI_EXAM_LIST.validateAsync(req.query);
    params.user_id = req.user.id;
    return await model.getExamList(params);
}

const JOI_GET_EXAM_PAPER_CLASSES = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperClasses(req, res) {
    const params = await JOI_GET_EXAM_PAPER_CLASSES.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getExamPaperClasses(params);
}

const JOI_GET_EXAM_PAPER_QUESTION_DETAIL = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    zujuanId: Joi.string().optional().allow(''),
});

async function getExamPaperQuestionDetail(req, res) {
    const params = await JOI_GET_EXAM_PAPER_QUESTION_DETAIL.validateAsync(_.assign(req.params, req.query));
    params.user_id = req.user.id;
    return await model.getExamPaperQuestionDetail(params);
}

const JOI_GET_EXAM_PAPER_QUESTION_SAME = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
});

async function getExamPaperQuestionSame(req, res) {
    const params = await JOI_GET_EXAM_PAPER_QUESTION_SAME.validateAsync(req.params);
    params.user_id = req.user.id;
    return await model.getExamPaperQuestionSame(params);
}

const JOI_GET_EXAM_PAPER_QUESTION_ANSWER_IMAGE = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    class_id: Joi.string().optional(),
    min: Joi.number().optional().default(-1),
    max: Joi.number().optional().default(-1)
});

async function getExamPaperQuestionAnswerImage(req, res) {
    const params = await JOI_GET_EXAM_PAPER_QUESTION_ANSWER_IMAGE.validateAsync(_.assign(req.params, req.query));
    params.user_id = req.user.id;
    return await model.getExamPaperQuestionAnswerImage(params);
}

const JOI_GET_EXAM_PAPER_STUDENT_QUESTION_ANSWER = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().required(),
    student_id: Joi.string().required(),
    question_id: Joi.string().required(),
});

async function getExamPaperStudentQuestionAnswer(req, res) {
    const params = await JOI_GET_EXAM_PAPER_STUDENT_QUESTION_ANSWER.validateAsync(_.assign(req.params, req.query));
    params.user_id = req.user.id;
    return await model.getExamPaperStudentQuestionAnswer(params);
}

const JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_TAG = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    type: Joi.string().required(),
    student_id: Joi.string().required()
});

async function putExamPaperQuestionAnswerTag(req, res) {
    const params = await JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_TAG.validateAsync(_.assign(req.params, req.body));
    params.user_id = req.user.id;
    await model.putExamPaperQuestionAnswerTag(params);
}
