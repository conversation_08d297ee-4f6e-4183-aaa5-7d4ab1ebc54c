---
title: 教师公开接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 教师公开接口

教师公开接口提供地理位置相关的公开服务。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取IP地域信息

GET /v1/teacher/region

根据客户端IP地址获取地理位置信息，用于统计分析和地域化服务。

### 请求参数

此接口无需传入参数，系统自动获取客户端IP地址。

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/teacher/region"
```

### 返回结果

#### 成功响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "country": "中国",
    "region": "华北地区", 
    "province": "北京",
    "city": "北京",
    "isp": "电信"
  }
}
```

#### IP无法解析

```json
{
  "code": 0,
  "msg": "成功",
  "data": null
}
```

### 返回数据结构

#### 成功时 (data不为null)

|名称|类型|必选|约束|说明|
|---|---|---|---|---|
|» code|integer|true|none|状态码，0表示成功|
|» msg|string|true|none|响应消息|
|» data|object|true|none|地域信息对象|
|»» country|string|true|none|国家名称|
|»» region|string|true|none|地区名称|
|»» province|string|true|none|省份名称（已去除"省"字符）|
|»» city|string|true|none|城市名称（已去除"市"字符）|
|»» isp|string|true|none|网络服务提供商|

#### IP无法解析时 (data为null)

|名称|类型|必选|约束|说明|
|---|---|---|---|---|
|» code|integer|true|none|状态码，0表示成功|
|» msg|string|true|none|响应消息|
|» data|null|true|none|IP地址无法解析时返回null|

### 业务逻辑说明

1. **IP提取**: 从HTTP请求头中提取客户端真实IP地址
2. **地域解析**: 使用IP2Region库解析IP对应的地理位置
3. **数据清洗**: 去除省市名称中的"省"、"市"等后缀
4. **结果返回**: 返回格式化后的地域信息

### 使用场景

- **统计分析**: 用户地域分布统计
- **内容推荐**: 基于地域的个性化推荐  
- **服务优化**: 地域化服务配置
- **风控分析**: 异常地域访问检测

### 技术实现

- 使用IP2Region离线IP地址库进行解析
- 支持IPv4地址解析
- 解析结果经过格式化处理
- 解析失败时优雅降级返回null

### 注意事项

- 此接口无需认证，任何客户端都可调用
- IP地址解析基于离线数据库，可能存在一定误差
- 对于内网IP或特殊IP可能无法解析
- 返回的地域信息仅供参考，不保证100%准确