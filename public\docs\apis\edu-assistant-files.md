---
title: 教育助手文件接口
language_tabs:
  - shell: Shell
search: true
code_clipboard: true
---

# 教育助手文件接口

教育助手文件接口提供教育资源文件管理功能，包括课件、教案、学案等教学资源的列表展示、详情查看、下载和统计功能。

Base URLs:
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com` 
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

## GET 获取资源文件列表

GET /v1/edu_assistant_files/list

获取教育资源文件列表，支持分页和多维度筛选。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|offset|query|number|是|分页偏移量|
|limit|query|number|是|每页数量|
|period|query|string|是|学段（小学/初中/高中）|
|subject|query|string|是|学科名称|
|type_id|query|string|是|资源类型ID（courseware/lesson_plan/study_plan）|
|book_id|query|number|否|教材ID|
|chapter_id|query|number|否|章节ID|
|sort_by|query|string|否|排序方式|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_assistant_files/list?offset=0&limit=20&period=初中&subject=数学&type_id=courseware&book_id=1001" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "total": 156,
    "list": [
      {
        "id": 12345,
        "name": "有理数概念课件",
        "host": "https://files.example.com",
        "url": "/courseware/math/rational_numbers.pptx",
        "download_times": 89,
        "view_times": 245,
        "ctime": 1698825600000,
        "period": "初中",
        "subject": "数学",
        "press_version": "人教版",
        "book_id": 1001,
        "book_name": "七年级数学上册",
        "chapter_id": 2001,
        "type_id": "courseware",
        "file": {
          "host": "https://files.example.com",
          "url": "/courseware/math/rational_numbers.pptx",
          "mime": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
          "size": 2048576
        }
      },
      {
        "id": 12346,
        "name": "有理数加法教案",
        "host": "https://files.example.com",
        "url": "/lesson_plan/math/rational_addition.docx",
        "download_times": 56,
        "view_times": 178,
        "ctime": 1698739200000,
        "period": "初中",
        "subject": "数学",
        "press_version": "人教版",
        "book_id": 1001,
        "book_name": "七年级数学上册",
        "chapter_id": 2001,
        "type_id": "lesson_plan",
        "file": {
          "host": "https://files.example.com",
          "url": "/lesson_plan/math/rational_addition.docx",
          "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "size": 1048576
        }
      }
    ]
  }
}
```

---

## GET 获取资源文件详情

GET /v1/edu_assistant_files/info

获取指定资源文件的详细信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|number/string|是|资源ID|
|from|query|number|否|来源类型，默认为1|
|Cookie|header|string|是|用户认证信息 unify_sid|

#### 来源类型说明

|值|说明|描述|
|---|---|---|
|1|系统资源|来自知识库的标准资源|
|2|用户资源|用户自己上传的资源|
|4|校本资源|学校本地化的资源|

### 请求示例

```shell
curl -X GET "http://localhost:8055/v1/edu_assistant_files/info?id=12345&from=1" \
  -H "Cookie: unify_sid=your_token_here"
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {
    "id": "12345",
    "name": "有理数概念课件",
    "type_id": "courseware",
    "period": "初中",
    "subject": "数学",
    "press_version": "人教版",
    "book_id": 1001,
    "book_name": "七年级数学上册",
    "chapter_id": 2001,
    "download_times": 89,
    "view_times": 246,
    "ctime": 1698825600000,
    "utime": 1698912000000,
    "file": {
      "host": "https://files.example.com",
      "url": "/courseware/math/rational_numbers.pptx",
      "mime": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "size": 2048576
    }
  }
}
```

### 错误示例

#### 资源不存在

```json
{
  "code": 4,
  "msg": "资源不存在",
  "data": ""
}
```

---

## POST 下载资源文件

POST /v1/edu_assistant_files/download

下载指定的教育资源文件。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|number/string|是|资源ID|
|type_id|body|string|是|资源类型ID|
|from|body|number|是|来源类型|
|content|body|string|否|HTML内容（用于HTML转文档）|
|Cookie|header|string|是|用户认证信息 unify_sid|

### 资源类型说明

|type_id|说明|文件类型|
|-------|---|--------|
|courseware|课件|PPT、PPTX等|
|lesson_plan|教案|DOC、DOCX等|
|study_plan|学案|DOC、DOCX等|

### 请求示例

```shell
curl -X POST "http://localhost:8055/v1/edu_assistant_files/download" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12345,
    "type_id": "courseware",
    "from": 1
  }'
```

### 返回结果

#### 成功响应

> 200 Response (Binary File)

响应头包含：
- `Content-Disposition`: attachment; filename=有理数概念课件.pptx
- `Content-Type`: application/octet-stream

返回文件的二进制流数据。

### 错误示例

#### 文件地址错误

```json
{
  "code": 4,
  "msg": "文件地址错误",
  "data": ""
}
```

#### 下载文件错误

```json
{
  "code": 4,
  "msg": "下载文件错误",
  "data": ""
}
```

---

## PUT 更新统计次数

PUT /v1/edu_assistant_files/times

更新资源文件的浏览或下载次数。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|body|number|是|资源ID|
|action|body|string|是|操作类型|
|Cookie|header|string|是|用户认证信息 unify_sid|

#### 操作类型说明

|action值|说明|
|--------|---|
|view_times|增加浏览次数|
|download_times|增加下载次数|

### 请求示例

```shell
curl -X PUT "http://localhost:8055/v1/edu_assistant_files/times" \
  -H "Cookie: unify_sid=your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12345,
    "action": "view_times"
  }'
```

### 返回结果

#### 成功响应

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": null
}
```

---

## 返回数据结构

### 资源文件信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|number/string|true|none|资源ID|唯一标识|
|name|string|true|none|资源名称|文件标题|
|type_id|string|true|none|资源类型|courseware/lesson_plan/study_plan|
|period|string|true|none|学段|小学/初中/高中|
|subject|string|true|none|学科|学科名称|
|press_version|string|true|none|教材版本|如人教版、苏教版等|
|book_id|number|true|none|教材ID|教材标识|
|book_name|string|true|none|教材名称|教材标题|
|chapter_id|number|false|none|章节ID|章节标识|
|download_times|number|true|none|下载次数|下载统计|
|view_times|number|true|none|浏览次数|查看统计|
|ctime|number|true|none|创建时间|时间戳|
|utime|number|false|none|更新时间|时间戳|

### 文件信息结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|host|string|true|none|文件主机|文件服务器地址|
|url|string|true|none|文件路径|文件相对路径|
|mime|string|true|none|文件类型|MIME类型|
|size|number|true|none|文件大小|字节数|

### 分页响应结构

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|total|number|true|none|总数量|符合条件的记录总数|
|list|array|true|none|数据列表|当前页数据|

## 错误码说明

|错误码|含义|说明|
|------|-----|-----|
|0|成功|请求处理成功|
|4|业务错误|业务逻辑处理错误|
|12|参数错误|请求参数格式错误或缺失必要参数|
|11|Cookie无效|用户登录状态过期|

## 业务逻辑说明

### 资源分类体系

系统支持三种主要的教育资源类型：
- **课件 (courseware)**: PPT、PPTX等演示文稿，用于课堂教学
- **教案 (lesson_plan)**: DOC、DOCX等文档，包含教学设计和流程
- **学案 (study_plan)**: DOC、DOCX等文档，供学生学习使用

### 资源来源管理

- **系统资源**: 来自知识库的标准化教育资源，质量有保障
- **用户资源**: 教师个人上传的资源，支持个性化教学
- **校本资源**: 学校定制的本地化资源，符合学校特色

### 统计功能

- **浏览次数**: 查看资源详情时自动增加，用于衡量资源受欢迎程度
- **下载次数**: 下载文件时自动增加，反映资源的实际使用情况
- **仅特定类型统计**: 只对课件和学案类型的资源统计下载次数

### 文件下载机制

1. **直接下载**: 支持各种格式的教育资源文件直接下载
2. **HTML转换**: 支持将HTML内容转换为Word文档下载
3. **文件名处理**: 自动进行URI编码，支持中文文件名
4. **默认格式**: HTML转换时默认使用DOCX格式

### 权限控制

- **认证要求**: 所有接口都需要用户认证
- **数据隔离**: 用户资源按用户隔离，只能访问自己的资源
- **公共资源**: 系统资源和校本资源对所有认证用户开放

### 性能优化

- **分页查询**: 支持分页查询，避免大数据量传输
- **缓存机制**: 资源信息支持缓存，提高响应速度
- **CDN支持**: 文件下载支持CDN加速，提升用户体验

### 错误处理

- **资源检查**: 下载前检查资源是否存在和文件地址是否有效
- **网络异常**: 处理远程文件下载失败的情况
- **参数验证**: 严格的参数验证，防止非法请求
- **日志记录**: 详细的错误日志，便于问题排查

### 使用场景

1. **教师备课**: 查找和下载课件、教案等教学资源
2. **资源分享**: 教师之间分享优质教学资源
3. **校本建设**: 学校构建本地化的教学资源库
4. **统计分析**: 通过下载和浏览统计分析资源使用情况
5. **移动教学**: 支持移动端访问和下载教学资源