const express = require('express');
const router = express.Router();
const service = require('../../service/exam');
const { handler } = require('../../../common/middlewares/request');

router.get('', handler(1, service.get_list));

// 获取列表
router.get('/detail', handler(1, service.get_exam_detail));
// 获取班级详细
router.get('/class', handler(1, service.get_class_detail));
// 组卷
router.post('/paper', handler(1, service.post_paper));

// 获取考试列表
router.get('/list', handler(1, service.getExamList));
// 获取关联考试班级列表
router.get('/:exam_id/paper/:paper_id/classes', handler(1, service.getExamPaperClasses));
// 获取考试详细
router.get('/:exam_id/paper/:paper_id/questions', handler(1, service.getExamPaperQuestionDetail));
// 获取相似题
router.get('/:exam_id/paper/:paper_id/question/:question_id/same', handler(1, service.getExamPaperQuestionSame));
// 获取试题答题卡信息
router.get('/:exam_id/paper/:paper_id/question/:question_id/answer/pictures', handler(1, service.getExamPaperQuestionAnswerImage));
// 获取指定试题学生作答
router.get('/:exam_id/paper/:paper_id/class/:class_id/student/:student_id/question/:question_id/brief', handler(1, service.getExamPaperStudentQuestionAnswer));
// 试题答题卡标记或取消标记
router.put('/:exam_id/paper/:paper_id/question/:question_id/answer/tag', handler(1, service.putExamPaperQuestionAnswerTag));




module.exports = router;
