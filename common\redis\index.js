const config = require('config');
const Redis = require('ioredis');
const logger = require('../lib/logger');

/**
 *
 */
class RedisClient {
    constructor(options) {
        this.client = new Redis(options);
        logger.info('redis init success');
    }
    async set(key, value, expireTime = null) {
        if (expireTime) {
            return this.client.set(key, value, 'EX', expireTime);
        } else {
            return this.client.set(key, value);
        }
    }

    async get(key) {
        return this.client.get(key);
    }

    async del(key) {
        return this.client.del(key);
    }

    async expire(key, seconds) {
        return this.client.expire(key, seconds);
    }

    async exists(key) {
        const result = await this.client.exists(key);
        return result === 1;
    }

    async ttl(key) {
        return this.client.ttl(key);
    }

    async quit() {
        return this.client.quit();
    }
}

// 导出单例实例
module.exports = new RedisClient(config.get('redis'));
