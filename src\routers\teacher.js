const express = require('express');
const router = express.Router();
const service = require('../service/teacher');
const { handler } = require('../../common/middlewares/request');

// 获取教师信息
router.get('/info', handler(1, service.getUserInfo));
// 获取统计信息
router.get('/profile', handler(1, service.getProfile));
// 获取好分数教师信息
router.get('/hfs/info', handler(1, service.getHfsTeacherInfo));
// 获取阅卷教师信息
router.get('/yj/info', handler(1, service.getYjTeacherInfo));
// 获取教师班级列表
router.get('/classes', handler(1, service.getClasses));
// 获取置顶班级学生
router.get('/class/:class_id/students', handler(1, service.getClassStudents));
// 保存用户属性
router.put('/property', handler(1, service.updateUserProperty));

module.exports = router;
