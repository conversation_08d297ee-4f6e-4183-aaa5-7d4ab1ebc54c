# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是一个基于 Node.js Express 的教育平台 API 服务（`open_tiku_serv`），提供题库和试卷管理功能。该服务集成了多个外部系统和数据库来提供教育内容服务。

## 核心架构

### 应用程序结构
- **入口点**: `bin/www` - 主应用程序启动脚本
- **应用配置**: `bin/app.js` - Express 应用设置，包含中间件、CORS 和路由配置
- **路由管理**: `src/routers/index.js` - 集中化路由注册，分为三个类别：
  - 公开路由（无需认证）: `/v1/xkw`, `/v1/teacher` 公开端点
  - 内部路由: `/v1/internal` 
  - 认证路由: 所有其他 `/v1/*` 端点都需要认证验证

### 三层架构
代码库遵循一致的三层模式：
- **路由层** (`src/routers/*`): HTTP 请求处理和路由定义
- **服务层** (`src/service/*`): 业务逻辑实现
- **模型层** (`src/models/*`): 数据访问和数据库操作

### 数据库架构
- **主数据库**: MongoDB `opentiku` - 主应用数据
- **辅助数据库**: MongoDB `kb_zyk` - 知识库数据
- **第三数据库**: MongoDB `jzl_jiaoyan` - 教研数据
- **缓存**: Redis 用于会话和性能优化
- **数据库访问**: `common/db/index.js` 中的自定义包装器提供 `collection()`, `zyk_collection()` 和 `jzl_collection()` 方法

### 认证系统
- 使用基于 JWT 的统一认证，通过 `unify_sid` cookie
- `common/middlewares/auth.js` 中的认证中间件解码用户信息和学校上下文
- 除公开路由外，所有路由都需要认证

## 开发命令

### 启动应用程序
```bash
# 启动服务器
node bin/www

# 安装依赖
npm install
```

### 代码质量检查（需要设置）
```bash
# 首先安装代码检查工具
npm install eslint babel-eslint --save-dev

# 运行代码检查
npx eslint .
```

### 配置
- 环境配置文件位于 `config/` 目录: `development.json`, `gray.json`, `production.json`, `test.json`
- 使用 `config` npm 包进行环境特定设置
- 默认开发端口: 8055

## 主要集成点

### 外部服务客户端
位于 `common/client/`，包含以下集成：
- `kb.js`, `kb_se.js`, `ai_kb.js` - 知识库服务
- `tiku.js` - 题库服务
- `boss.js`, `hfs.js` - 管理和文件服务
- `xkw.js` - 第三方教育平台
- `dtk.js` - 数据分析服务

### 通用工具
- **日志记录**: `common/lib/logger.js` - 基于 log4js 的日志记录，支持文件轮转
- **响应处理**: `common/lib/response.js` - 标准化 API 响应格式
- **错误处理**: `common/exceptions/BussError.js` - 业务错误处理
- **数据库**: `common/lib/mongodber.js` - MongoDB 操作包装器
- **Redis**: `common/redis/index.js` - Redis 客户端管理

## 重要模式

### 服务集成
添加新的外部服务集成时：
1. 在 `common/client/` 中按照现有模式创建客户端
2. 在环境配置文件中添加配置
3. 使用一致的错误处理和日志记录

### 路由添加
对于新的 API 端点：
1. 在 `src/routers/` 中定义路由
2. 在 `src/service/` 中实现业务逻辑
3. 在 `src/models/` 中创建数据模型
4. 在 `src/routers/index.js` 中注册路由

### 数据库操作
- 使用 `db.collection('name')` 访问主数据库
- 使用 `db.zyk_collection('name')` 访问知识库
- 使用 `db.jzl_collection('name')` 访问教研数据
- 数据库连接在 `bin/www` 中通过 `db.init()` 初始化

### 错误处理
- 业务错误应使用 `common/exceptions/BussError.js`
- API 响应应使用 `common/lib/response.js` 保持一致性
- 日志记录应使用配置的日志记录实例

### 文件编码规范
- **强制要求**: 所有新创建的文件必须使用 UTF-8 编码
- **中文支持**: 确保代码中的中文注释和字符串正确显示
- **验证方法**: 使用 `file filename` 命令验证文件编码为 UTF-8
- **编辑器设置**: 在编辑器中明确设置文件保存编码为 UTF-8
- **特别注意**: 
  - JavaScript 文件 (*.js) 必须为 UTF-8 编码
  - Markdown 文档 (*.md) 必须为 UTF-8 编码
  - JSON 配置文件 (*.json) 必须为 UTF-8 编码
  - 如发现编码问题，立即重新保存为 UTF-8 格式

### 参数校验规范 (Joi)
- **统一使用**: 所有 API 接口必须使用 Joi 进行参数校验
- **校验位置**: 在服务层函数开始时进行校验，使用 `await JOI_SCHEMA.validateAsync()`
- **命名规范**: 校验 Schema 使用 `JOI_` 前缀 + 大写描述，如 `JOI_CREATE_USER`
- **校验规则**: 
  - 必填参数使用 `.required()`
  - 可选参数使用 `.optional()`
  - 字符串长度限制使用 `.max()` 和 `.min()`
  - 枚举值使用 `.valid()` 方法
  - 数组类型使用 `.array().items()`
  - 使用 `.label()` 为字段添加中文说明
- **错误处理**: Joi 校验失败时会自动抛出错误，无需手动处理
- **示例**:
  ```javascript
  const JOI_CREATE_USER = Joi.object({
    name: Joi.string().required().max(50).label('用户名'),
    email: Joi.string().email().required().label('邮箱'),
    age: Joi.number().integer().min(1).max(150).optional().label('年龄'),
    type: Joi.string().valid('admin', 'user').required().label('用户类型')
  });
  ```

### API 文档规范 (JSDoc + Swagger)
- **必须添加**: 所有公开 API 接口必须添加完整的 JSDoc Swagger 注释
- **注释位置**: 在服务层函数定义之前添加 `@swagger` 注释
- **必要内容**: 
  - 接口路径和 HTTP 方法
  - 接口描述和用途
  - 标签分类 (tags)
  - 安全认证要求 (security)
  - 请求参数 (parameters) 或请求体 (requestBody)
  - 响应格式 (responses)
  - 数据模型 (components/schemas)
- **格式要求**:
  - 使用标准的 OpenAPI 3.0 规范
  - 包含详细的参数描述和示例
  - 定义完整的响应状态码
  - 枚举值必须完整列出
- **示例**:
  ```javascript
  /**
   * @swagger
   * /v1/users:
   *   post:
   *     summary: 创建用户
   *     description: 创建新的用户账户
   *     tags:
   *       - 用户管理
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - email
   *             properties:
   *               name:
   *                 type: string
   *                 description: 用户名
   *                 example: "张三"
   *     responses:
   *       200:
   *         description: 创建成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/User'
   */
  ```

### 用户身份信息规范 (req.user)
- **强制使用**: 所有需要用户身份信息的场景，必须使用 `req.user` 对象，禁止从接口参数获取
- **可用字段**: 认证中间件解析后的 `req.user` 包含以下字段：
  - `user_id`: 用户ID
  - `school_id`: 学校ID
  - 其他用户相关信息
- **使用原则**:
  - **用户ID获取**: 始终使用 `req.user.user_id`，不允许前端传递 `user_id` 参数
  - **学校ID获取**: 始终使用 `req.user.school_id`，不允许前端传递 `school_id` 参数
  - **数据隔离**: 所有数据查询必须基于 `req.user.school_id` 进行学校级别隔离
  - **权限控制**: 创建、修改、删除操作需要验证资源所属用户或学校
- **安全考虑**:
  - 防止用户伪造身份信息
  - 确保数据访问权限正确
  - 避免跨学校数据泄露
- **实现模式**:
  ```javascript
  // ✅ 正确：从 req.user 获取用户信息
  async function createResource(req, res) {
    const params = await JOI_SCHEMA.validateAsync(req.body);
    const resourceData = {
      ...params,
      user_id: req.user.user_id,        // 从认证信息获取
      school_id: req.user.school_id     // 从认证信息获取
    };
    return await model.create(resourceData);
  }
  
  // ✅ 正确：查询时自动过滤学校数据
  async function getResourceList(req, res) {
    const params = await JOI_SCHEMA.validateAsync(req.query);
    const queryParams = {
      ...params,
      school_id: req.user.school_id     // 强制学校隔离
    };
    // 可选：添加用户筛选逻辑
    if (params.my_only) {
      queryParams.user_id = req.user.user_id;
    }
    return await model.getList(queryParams);
  }
  
  // ❌ 错误：不允许从参数获取用户身份信息
  const JOI_SCHEMA = Joi.object({
    user_id: Joi.string(),     // 禁止
    school_id: Joi.number()    // 禁止
  });
  ```
- **筛选用户数据**: 如需按创建用户筛选数据，使用布尔型参数控制：
  ```javascript
  // 推荐方式：使用 my_only 等语义化参数
  const JOI_LIST_SCHEMA = Joi.object({
    my_only: Joi.boolean().optional().default(false).label('只显示我创建的'),
    my_school: Joi.boolean().optional().default(true).label('只显示我校的')
  });
  ```

### 分页查询规范
- **统一使用 offset + limit**: 所有列表查询接口必须使用 `offset` + `limit` 分页模式，禁止使用 `page` + `limit`
- **参数规范**:
  - `offset` (number): 偏移量，表示跳过的记录数，最小值为 0，默认值为 0
  - `limit` (number): 每页数量，表示返回的记录数，范围 1-100，默认值为 20
- **Joi 校验规范**:
  ```javascript
  const JOI_LIST_SCHEMA = Joi.object({
    offset: Joi.number().integer().min(0).optional().default(0).label('偏移量'),
    limit: Joi.number().integer().min(1).max(100).optional().default(20).label('每页数量'),
    // 其他查询参数...
  });
  ```
- **数据库查询实现**:
  ```javascript
  // ✅ 正确：使用 offset 进行分页
  async function getList(params) {
    const { offset = 0, limit = 20, ...filters } = params;
    const skip = parseInt(offset);  // 直接使用 offset 作为 skip 值
    const total = await collection.countDocuments(query);
    const list = await collection.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .toArray();
    return { list, total, offset: parseInt(offset), limit: parseInt(limit) };
  }
  
  // ❌ 错误：不要使用 page 分页
  const skip = (page - 1) * limit;  // 禁止
  ```
- **响应格式规范**:
  ```javascript
  // ✅ 标准分页响应格式
  {
    "code": 0,
    "message": "success", 
    "data": {
      "list": [...],        // 数据列表
      "total": 100,         // 总记录数
      "offset": 0,          // 当前偏移量
      "limit": 20           // 当前页大小
    }
  }
  ```
- **前端使用方式**:
  ```javascript
  // 第一页
  GET /api/resource?offset=0&limit=20
  
  // 第二页  
  GET /api/resource?offset=20&limit=20
  
  // 第三页
  GET /api/resource?offset=40&limit=20
  ```
- **优势说明**:
  - **更直观**: offset 直接表示跳过的记录数，避免页码计算
  - **更灵活**: 支持任意偏移量，不受页大小限制
  - **更高效**: 数据库原生支持 skip，无需额外计算
  - **更标准**: 符合 REST API 最佳实践

## 环境配置

应用程序使用环境特定的 JSON 配置文件。主要配置部分：
- `app`: 应用程序设置（名称、端口、URL）
- `db`: 所有数据库的 MongoDB 连接字符串
- `redis`: Redis 连接设置
- 外部服务配置（多个 API 和服务器）
- `cors`: 跨域请求允许的来源
- `log4js`: 日志配置，支持文件轮转

## 文件上传/下载
- 为 `/download/xlsx` 端点配置静态文件服务
- 文件上传大小限制设置为 5MB，适用于 JSON、URL 编码和原始 body 类型

## API 文档

### 文档结构
项目包含完整的 API 文档，位于 `public/docs/` 目录：
- **主文档**: `api-index.md` - API 文档总览，包含所有接口的索引和统计
- **接口文档**: `apis/` 目录下的各模块文档，按功能分类

### 文档组织
API 文档按照以下模块组织：
1. **公开接口** (无需认证)
   - 学科网公开接口 (`xkw-public.md`)
   - 教师公开接口 (`teacher-public.md`)

2. **内部接口** (内部调用)
   - 内部调用接口 (`internal.md`)

3. **认证接口** (需要JWT认证)
   - **核心教学模块**: 健康检查、教材、题目、知识树、试题篮、试卷、用户试卷、考试等
   - **用户管理模块**: 教师、学校、地区管理
   - **资源管理模块**: 备课资源、教育助手文件、双向细目表、智能组卷
   - **教学计划模块**: 教学计划和集体备课
   - **分析统计模块**: 学习分析和考情分析
   - **第三方集成模块**: 外部平台集成

### 接口统计
- **路由模块总数**: 28个
- **API接口总数**: 270+个
- **支持HTTP方法**: GET, POST, PUT, DELETE

### 环境配置
API 服务在不同环境下使用不同的 Base URL：
- **测试环境**: `https://devopen-tiku-serv-wan.yunxiao.com`
- **灰度环境**: `https://grayopen-tiku-serv-wan.yunxiao.com`
- **正式环境**: `https://open-tiku-serv-wan.yunxiao.com`

### 文档特性
- 使用 Markdown 格式，支持代码高亮
- 包含详细的请求参数、响应格式和示例
- 提供 Shell 命令示例（curl）
- 统一的错误码和响应格式说明

### 最近更新
- 智能组卷接口从第三方集成模块移至资源管理模块
- 新增独立的智能组卷文档 (`exampaper.md`)，包含6个接口
- 移除所有 JavaScript 格式的请求示例，仅保留 Shell 示例
- 更新所有文档的 Base URL 为环境特定的地址