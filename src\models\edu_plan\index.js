const config = require('config');
const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;

const db = require('../../../common/db');
const schema = require('../../../common/enums/schema');
const enums = require('../../../common/enums/enums');
const utils = require('../../../common/utils/utils');
const client = require('../../../common/client');
const BussError = require('../../../common/exceptions/BussError');
const jzl = require('../jzl/index');
const paper_utils = require('../../../common/utils/paper_utils');
const questionModel = require('../question');
const zykModel = require('../zyk');

module.exports = {
    getByParams,
    getById,
    getLessonItems,
    save,
    getChapterPapers,
    getChapterResource,
    getChapterLessonResource,
}

// operator
// creator
// pBranch
// createdAt
// updatedAt

async function getByParams(params) {
    const { period, subject, grade, user } = params;
    let { from_year, to_year, semester } = params;
    const schoolYearInfo = utils.getSemesterAndYear();
    // if (!from_year) from_year = schoolYearInfo.from_year;
    if (!to_year) to_year = schoolYearInfo.to_year;
    if (!semester) semester = schoolYearInfo.semester;
    const query = {
        school_id: user.school_id,
        period,
        subject,
        grade,
        // from_year,
        to_year,
        semester,
        deleted: enums.BOOL.NO
    }
    const result = await db.jzl_collection(schema.edu_plan).findOne(query);
    jzl.handlerData(result);
    return result;
}

async function getById(params) {
    const {id, user} = params;
    const result = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    jzl.handlerData(result);
    return result;
}
// 获取课时资源选项
async function getLessonItems(params, user) {
    const {id} = params;
    const result = [];
    const plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(plan)) return result;
    const prep_list = await db.jzl_collection(schema.team_prep).find({
        edu_plan_id: id,
        status: enums.TeamPrepStatus.DONE,
        deleted: enums.BOOL.NO
    }).toArray();
    if (!_.size(prep_list)) return result;
    let lessons = utils.flattenTree(plan.children).filter(e => e.key === enums.EduPlanChapterKey.LESSON);
    if (!_.size(lessons)) return result;
    const lesson_data = await db.jzl_collection(schema.team_prep_data).find({
        team_prep_id: {$in: prep_list.map(e => e._id.toString())},
        deleted: enums.BOOL.NO
    }).sort({updatedAt: -1}).toArray();
    for (const lesson of lessons) {
        const list = lesson_data.filter(e => e.edu_plan_chapter_id === lesson.id);
        const set = new Set();
        if (_.size(list)) list.forEach(e => set.add(e.key));
        lesson.items = [...set];
    }
    return lessons;
}

async function save(params, user) {
    const schoolYearInfo = utils.getSemesterAndYear();
    const jzlUser = await jzl.getUser(user);
    const {id} = params;
    if (id) {
        const curr = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(params.id)});
        if (_.isEmpty(curr)) throw new BussError('教学计划不存在');
        params.updatedAt = new Date();
        jzl.addUpdateBaseInfo(jzlUser, params);
        await syncChapter(jzlUser, params, curr);
        await db.jzl_collection(schema.edu_plan).updateOne({_id: new ObjectId(id)}, {$set: params});
        return {id};
    } else {
        const query = {
            school_id: user.school_id,
            period: params.period,
            subject: params.subject,
            grade: params.grade,
            // from_year: params.from_year,
            to_year: params.to_year,
            semester: params.semester
        }
        const result = await db.jzl_collection(schema.edu_plan).findOne(query);
        if (!_.isEmpty(result)) throw new BussError('不能重复创建');
        if (!params.to_year) params.to_year = schoolYearInfo.to_year;
        if (!params.from_year) params.from_year = params.to_year - 1;
        if (!params.semester) params.semester = schoolYearInfo.semester;
        params.school_id = user.school_id;
        jzl.addInsertBaseInfo(jzlUser, params);
        await syncChapter(jzlUser, params);
        const res = await db.jzl_collection(schema.edu_plan).insertOne(params);
        return {id: res.insertedId.toString()};
    }
}

async function syncChapter(jzlUser, data, old) {
    if (!_.size(data.children)) return;
    const chapters = utils.flattenTree(data.children);
    for (const chapter of chapters) {
        if (!chapter.id) {
            const doc = {
                name: chapter.name,
                key: chapter.key,
                // source_id: chapter.source_id || '',
                book_id: chapter.book_id,
                chapter_id: chapter.chapter_id,
                index: chapter.index || (chapter.key === enums.EduPlanChapterKey.LESSON ? 1 : 0)
            };
            jzl.addInsertBaseInfo(jzlUser, doc);
            const res = await db.jzl_collection(schema.edu_plan_chapter).insertOne(doc);
            chapter.id = res.insertedId.toString();
        }
    }
    // 处理删除的节点
    if (!_.isEmpty(old)) {
        const oldChapters = utils.flattenTree(old.children);
        const delIds = [];
        for (const chapter of oldChapters) {
            const data = chapters.find(e => e.id === chapter.id);
            if (_.isEmpty(data)) delIds.push(chapter.id);
        }
        if (_.size(delIds)) {
            await db.jzl_collection(schema.edu_plan_chapter).updateMany(
                {_id: {$in: delIds.map(e => new ObjectId(e))}},
                {$set: {deleted: enums.BOOL.YES, updatedAt: new Date()}}
            );
        }
    }
}

async function getChapterPapers(params, user) {
    const {id, team_prep_id, chapter_id, lesson_id } = params;
    let data = null;
    if (team_prep_id) { // 优先集备
        data = await db.jzl_collection(schema.team_prep).findOne({_id: new ObjectId(team_prep_id)});
    } else {
        data = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    }
    // const plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(data)) throw new BussError('教学计划不存在');
    const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === data.grade || e.grade === data.grade);
    if (!gradeInfo) throw new BussError(`年级[${data.grade}]无法转换成标准年级`);
    const chapter = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(chapter_id)});
    if (_.isEmpty(data) || chapter.key === enums.EduPlanChapterKey.LESSON)  throw new BussError('教学计划章节不存在');
    const root_chapter_id = utils.findRootId(data.children, chapter_id);
    const root_chapter = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(root_chapter_id)});

    const algo_params = {
        period: data.period,
        subject: data.subject,
        type: '同步练习',
        grade: gradeInfo.grade,
        term: data.semester,
        trees: [],
        ai_paper: 0,
        real_paper: 10,
        school_id: data.school_id
    };
    if (root_chapter.key === enums.EduPlanChapterKey.EXAM) {
        algo_params.type = enums.EduPlanExamTypeMapping[root_chapter.name] || root_chapter.name;
    }
    const result = {
        total: 0,
        list: []
    };
    // if (chapter.source_id) {
    //     const sync = 'sync';
    //     const book_chapter = await db.jzl_collection(schema.jzl.book_chapter).findOne({_id: new ObjectId(chapter.source_id)});
    //     if (!_.isEmpty(book_chapter) && book_chapter.source === sync) {
    //         const book = await db.jzl_collection(schema.jzl.book).findOne({_id: book_chapter.book});
    //         if (_.isEmpty(book)) return result;
    //         const press_version = await db.jzl_collection(schema.jzl.press_version).findOne({_id: book.press_version});
    //         if (_.isEmpty(press_version)) return result;
    //         if (book.source === sync) {
    //             let knowledge = await db.jzl_collection(schema.jzl.knowledge).find({_id: {$in: book_chapter.knowledges}}).toArray();
    //             knowledge = _.chain(knowledge).filter(e => e.source === sync).map(e => {
    //                 return {
    //                     id: e.source_id,
    //                     name: e.name,
    //                     key: 'knowledge'
    //                 };
    //             }).value();
    //             const chapters = findNodePath(book.children, book_chapter._id.toString(), knowledge);
    //             algo_params.trees.push({
    //                 id: book.source_id,
    //                 name: book.name,
    //                 type: 'book',
    //                 version: press_version.name,
    //                 children: [chapters] || []
    //             })
    //         }
    //     }
    // }

    if (chapter.book_id) {
        const book = await client.kb.getBookDetail({book_id: chapter.book_id});
        if (!_.isNumber(book)) {
            algo_params.trees.push({
                type: 'book',
                id: book.book_id,
                name: book.grade,
                version: book.press_version,
                children: [findNodePath(book.book.children, chapter.chapter_id)]
            })
        }
    }

    const algo_papers = await client.algo.recommendSyncPaper(algo_params);
    if (_.isEmpty(algo_papers)) return result;
    const real_papers = algo_papers.real_papers || [];
    result.total = _.size(real_papers);
    if (!result.total) return result;
    result.list = await client.kb.getPaperByIds(real_papers.map(e => e.id));
    return result;
}

async function getChapterResource(params, user) {
    const {id, chapter_id, key} = params;
    const plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(plan)) throw new BussError('教学计划不存在');
    const chapter_list = utils.flattenTree(plan.children);
    const result = [];
    const chapter = chapter_list.find(e => e.id === chapter_id);
    if (_.isEmpty(chapter)) return result;
    let lessons = utils.flattenTree(chapter.children).filter(e => e.key === enums.EduPlanChapterKey.LESSON);
    if (chapter.key === enums.EduPlanChapterKey.LESSON) {
        lessons = [chapter];
    }
    if (!_.size(lessons)) return result;
    const root_chapter_id = utils.findRootId(plan.children, chapter.id);
    const root_chapter = plan.children.find(e => e.id === root_chapter_id);
    // const prep_list = await db.jzl_collection(schema.team_prep).find({
    //     edu_plan_id: id,
    //     status: enums.TeamPrepStatus.DONE,
    //     deleted: enums.BOOL.NO
    // }).toArray();
    // if (!_.size(prep_list)) return result;
    const list = await db.jzl_collection(schema.team_prep_data).find({
        // team_prep_id: {$in: prep_list.map(e => e._id.toString())},
        edu_plan_chapter_id: {$in: lessons.map(e => e.id)},
        status: enums.TeamPrepStatus.DONE,
        deleted: enums.BOOL.NO
    }).sort({updatedAt: -1}).toArray();
    for (const lesson of lessons) {
        const lesson_data = list.find(e => e.edu_plan_chapter_id === lesson.id);
        const data = {
            id: lesson.id,
            name: lesson.name,
            index: lesson.index,
            children: []
        };
        if (!_.isEmpty(lesson_data)) {
            const paper_info = _.pick(plan, ['period', 'subject', 'grade', 'from_year', 'to_year']);
            paper_info.from_enum = enums.PaperFrom.JYZY;
            paper_info.type = '同步练习';
            if (root_chapter.key === enums.EduPlanChapterKey.EXAM) {
                paper_info.type = enums.EduPlanExamTypeMapping[root_chapter.name] || root_chapter.name;
            }
            paper_info.ques_num = _.size(lesson_data.children);
            data.children.push({
                ...paper_info,
                key: key,
                name: `第${utils.numberToChinese(lesson.index || 1)}课时 ${lesson.name}作业`,
            });
        }
        result.push(data);
    }
    return result;
}

async function getChapterLessonResource(params, user) {
    const {id, chapter_id, lesson_id } = params;
    const plan = await db.jzl_collection(schema.edu_plan).findOne({_id: new ObjectId(id)});
    if (_.isEmpty(plan)) throw new BussError('教学计划不存在');
    // const prep_list = await db.jzl_collection(schema.team_prep).find({
    //     edu_plan_id: id,
    //     status: enums.TeamPrepStatus.DONE,
    //     deleted: enums.BOOL.NO
    // }).toArray();
    const result = {
        total: 0,
        list: []
    }
    // if (!_.size(prep_list)) return result;
    const list = await db.jzl_collection(schema.team_prep_data).find({
        // team_prep_id: {$in: prep_list.map(e => e._id.toString())},
        edu_plan_chapter_id: lesson_id,
        status: enums.TeamPrepStatus.DONE,
        deleted: enums.BOOL.NO
    }).sort({updatedAt: -1}).toArray();
    const data_resource = list.find(e => _.size(e.children));
    if (_.isEmpty(data_resource)) return result;
    // 插入试题
    const kb_ids = [];
    const zx_ids = [];
    const zyk_ids = [];
    const ques_map = {};
    for (const question of data_resource.children) {
        if (question.source === enums.QuestionSource.ZX) {
            zx_ids.push(question.id);
        } else if (question.source === enums.QuestionSource.ZYK) {
            zyk_ids.push(question.id);
        } else {
            if (_.isNumber(question.id)) kb_ids.push(question.id);
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestionByIds(kb_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const questions = await questionModel.getByIds(zx_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    if (_.size(zyk_ids)) {
        const questions = await zykModel.getQuestionByIds(zyk_ids);
        questions.forEach(q => ques_map[q.id] = q);
    }
    const lesson_node = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(lesson_id)});
    const basket = paper_utils.init(user.id);
    basket.name = `第${utils.numberToChinese(lesson_node.index || 1)}课时 ${lesson_node.name}作业`;
    basket.period = plan.period;
    basket.subject = plan.subject;
    basket.grade = plan.grade;
    basket.type = '同步练习';
    const root_chapter_id = utils.findRootId(plan.children, lesson_id);
    const root_chapter = await db.jzl_collection(schema.edu_plan_chapter).findOne({_id: new ObjectId(root_chapter_id)});
    if (root_chapter.key === enums.EduPlanChapterKey.EXAM) {
        basket.type = enums.EduPlanExamTypeMapping[root_chapter.name] || root_chapter.name;
    }
    for (const q of data_resource.children) {
        let question = ques_map[q.id];
        if (_.isEmpty(question)) continue;
        const ques = _.assign({}, question);
        ques.source = q.source;
        ques.source_id = q.source_id;
        if (q.score) ques.score = q.score;
        paper_utils.insert_questions(basket, ques);
    }
    paper_utils.traverse_questions(basket, ques_map);
    paper_utils.render_basket(basket);
    result.total = 1;
    result.list.push(basket);
    return result;
}

function findNodePath(tree, targetId) {
    for (const node of tree) {
        if (node.id === targetId) {
            // 找到目标节点，返回精简后的节点
            let chapters = [];
            if (node.children) chapters = utils.flattenTree(node.children);
            for (const c of chapters) {
                delete c.importance;
                delete c.chance;
                delete c.score;
                delete c.ques_num;
            }
            return {
                id: node.id,
                name: node.name,
                key: node.key,
                children: node.children
            };
        }

        if (node.children && node.children.length > 0) {
            const childResult = findNodePath(node.children, targetId);
            if (childResult) {
                // 如果子节点中找到目标，构建精简后的父节点
                return {
                    id: node.id,
                    name: node.name,
                    key: node.key,
                    children: [childResult]
                };
            }
        }
    }
    return null;
}
