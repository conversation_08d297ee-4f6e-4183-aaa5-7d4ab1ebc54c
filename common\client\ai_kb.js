const config = require('config');
const axios = require('axios');
const URL = require('url');
const qs = require('querystring');
const _ = require('lodash');
const server = config.get('ai_kb_server');
const logger = require('../lib/logger');

module.exports = {
    filterRepeatQues,
}

async function filterRepeatQues(ids) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/ai_dispatch/v1/exam/ctb_dup',
        search: qs.stringify({
            appKey: server.appKey,
        })
    });
    try {
        let body = {}
        body['sim_ques_ids'] = ids;

        const result = await axios.post(url, JSON.stringify([body]));
        let saveQues = []
        let filterQues = [];
        if (result.data && result.data.code === 0 && result.data.data){
            let repeatArray = result.data.data.repeat_array || [];
            repeatArray.forEach(element => {
                let repeatQues = [];
                // 一组重复的试题索引集合
                element.forEach(indexDouble => {
                    indexDouble = indexDouble.replace(':', '')
                    let indexList = indexDouble.split('_')
                    indexList.forEach(index => {
                        if (!repeatQues.includes(index)){
                            repeatQues.push(index)
                        }
                    })
                });
                // 保留最后一个索引，删除之前的索引
                if (repeatQues.length !== 0){
                    filterQues = filterQues.concat(repeatQues.slice(0, -1));
                }
            })
        }

        // 过滤后保留的试题
        if (filterQues.length !== 0){
            for (let i=0; i< ids.length; i++){
                if (filterQues.includes(i.toString())){
                    continue
                }
                saveQues.push(ids[i])
            }
        }
        if (saveQues.length === 0){
            saveQues = ids
        }
        return saveQues;
    } catch (e) {
        logger.error('获取知识点相关试卷异常：', e);
        return [];
    }
}
