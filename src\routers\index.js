
const _ = require('lodash');
const auth = require('../../common/middlewares/auth');

const logger = require('../../common/lib/logger');
const response = require('../../common/lib/response');
const health = require('./health');
const book = require('./book');
const question = require('./question');
const question_public = require('./question_public');
const knowledge_tree = require('./knowledge_tree');
const basket = require('./basket');
const paper = require('./paper');
const user_paper = require('./user_paper');
const user_paper_public = require('./user_paper_public');
const region = require('./region');
const exam = require('./exam/exam');
const tw_specifications = require('./tw_specifications');
const prep_resource = require('./prep/resource');
const prep_basket = require('./basket/prep_basket');
const user_prep_catalog = require('./user/user_prep_catalog');
const user_prep_resource = require('./user/user_prep_resource');
const school_zyk = require('./school/school_zyk');
const teacher = require('./teacher');
const teacher_public = require('./teacher_public');
const xyz = require('./xyz');
const ctb = require('./ctb');
const boss = require('./boss');
const zyk = require('./zyk');
const mkp = require('./mkp');
const xkw = require('./xkw');
const xkw_public = require('./xkw/public');
const exampaper = require('./exampaper');
const kb = require('./kb');
const internal = require('./internal');
const edu_assistant_file = require('./edu_assistant_file');
const learning_analysis = require('./learning_analysis');
const school = require('./school');
const edu_plan = require('./edu_plan');
const team_prep = require('./edu_plan/team_prep');
const exam_v2 = require('./exam/v2');
const common = require('./common');
const textbook = require('./textbook');
const banner = require('./banner');
const resource_album = require('./resource_album');

const public_routers = {
    '/v1/xkw': xkw_public,
    '/v1/teacher': teacher_public,
    '/v1/common': common,
    '/v1/user_paper': user_paper_public,
    '/v1/question': question_public,
};

const internal_routers = {
    '/v1/internal': internal,
};

const routers = {
    '/healthcheck': health,
    '/v1/book': book,
    '/v1/question': question,
    '/v1/knowledge_tree': knowledge_tree,
    '/v1/basket': basket,
    '/v1/paper': paper,
    '/v1/region': region,
    '/v1/user_paper': user_paper,
    '/v1/exam': exam,
    '/v1/tw_specifications': tw_specifications,
    '/v1/prep': prep_resource,
    '/v1/prep/basket': prep_basket,
    '/v1/user/prep/catalog': user_prep_catalog,
    '/v1/user/prep/resource': user_prep_resource,
    // '/v1/school': school_zyk,
    '/v1/teacher': teacher,
    '/v1/xyz': xyz,
    '/v1/ctb': ctb,
    '/v1/boss': boss,
    '/v1/zyk': zyk,
    '/v1/mkp': mkp,
    '/v1/xkw': xkw,
    '/v1/exampaper': exampaper,
    '/v1/kb': kb,
    '/v1/edu_assistant_files': edu_assistant_file,
    '/v1/learning_analysis': learning_analysis,
    '/v1/school': school,
    '/v1/edu_plan': edu_plan,
    '/v1/team_prep': team_prep,
    '/v2/exam': exam_v2,
    '/v1/textbook': textbook,
    '/v1/banners': banner,
    '/v1/resource_album': resource_album,

};


function init(app) {
    // 开放接口
    _.each(public_routers, (action, path) => {
        app.use(path, action);
    });
    // 内部调用接口
    _.each(internal_routers, (action, path) => {
        app.use(path, action);
    });
    // 鉴权校验
    app.use(auth.verify);
    // 处理所有URL
    _.each(routers, (action, path) => {
        app.use(path, action);
    });

    // catch 404 and forward to
    // error handler
    app.use(function (req, res, next) {
        const err = new Error('Not Found');
        err.status = 404;
        next(err);
    });

    // catch 500 internal error handler
    app.use(function (err, req, res, next) {
        logger.error(err);
        res.status(err.status || 500);

        // const errMsg = {
        //     ip: req.ip,
        //     msg: 'IN uri error: ' + req.originalUrl,
        //     logLevel: 'error'
        // };
        //
        // if (err.status === 404) {
        //     errMsg.msgIn = 'Not Found';
        //     return res.json(utils.resJSON(404, 'Not Found', null, errMsg));
        // }
        // errMsg.msgIn = 'Capture Internal err!';
        // errMsg.errStack = err.stack || err;
        // return res.json(utils.resJSON(1000, '内部错误', null, errMsg));
        return res.json(response.error(1000, ''));
    });
}

module.exports = {
    init: init
};

