const kb = require('./kb');
const utilbox = require('./utilbox');
const hfsTeacherV3 = require('./hfs_teacher_v3');
const yuanpei = require('./yuanpei');
const algo = require('./algo');
const dtk = require('./dtk');
const tiku = require('./tiku');
const hfs = require('./hfs');
const rank = require('./rank');
const yj = require('./yj');
const file = require('./file');
const boss = require('./boss');
const xkw = require('./xkw');
const wordParser = require('./word_parser');
const kbSe = require('./kb_se');
const aiKb = require('./ai_kb');
const jiaoyan = require('./jiaoyan');
const mkp = require('./mkp');

module.exports = {
    kb,
    utilbox,
    hfsTeacherV3,
    yuanpei,
    algo,
    dtk,
    tiku,
    hfs,
    rank,
    yj,
    file,
    boss,
    xkw,
    wordParser,
    kbSe,
    aiKb,
    jiaoyan,
    mkp,
}
