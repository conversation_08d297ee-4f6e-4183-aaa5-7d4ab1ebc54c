const config = require('config')
const _ = require('lodash')
const db = require('../common/db')
const schema = require('../common/enums/schema')
const enums = require('../common/enums/enums')
const client = require('../common/client')
const path = require('path')
const axios = require('axios')
const crypto = require('crypto')
const { count } = require('console')
const ObjectId = require("mongodb").ObjectId;
// const db_url_test = 'mongodb://CTB:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/kb?replicaSet=ReplsetTest&readPreference=secondaryPreferred';
// const db_url = '***********************************************************************************************************************************';
// const kb_client = new MongoClient(process.env.NODE_ENV === 'production' ? db_url : db_url_test, config.get('db.options'));

(async () => {
    try {
        await db.init()
        console.log('db init')
        await main()
        console.log('data sync success')
    } catch (e) {
        console.log(e)
    }
})()

const userMap = {}

async function main () {
    // 所有不包含
    // 获取所有上传的数据
    let list = await db.collection(schema.user_paper).find({
        // user_id: '000000003748837837881344',
        valid: enums.BOOL.YES,
        source: enums.PaperSourceType.UPLOAD,
        "_remark_0610": "题库数据打通迁移",
        // source_id: {$exists: false}
    }).toArray()
    if (!_.size(list)) return
    // list = [list[0]];
    let index = 1, errorCount = 0
    for (const data of list) {
        console.log(`处理：${index}/ ${_.size(list)}, ${data._id.toString()}`)
        if (!data.source_id && data.source_url) {
            let teacher = userMap[data.user_id]
            if (!teacher) {
                try {
                    // 获取教师信息
                    teacher = await client.yj.getUserInfo(data.user_id)
                } catch (e) {
                    console.error(`获取教师信息异常：${data.user_id}`, e)
                    errorCount++
                    index++
                    continue
                }
                if (!teacher) {
                    index++
                    errorCount++
                    continue
                }
                userMap[data.user_id] = teacher
            }
            // 同步文件
            const { task_id } = await createFile(data, teacher)
            console.log('任务ID：', task_id)
            // if (task_id) {
            //     // console.log('任务ID：', task_id);
            //     // 同步任务
            //     // await db.collection(schema.user_paper).updateOne({_id: data._id}, {$set: {source_id: task_id}});
            // }
        }
        index++
    }
    console.log(`处理完成，错误次数：${errorCount}`)
    console.log(`处理完成，成功次数：${index - errorCount}`)
}

async function createFile (paper, teacher) {
    // 生成file
    const userId = teacher.userId.toString()
    const schoolId = teacher.schoolId
    const { branch_id, branch_user_id } = await createJzlUser(teacher)
    let hash, size
    try {
        const info = await calculateFileHash(paper.source_url)
        hash = info.hash
        size = info.size
    } catch (e) {
        console.error('检查文件是否存在异常：', e)
        return {}
    }
    const file = {
        period: paper.period,
        subject: paper.subject,
        name: `${paper.name}${getExtName(paper.source_url)}`,
        size: size,
        type: '',
        hash: hash,
        url: paper.source_url || '',
        suffix: getExtName(paper.source_url) || '',
        category: 'document',
        parent_id: '0', // 根目录
        is_folder: 0,
        operator: branch_user_id,
        creator: branch_user_id,
        pBranch: branch_id,
        school_id: schoolId,
        user_id: userId,
        source: 'upload',
        source_id: 'upload',
        download_times: 0,
        view_times: 0,
        deleted: enums.BOOL.NO,
        shared: enums.BOOL.NO,
        is_top: enums.BOOL.NO,
        createdAt: paper.ctime || new Date(),
        updatedAt: paper.utime || new Date(),
        '_remark_0610': '题库数据打通迁移',
        '__v': 0,
    }
    if (file.suffix === '.doc') {
        file.type = 'application/msword'
    } else {
        file.type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
    const file_insert = await db.jzl_collection('disk-file').insertOne(file)
    // file.id = file_insert.insertedId.toString();
    // 添加解析任务
    const task = {
        name: paper.name,
        task_type: 'word',
        disk_files: [file_insert.insertedId],
        period: paper.period,
        subject: paper.subject,
        grade: paper.subject,
        type: paper.type,
        from_year: paper.from_year,
        to_year: paper.to_year,
        status: paper.status,
        error: paper.error || '',
        school_id: schoolId,
        user_id: userId,
        tiku_paper_id: '',
        creator: branch_user_id,
        pBranch: branch_id,
        deleted: enums.BOOL.NO,
        createdAt: paper.ctime || new Date(),
        updatedAt: paper.utime || new Date(),
        '_remark_0610': '题库数据打通迁移',
        '__v': 0,
    }
    if (paper.status === enums.PaperStatus.EDIT || paper.status === enums.PaperStatus.DONE) {
        task.tiku_paper_id = paper._id.toString()
    }
    const task_insert = await db.jzl_collection(schema.parse_task).insertOne(task)
    // 更新file信息
    await db.jzl_collection('disk-file').updateOne({ _id: file_insert.insertedId }, { $set: { parse_task: task_insert.insertedId } })
    // 更新试卷信息
    await db.collection(schema.user_paper).updateOne({ _id: paper._id }, { $set: { source_id: task_insert.insertedId.toString() } })
    return { task_id: task_insert.insertedId.toString() }
}

function getExtName (name) {
    return path.extname(name).toLowerCase()
}

async function createJzlUser (teacher) {
    const collection_branch = 'users-permissions_branch'
    let branch = await db.jzl_collection(collection_branch).findOne({ yjSchoolId: teacher.schoolId })
    if (_.isEmpty(branch)) {
        branch = {
            periods: [],
            subjects: [],
            name: teacher.schoolName,
            shortName: teacher.schoolName,
            type: teacher.schoolId.toString(),
            yjSchoolId: teacher.schoolId,
            createdAt: new Date(),
            updatedAt: new Date(),
            '__v': 0,
        }
        const res = await db.jzl_collection(collection_branch).insertOne(branch)
        branch._id = res.insertedId
    }
    // 角色
    // const collection_role = 'users-permissions_role';
    // const allRoles = await db.jzl_collection(collection_role).find({}).toArray();
    // const currentRoleTypes = teacher ? teacher.roles.map(e => e.type) : [];
    // const allRoleTypes = ['teacher', ...currentRoleTypes];
    // const roles = allRoles.filter(role => allRoleTypes.includes(role.type));
    // 用户
    const collection_user = 'users-permissions_user'
    let branch_user = await db.jzl_collection(collection_user).findOne({ yjUserId: teacher.userId.toString() })
    if (_.isEmpty(branch_user)) {
        branch_user = {
            yjUserId: teacher.userId.toString(),
            username: teacher.userName,
            confirmed: true,
            blocked: false,
            loginCodeTryCount: 0,
            loginPasswordTryCount: 0,
            // 测试
            // role: new ObjectId('67cd46d19ceb680f6560e359'),
            // roles: [new ObjectId('67cd46d19ceb680f6560e359')],
            // 正式
            role: new ObjectId('67d8ce0f411e15e9e224ad69'),
            roles: [new ObjectId('67d8ce0f411e15e9e224ad69')],
            pBranch: branch._id,
            pBranches: [],
            thirdParties: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            '__v': 0,
        }
        const res = await db.jzl_collection(collection_user).insertOne(branch_user)
        branch_user._id = res.insertedId
    }
    return { branch_id: branch._id, branch_user_id: branch_user._id }
}


async function calculateFileHash (url) {
    try {
        // 创建哈希对象
        const hash = crypto.createHash('md5')
        // 获取文件流
        const response = await axios({
            method: 'get',
            url: url,
            responseType: 'stream'
        })
        let size = 0
        // 通过流式计算哈希
        response.data.on('data', (chunk) => {
            size += chunk.length
            hash.update(chunk)
        })

        // 返回 Promise，当流结束时解析哈希值
        return new Promise((resolve, reject) => {
            response.data.on('end', () => {
                const fileHash = hash.digest('hex')
                resolve({ hash: fileHash, size: size })
            })

            response.data.on('error', (err) => {
                reject(err)
            })
        })
    } catch (error) {
        console.error('Error calculating file hash:', error.message)
        throw error
    }
}

