const Joi = require('@hapi/joi');
const _ = require('lodash');
const model = require('../../models/learning_analysis');
const enums = require('../../../common/enums/enums');

module.exports = {
    knowledge,
    knowledgeQuestions,
    knowledgePaper
}


const JOI_KNOWLEDGE = Joi.object({
    grade: Joi.string().required(), //  学段
    period: Joi.string().required(), // 科目
    subject: Joi.string().required(), // 科目
    need_ques_num: Joi.boolean().optional(),
    type: Joi.string().required(),
    provinces: Joi.array().items(Joi.object({
        name: Joi.string().required()
    })).required(), // 试卷ID
    to_year: Joi.string().required()
});

async function knowledge(req, res) {
    const params = await JOI_KNOWLEDGE.validateAsync(req.body);
    return await model.knowledge(params);
}

const JOI_KNOWLEDGE_QUESTIONS = Joi.object({
    ids: Joi.string().required(), //  知识点ID
    refer_category: Joi.string().required(), // 试卷类型
});

async function knowledgeQuestions(req, res) {
    const params = await JOI_KNOWLEDGE_QUESTIONS.validateAsync(req.query);
    return await model.knowledgeQuestions(params);
}

const JOI_KNOWLEDGE_PAPER = Joi.object({
    grade: Joi.string().required(), //  学段
    period: Joi.string().required(), // 科目
    subject: Joi.string().required(), // 科目
    need_ques_num: Joi.boolean().optional(),
    type: Joi.string().required(),
    provinces: Joi.array().items(Joi.object({
        name: Joi.string().required()
    })).required(), // 试卷ID
    to_year: Joi.string().required(),
    knowledges: Joi.array().items(Joi.object()).required().min(1).max(5)
});

async function knowledgePaper(req, res) {
    const params = await JOI_KNOWLEDGE_PAPER.validateAsync(req.body);
    params.user_id = req.user.id;
    return await model.knowledgePaper(params);
}
