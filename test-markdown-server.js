const express = require('express');
const markdownMiddleware = require('./common/middlewares/markdown');

const app = express();
const port = 3000;

// 支持通过域名+路径访问/public下的markdown文件，删除/public前缀，直接使用下级路径
app.use(markdownMiddleware);

// 支持通过域名+路径访问/public下的其他静态文件，删除/public前缀，直接使用下级路径
app.use(express.static('public', {
    maxAge: '1d',
    etag: true,
    lastModified: true
}));

// 简单的健康检查端点
app.get('/health', (req, res) => {
    res.json({ status: 'ok', message: 'Markdown server is running' });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: 'Not Found' });
});

app.listen(port, () => {
    console.log(`Markdown test server running at http://localhost:${port}`);
    console.log('Test URLs:');
    console.log(`- http://localhost:${port}/README.md`);
    console.log(`- http://localhost:${port}/docs/guide.md`);
    console.log(`- http://localhost:${port}/test.html`);
    console.log(`- http://localhost:${port}/assets/style.css`);
});
